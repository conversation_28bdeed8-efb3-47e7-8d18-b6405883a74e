{"root": true, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react-refresh"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"varsIgnorePattern": "^_", "argsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "react-refresh/only-export-components": "warn"}, "overrides": [{"files": ["**/__tests__/**/*.[jt]s?(x)", "**/?(*.)+(spec|test).[jt]s?(x)"], "rules": {"@typescript-eslint/no-explicit-any": "off"}}, {"files": ["src/utils/**/*.[jt]s?(x)", "src/store/**/*.[jt]s?(x)"], "rules": {"@typescript-eslint/no-explicit-any": "off"}}]}