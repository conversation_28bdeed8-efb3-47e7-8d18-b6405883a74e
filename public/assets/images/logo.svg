<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- Background Circle -->
    <circle cx="256" cy="256" r="248" fill="#1E293B" stroke="#0EA5E9" stroke-width="16"/>
    
    <!-- Outer Progress Ring -->
    <circle cx="256" cy="256" r="200" stroke="#0EA5E9" stroke-width="4" stroke-dasharray="1256.64" stroke-dashoffset="0" opacity="0.2"/>
    <circle cx="256" cy="256" r="200" stroke="#0EA5E9" stroke-width="4" stroke-dasharray="1256.64" stroke-dashoffset="942.48" opacity="0.8">
        <animate attributeName="stroke-dashoffset"
                 dur="4s"
                 repeatCount="indefinite"
                 values="1256.64;942.48;1256.64"/>
    </circle>

    <!-- Neural Network Nodes -->
    <g>
        <!-- Central Node -->
        <circle cx="256" cy="256" r="24" fill="#0EA5E9">
            <animate attributeName="r"
                     dur="2s"
                     repeatCount="indefinite"
                     values="24;28;24"/>
        </circle>

        <!-- Satellite Nodes -->
        <g>
            <circle cx="176" cy="256" r="16" fill="#38BDF8" opacity="0.8">
                <animate attributeName="opacity"
                         dur="3s"
                         repeatCount="indefinite"
                         values="0.8;0.4;0.8"/>
            </circle>
            <circle cx="336" cy="256" r="16" fill="#38BDF8" opacity="0.8">
                <animate attributeName="opacity"
                         dur="3s"
                         repeatCount="indefinite"
                         values="0.8;0.4;0.8"
                         begin="1s"/>
            </circle>
            <circle cx="256" cy="176" r="16" fill="#38BDF8" opacity="0.8">
                <animate attributeName="opacity"
                         dur="3s"
                         repeatCount="indefinite"
                         values="0.8;0.4;0.8"
                         begin="0.5s"/>
            </circle>
            <circle cx="256" cy="336" r="16" fill="#38BDF8" opacity="0.8">
                <animate attributeName="opacity"
                         dur="3s"
                         repeatCount="indefinite"
                         values="0.8;0.4;0.8"
                         begin="1.5s"/>
            </circle>
        </g>

        <!-- Connection Lines -->
        <g stroke="#7DD3FC" stroke-width="3" opacity="0.6">
            <line x1="195" y1="256" x2="237" y2="256">
                <animate attributeName="opacity"
                         dur="2s"
                         repeatCount="indefinite"
                         values="0.6;0.3;0.6"/>
            </line>
            <line x1="275" y1="256" x2="317" y2="256">
                <animate attributeName="opacity"
                         dur="2s"
                         repeatCount="indefinite"
                         values="0.6;0.3;0.6"
                         begin="0.5s"/>
            </line>
            <line x1="256" y1="195" x2="256" y2="237">
                <animate attributeName="opacity"
                         dur="2s"
                         repeatCount="indefinite"
                         values="0.6;0.3;0.6"
                         begin="1s"/>
            </line>
            <line x1="256" y1="275" x2="256" y2="317">
                <animate attributeName="opacity"
                         dur="2s"
                         repeatCount="indefinite"
                         values="0.6;0.3;0.6"
                         begin="1.5s"/>
            </line>
        </g>
    </g>

    <!-- Pulse Effect -->
    <circle cx="256" cy="256" r="48" stroke="#0EA5E9" stroke-width="2" fill="none" opacity="0.4">
        <animate attributeName="r"
                 dur="3s"
                 repeatCount="indefinite"
                 values="48;120;48"/>
        <animate attributeName="opacity"
                 dur="3s"
                 repeatCount="indefinite"
                 values="0.4;0;0.4"/>
    </circle>
</svg> 