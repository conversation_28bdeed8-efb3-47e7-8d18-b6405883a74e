#!/bin/bash

# Exit on error
set -e

# Load environment variables
if [ -f .env.local ]; then
    export $(cat .env.local | grep -v '^#' | xargs)
fi

# Ensure we have the API key
if [ -z "$VITE_BUGSNAG_API_KEY" ]; then
    echo "Error: VITE_BUGSNAG_API_KEY is not set in .env.local"
    exit 1
fi

# Check if running in CI
if [ -z "$CI" ]; then
    # Get current version before bump
    CURRENT_VERSION=$(node -p "require('./package.json').version")
    
    # Only bump version if not in CI
    echo "Updating version..."
    npm version patch
    
    # Get new version after bump
    NEW_VERSION=$(node -p "require('./package.json').version")
    echo "Version updated: $CURRENT_VERSION -> $NEW_VERSION"
else
    # In CI, validate that version has been incremented
    if git fetch origin main && git checkout origin/main -- package.json; then
        MAIN_VERSION=$(node -p "require('./package.json').version")
        git checkout -- package.json
        CURRENT_VERSION=$(node -p "require('./package.json').version")
        
        if [ "$CURRENT_VERSION" = "$MAIN_VERSION" ]; then
            echo "Error: Version must be incremented before deploying to production"
            echo "Current version: $CURRENT_VERSION"
            echo "Main branch version: $MAIN_VERSION"
            exit 1
        fi
        
        echo "Version increment validated:"
        echo "Main: $MAIN_VERSION -> Current: $CURRENT_VERSION"
    else
        echo "Warning: Could not validate version increment"
    fi
fi

# Get the version
VERSION=$(node -p "require('./package.json').version")
echo "Building version: $VERSION"

# Export version for Vite
export VITE_APP_VERSION=$VERSION

# Clean previous build
echo "Cleaning previous build..."
rm -rf dist

# Build the project
echo "Building project..."
npm run build

# Set the base URL for source maps
if [ -z "$VITE_APP_URL" ]; then
    # Default to local development URL if not set
    BASE_URL="http://localhost:5173"
else
    BASE_URL="$VITE_APP_URL"
fi

# Upload source maps to Bugsnag
echo "Uploading source maps to Bugsnag..."
echo "Using base URL: $BASE_URL"
npx bugsnag-source-maps upload-browser \
    --api-key "$VITE_BUGSNAG_API_KEY" \
    --app-version "$VERSION" \
    --directory dist \
    --base-url "$BASE_URL" \
    --overwrite

# Optional: Clean up source maps after upload
echo "Cleaning up source maps..."
find dist -name "*.map" -delete

echo "Deployment build completed successfully!"
echo "Version: $VERSION"

# Only show git push reminder if not in CI
if [ -z "$CI" ]; then
    echo "Don't forget to push the version bump:"
    echo "git push && git push --tags"
fi 