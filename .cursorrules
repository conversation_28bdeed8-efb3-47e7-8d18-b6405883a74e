## Code Style and Structure
- Write concise, clean, minimal and readable code
- Structure repository files as follows:
```
src/
├── app/                    # App-wide setup and configuration
│   ├── providers/         # All context providers
│   ├── routes/           # Route definitions and configurations
│   └── layout/           # Root layout components
├── assets/               # Static assets (images, fonts, etc.)
├── components/
│   ├── ui/              # Reusable UI components (shadcn)
│   └── common/          # Shared components used across features
├── features/            # Feature-based modules
│   ├── auth/           
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── store/
│   │   ├── types/
│   │   └── __tests__/
│   ├── teams/
│   ├── tasks/
│   └── calendar/
├── hooks/               # Global custom hooks
├── lib/                # Third-party library configurations
│   ├── supabase/
│   └── utils/
├── services/           # API and external service integrations
│   ├── api/
│   └── storage/
├── store/              # Global state management (Zustand stores)
├── styles/             # Global styles and Tailwind configurations
├── types/              # Global TypeScript types/interfaces
└── utils/              # Shared utility functions
```

## Tech Stack
- React
- TypeScript
- Tailwind CSS
- Shadcn UI
- Supabase (Database & Authentication)
- Zustand (State Management)

## Naming Conventions
- Use lowercase with dashes for directories (e.g., components/form-wizard)
- Favor named exports for components and utilities
- Use PascalCase for component files (e.g., VisaForm.tsx)
- Use camelCase for utility files (e.g., formValidator.ts)

## State Management
- Use React Context for global state when needed
- Use Zustand for complex state management
- Follow Supabase real-time subscription patterns
- Implement proper database state sync
- Cache frequently accessed data

## Zustand Store Patterns
- Use slice pattern for complex stores (see docs/kanban-store-README.md)
- Create selectors for all state access to ensure proper memoization
- Implement cleanup functions for all stores to prevent memory leaks
- Follow the store structure pattern:
  - State: Define state types and initial state
  - Actions: Define actions that modify state
  - Selectors: Define selectors for accessing state
  - Initialization: Provide a clear initialization pattern
  - Cleanup: Always include cleanup functions

## Component Patterns
- Implement proper component separation based on feature boundaries
- Use composition over inheritance
- Create UI components that are pure and reusable
- Use React.memo() for performance-critical components
- Implement useCallback for function props
- Use useMemo for expensive computations

## Documentation Standards
- Document all complex components with JSDoc comments
- Document all hooks with usage examples
- Keep the docs/ folder organized by feature or architecture concern
- Update documentation when implementing significant changes
- Document database schema changes in migration files

## Syntax and Formatting
- Use "function" keyword for pure functions
- Avoid unnecessary curly braces in conditionals
- Use declarative JSX
- Implement proper TypeScript discriminated unions for message types

## UI and Styling
- Use Shadcn UI and Radix for components
- Implement Tailwind CSS for styling
- Follow responsive design principles with mobile-first approach
- Use CSS variables for theming

## Error Handling
- Implement proper error boundaries
- Log errors appropriately for debugging
- Provide user-friendly error messages
- Handle network failures gracefully

## Testing
- Write unit tests for utilities and components
- Implement E2E tests for critical flows
- Test database migrations
- Test RLS policies
- Implement integration tests for Supabase operations

## Security
- Sanitize user inputs
- Handle sensitive data properly
- Implement proper CORS handling
- Implement Supabase RLS policies
- Handle authentication tokens securely
- Implement proper database access controls
- Use prepared statements for database queries
- Never expose database credentials in client code

## Database Patterns
- Use Supabase Row Level Security (RLS)
- Implement proper database migrations
- Follow naming convention for database tables (snake_case)
- Document policy changes in migration files

## Implementation References
- Kanban Board: Refer to docs/kanban-store-README.md for implementation details
- Feature Structure: Follow the pattern in src/features/auth for new features
- Hook Patterns: Follow the patterns in src/hooks for new custom hooks
- Database Access: Follow the patterns in src/services/api for database access
