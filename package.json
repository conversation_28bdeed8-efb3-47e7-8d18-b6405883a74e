{"name": "cadence-ai", "private": true, "version": "0.6.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "deploy": "./deploy.sh"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "dependencies": {"@bugsnag/browser-performance": "^2.11.0", "@bugsnag/js": "^8.2.0", "@bugsnag/plugin-react": "^8.2.0", "@bugsnag/react-router-performance": "^2.7.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^4.1.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.48.1", "cmdk": "^0.2.1", "date-fns": "^3.3.1", "dotenv": "^16.3.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-day-picker": "^9.5.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-markdown": "^9.1.0", "react-router-dom": "^6.22.3", "remark-gfm": "^4.0.1", "zod": "^3.24.2", "zustand": "^4.5.6"}, "devDependencies": {"@bugsnag/source-maps": "^2.3.3", "@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^1.6.1", "@vitest/ui": "^1.6.1", "autoprefixer": "^10.4.18", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^24.1.3", "postcss": "^8.4.35", "tailwind-merge": "^3.0.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.6.1"}}