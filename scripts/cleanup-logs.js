#!/usr/bin/env node

/**
 * This script cleans up diagnostic logging statements from the codebase
 * It removes console.log statements that were added for debugging during development
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const TARGET_DIR = 'src/features/teams';
const LOG_PATTERNS = [
    /console\.log\([^)]*\);?/g,
    /console\.debug\([^)]*\);?/g,
    // Keep error and warning logs
    // /console\.error\([^)]*\);?/g,
    // /console\.warn\([^)]*\);?/g,
];
const IGNORE_PATTERNS = [
    /\/node_modules\//,
    /\/__tests__\//,
    /\.test\./,
    /\.spec\./,
];
const FILE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Find all files in the target directory
function findFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);

    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
            // Skip directories that match ignore patterns
            if (!IGNORE_PATTERNS.some(pattern => pattern.test(filePath))) {
                findFiles(filePath, fileList);
            }
        } else {
            // Only include files with the specified extensions
            if (FILE_EXTENSIONS.includes(path.extname(filePath))) {
                // Skip files that match ignore patterns
                if (!IGNORE_PATTERNS.some(pattern => pattern.test(filePath))) {
                    fileList.push(filePath);
                }
            }
        }
    });

    return fileList;
}

// Clean up logs in a file
function cleanupLogsInFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let logCount = 0;

    // Apply each pattern
    LOG_PATTERNS.forEach(pattern => {
        const matches = content.match(pattern);
        if (matches) {
            logCount += matches.length;
        }
        content = content.replace(pattern, '');
    });

    // Remove empty lines created by removing logs
    content = content.replace(/^\s*[\r\n]/gm, '');

    // Only write to the file if changes were made
    if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`Cleaned up ${logCount} log statements in ${filePath}`);
        return logCount;
    }

    return 0;
}

// Main function
function main() {
    console.log(`Cleaning up diagnostic logs in ${TARGET_DIR}...`);

    // Find all files
    const files = findFiles(TARGET_DIR);
    console.log(`Found ${files.length} files to process`);

    // Clean up logs in each file
    let totalLogCount = 0;
    files.forEach(file => {
        totalLogCount += cleanupLogsInFile(file);
    });

    console.log(`Cleaned up ${totalLogCount} log statements in total`);
}

// Run the script
main(); 