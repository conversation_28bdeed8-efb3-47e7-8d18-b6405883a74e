-- Activity report that generates team activity data in JSON format
-- Usage: SELECT * FROM team_activity_report('63a4252a-6373-4047-bf2b-c8a585a7fcda', '2025-03-01', '2025-03-15');

CREATE OR REPLACE FUNCTION team_activity_report(
  input_team_id UUID,
  start_date TIMESTAMP DEFAULT (date_trunc('week', now()) + interval '-10 days')::timestamp,
  end_date TIMESTAMP DEFAULT date_trunc('week', now())::timestamp
)
RETURNS JSON AS $$
-- Common date range for all queries
WITH date_range AS (
  SELECT 
    end_date as week_end,
    start_date as week_start
)

-- 1. Completed Tasks (Cards moved to Done/Completed column)
, completed_tasks AS (
  SELECT 
    kc.id as card_id,
    kc.title as card_title,
    kc.updated_at as completed_at,
    p.full_name as completed_by,
    kc.archived_at as archived_at
  FROM kanban_cards kc
  JOIN kanban_columns kcol ON kc.column_id = kcol.id
  LEFT JOIN profiles p ON kc.assignee_id = p.id
  CROSS JOIN date_range dr
  WHERE 
    kc.team_id = input_team_id
    AND kc.updated_at BETWEEN dr.week_start AND dr.week_end
    AND kc.archived_at IS Not NULL
    AND kc.deleted_at IS NULL
)

-- 2. Card Movements (Between columns)
, card_movements AS (
  SELECT 
    kc.id as card_id,
    kc.title as card_title,
    kc.updated_at as moved_at,
    p.full_name as moved_by,
    kcol.title as current_column
  FROM kanban_cards kc
  JOIN kanban_columns kcol ON kc.column_id = kcol.id
  LEFT JOIN profiles p ON kc.assignee_id = p.id
  CROSS JOIN date_range dr
  WHERE 
    kc.team_id = input_team_id
    AND kc.updated_at BETWEEN dr.week_start AND dr.week_end
    AND kc.deleted_at IS NULL
)

-- 3. New Cards Created
, new_cards AS (
  SELECT 
    kc.id as card_id,
    kc.title as card_title,
    kc.created_at,
    p.full_name as created_by,
    kcol.title as column_title
  FROM kanban_cards kc
  JOIN kanban_columns kcol ON kc.column_id = kcol.id
  LEFT JOIN profiles p ON kc.assignee_id = p.id
  CROSS JOIN date_range dr
  WHERE 
    kc.team_id = input_team_id
    AND kc.created_at BETWEEN dr.week_start AND dr.week_end
    AND kc.deleted_at IS NULL
)

-- 4. Subtask Updates
, subtask_updates AS (
  SELECT 
    ks.id as subtask_id,
    ks.title as subtask_title,
    kc.title as card_title,
    ks.updated_at,
    ks.is_completed,
    p.full_name as updated_by
  FROM kanban_subtasks ks
  JOIN kanban_cards kc ON ks.card_id = kc.id
  LEFT JOIN profiles p ON ks.assignee_id = p.id
  CROSS JOIN date_range dr
  WHERE 
    kc.team_id = input_team_id
    AND ks.updated_at BETWEEN dr.week_start AND dr.week_end
)

-- 5. Comments/Discussions
, comments AS (
  SELECT 
    kcom.id as comment_id,
    kc.title as card_title,
    kcom.content,
    kcom.created_at,
    kcom.is_system,
    p.full_name as commented_by
  FROM kanban_comments kcom
  JOIN kanban_cards kc ON kcom.card_id = kc.id
  LEFT JOIN profiles p ON kcom.user_id = p.id
  CROSS JOIN date_range dr
  WHERE 
    kc.team_id = input_team_id
    AND kcom.created_at BETWEEN dr.week_start AND dr.week_end
    AND kcom.deleted_at IS NULL
    AND kcom.is_system = false
)

-- Final Combined Query
SELECT 
  json_build_object(
    'period', json_build_object(
      'start', (SELECT week_start FROM date_range),
      'end', (SELECT week_end FROM date_range)
    ),
    'completed_tasks', (
      SELECT COALESCE(json_agg(completed_tasks.*), '[]'::json)
      FROM completed_tasks
    ),
    'card_movements', (
      SELECT COALESCE(json_agg(card_movements.*), '[]'::json)
      FROM card_movements
    ),
    'new_cards', (
      SELECT COALESCE(json_agg(new_cards.*), '[]'::json)
      FROM new_cards
    ),
    'subtask_updates', (
      SELECT COALESCE(json_agg(subtask_updates.*), '[]'::json)
      FROM subtask_updates
    ),
    'comments', (
      SELECT COALESCE(json_agg(comments.*), '[]'::json)
      FROM comments
    )
  ) as team_activity_data;
$$ LANGUAGE SQL;

-- Example usage:
-- Default date range (10 days before current week start to current week start):
-- SELECT * FROM team_activity_report('63a4252a-6373-4047-bf2b-c8a585a7fcda');
-- 
-- Custom date range:
-- SELECT * FROM team_activity_report('63a4252a-6373-4047-bf2b-c8a585a7fcda', '2025-03-01', '2025-03-15'); 