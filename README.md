# cadence.ai

[![CI/CD](https://github.com/[your-username]/[your-repo]/actions/workflows/ci.yml/badge.svg)](https://github.com/[your-username]/[your-repo]/actions/workflows/ci.yml)

Your OKR cadence powered by AI.

## Development

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Run tests
npm run test

# Run tests with coverage
npm run test:coverage
```

## Deployment

### Overview
The project uses two deployment methods:
1. Local deployment script for development
2. CI/CD pipeline for production deployments

### Local Development Deployment
Run the local deployment script:
```bash
npm run deploy
```

This will:
- Automatically bump patch version in package.json
- Build the project
- Upload source maps to Bugsnag
- Remind you to push changes

Required environment variables in `.env.local`:
```bash
VITE_BUGSNAG_API_KEY=your_bugsnag_api_key
VITE_APP_URL=http://localhost:5173  # or your local development URL
```

### Production Deployment (CI/CD)
Production deployments are handled automatically when changes are merged to the main branch.

**Process:**
1. Create a feature branch:
   ```bash
   git checkout -b feature/your-feature
   ```

2. Make your changes and bump version:
   ```bash
   # For patch updates (0.0.1 -> 0.0.2)
   npm version patch

   # For minor updates (0.1.0 -> 0.2.0)
   npm version minor

   # For major updates (1.0.0 -> 2.0.0)
   npm version major
   ```

3. Push changes and create a PR to main:
   ```bash
   git push origin feature/your-feature
   ```

4. When merged to main, the CI/CD pipeline will:
   - Validate version increment
   - Run tests and linting
   - Build the project
   - Upload source maps to Bugsnag
   - Create a git tag for the version
   - Deploy to Vercel production

Required GitHub secrets:
```bash
VITE_BUGSNAG_API_KEY=your_bugsnag_api_key
VITE_APP_URL=https://your-production-url.com
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_key
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_org_id
VERCEL_PROJECT_ID=your_project_id
```

### Error Tracking
The project uses Bugsnag for error tracking and monitoring:
- Automatic error capturing
- Source map integration for readable stack traces
- Version tracking for each deployment
- Performance monitoring
- React error boundary integration

### Version Management
- Local deployments automatically bump patch version
- Production deployments require manual version bumps
- All versions are tagged in git
- Each deployment is tracked with its version in Bugsnag
- Version validation ensures incremental updates

### Environment Variables
Create the following files for environment variables:

**.env.local** (for local development):
```bash
VITE_BUGSNAG_API_KEY=your_bugsnag_api_key
VITE_APP_URL=http://localhost:5173
```

**.env.production** (for production builds):
```bash
VITE_BUGSNAG_API_KEY=your_bugsnag_api_key
VITE_APP_URL=https://your-production-url.com
```

### Deployment Commands

| Command | Description |
|---------|-------------|
| `npm run deploy` | Local deployment script |
| `npm version patch` | Bump patch version |
| `npm version minor` | Bump minor version |
| `npm version major` | Bump major version |
| `npm run build` | Build the project |
| `npm run test` | Run tests |
| `npm run lint` | Run linting |

### Troubleshooting

1. **Source Map Upload Errors**
   - Ensure `VITE_APP_URL` is set correctly
   - Verify Bugsnag API key is valid
   - Check if source maps are generated in build

2. **Version Conflicts**
   - Pull latest changes from main
   - Resolve any merge conflicts in package.json
   - Ensure version is incremented before PR

3. **Deployment Failures**
   - Check GitHub Actions logs
   - Verify all required secrets are set
   - Ensure tests and linting pass locally

## Project Structure

```
src/
├── app/                    # App-wide setup and configuration
│   ├── providers/         # All context providers
│   ├── routes/           # Route definitions and configurations
│   └── layout/           # Root layout components
├── assets/               # Static assets (images, fonts, etc.)
├── components/
│   ├── ui/              # Reusable UI components (shadcn)
│   └── common/          # Shared components used across features
├── features/            # Feature-based modules
│   ├── auth/           
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── store/
│   │   ├── types/
│   │   └── __tests__/
│   ├── teams/
│   ├── tasks/
│   └── calendar/
├── hooks/               # Global custom hooks
├── lib/                # Third-party library configurations
│   ├── supabase/
│   └── utils/
├── services/           # API and external service integrations
│   ├── api/
│   └── storage/
├── store/              # Global state management (Zustand stores)
├── styles/             # Global styles and Tailwind configurations
├── types/              # Global TypeScript types/interfaces
└── utils/              # Shared utility functions
```

### Directory Structure Details

- **app/**: Contains core application setup, providers, and layouts
- **components/**: Houses all reusable components
  - **ui/**: Shadcn UI components and their customizations
  - **common/**: Shared components used across multiple features
- **features/**: Feature-based modules following a modular architecture
  - Each feature (auth, teams, tasks, calendar) contains its own:
    - Components
    - Hooks
    - Store (local state)
    - Types
    - Tests
- **lib/**: Configuration and setup for third-party libraries
- **services/**: External service integrations and API calls
- **store/**: Global state management using Zustand
- **styles/**: Global styles and Tailwind CSS configurations
- **types/**: TypeScript types shared across the application
- **utils/**: Shared utility functions and helpers 