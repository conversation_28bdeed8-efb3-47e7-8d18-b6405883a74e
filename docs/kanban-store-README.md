# Kanban Store

## Overview
The Kanban store is a state management solution for the Kanban board functionality in the application. It uses Zustand for state management and is structured into slices for better maintainability and scalability.

## Store Structure
The Kanban store is divided into the following slices:

### 1. Board Slice
Manages columns and board layout.

**State:**
- `columns`: Array of columns in the board
- `loading`: <PERSON>olean indicating if the board is loading
- `error`: Error message if board loading fails

**Actions:**
- `fetchBoard`: Fetches the board layout for a team
- `addColumn`: Adds a new column to the board
- `updateColumn`: Updates a column's title
- `deleteColumn`: Deletes a column
- `reorderColumns`: Reorders columns in the board

### 2. Card Slice
Manages cards and subtasks.

**State:**
- `cards`: Array of cards in the board
- `subtasks`: Array of subtasks for all cards
- `cardPages`: Record of card pages for each column
- `hasMoreCards`: Record indicating if a column has more cards
- `loadingCards`: Record indicating if cards are loading for a column
- `columnData`: Map of column data including cards, loading state, etc.

**Actions:**
- `fetchColumnCards`: Fetches cards for a column
- `addCard`: Adds a new card to a column
- `updateCard`: Updates a card's details
- `deleteCard`: Deletes a card
- `moveCard`: Moves a card between columns
- `addSubtask`: Adds a subtask to a card
- `updateSubtask`: Updates a subtask's details
- `deleteSubtask`: Deletes a subtask
- `reorderSubtasks`: Reorders subtasks in a card

### 3. Comment Slice
Manages card comments.

**State:**
- `comments`: Array of comments for all cards
- `commentPages`: Record of comment pages for each card
- `hasMoreComments`: Record indicating if a card has more comments
- `loadingComments`: Record indicating if comments are loading for a card

**Actions:**
- `fetchComments`: Fetches comments for a card
- `addComment`: Adds a comment to a card
- `updateComment`: Updates a comment's content
- `deleteComment`: Deletes a comment

### 4. User Slice
Manages users and assignments.

**State:**
- `users`: Array of users who can be assigned to cards and subtasks

**Actions:**
- `updateUsers`: Fetches and updates the list of users

## Usage

### Initializing the Store
```typescript
import { useKanbanStore } from '@/features/teams/store/teamKanbanStore';

// In your component
const initializeStore = useKanbanStore(state => state.initializeStore);

// Initialize the store for a team
useEffect(() => {
  initializeStore('team-123');
}, [initializeStore]);
```

### Accessing State
```typescript
import { useKanbanStore, kanbanSelectors } from '@/features/teams/store/teamKanbanStore';

// Using selectors (recommended)
const columns = useKanbanStore(kanbanSelectors.selectColumns);
const card = useKanbanStore(kanbanSelectors.selectCardById('card-123'));
const columnCards = useKanbanStore(kanbanSelectors.selectColumnCards('column-123'));
const users = useKanbanStore(kanbanSelectors.selectUsers);

// Direct state access
const allCards = useKanbanStore(state => state.cards);
const subtasks = useKanbanStore(state => state.subtasks);
```

### Performing Actions
```typescript
import { useKanbanStore } from '@/features/teams/store/teamKanbanStore';

// Get actions
const addCard = useKanbanStore(state => state.addCard);
const updateCard = useKanbanStore(state => state.updateCard);
const moveCard = useKanbanStore(state => state.moveCard);
const addSubtask = useKanbanStore(state => state.addSubtask);

// Use actions
const handleAddCard = async () => {
  await addCard('column-123', 'team-123', 'New Task');
};

const handleUpdateCard = async () => {
  await updateCard('card-123', { title: 'Updated Title', description: 'New description' });
};

const handleMoveCard = async () => {
  await moveCard('card-123', 'column-456', 2);
};

const handleAddSubtask = async () => {
  await addSubtask('card-123', 'New Subtask');
};
```

### Cleanup
```typescript
import { useKanbanStore } from '@/features/teams/store/teamKanbanStore';

// Get cleanup function
const cleanup = useKanbanStore(state => state.cleanup);

// Clean up when unmounting
useEffect(() => {
  return () => {
    cleanup();
  };
}, [cleanup]);
```

## Best Practices

1. **Use Selectors**: Always use selectors when accessing state to ensure proper memoization and prevent unnecessary re-renders.

2. **Handle Errors**: Always handle errors when performing actions to provide a good user experience.

3. **Cleanup**: Always clean up the store when unmounting the Kanban board to prevent memory leaks.

4. **Optimistic Updates**: Consider implementing optimistic updates for better user experience, especially for actions like moving cards.

5. **Batch Updates**: When performing multiple actions, consider batching them to reduce the number of re-renders.

## Types
The Kanban store uses the following types:

- `TeamKanbanColumn`: Represents a column in the board
- `TeamKanbanCard`: Represents a card in the board
- `TeamKanbanSubtask`: Represents a subtask in a card
- `TeamKanbanComment`: Represents a comment on a card
- `User`: Represents a user who can be assigned to cards and subtasks

These types are defined in `src/features/teams/types/kanban.ts`. 