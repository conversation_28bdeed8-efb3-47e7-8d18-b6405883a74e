# Cross-Slice Communication Fix Plan

## Overview
This document outlines the plan to fix cross-slice communication issues in the Kanban board store after migrating to a sliced architecture. Proper communication between slices is essential for maintaining data consistency and ensuring that operations that affect multiple slices work correctly.

## Current Issues

### 1. Card Moving Functionality
- When moving a card between columns, both the board slice and card slice need to be updated
- Currently, the `moveCard` function in the card slice updates the card's column_id and order_index, but doesn't properly update the board slice's column data

### 2. Subtask Management
- Subtask operations (add, update, delete) need to update both the card slice and potentially the user slice (for assignments)
- The current implementation doesn't ensure that all relevant slices are updated consistently

### 3. Comment Management
- Comment operations (add, update, delete) need to update both the card slice and the comment slice
- The current implementation may not properly reflect comment changes across all slices

## Implementation Plan

### Phase 1: Fix Card Moving Functionality

#### 1.1 Analyze Current Implementation
- Review the current `moveCard` function in the card slice
- Identify how column data is stored in the board slice
- Understand how card data is stored in the card slice

#### 1.2 Implement Cross-Slice Updates
- Modify the `moveCard` function to update both slices:
  ```typescript
  moveCard: async (cardId: string, columnId: string, newIndex: number) => {
    try {
      const state = get() as CardState & BoardState;
      
      // Get card from card slice
      const card = state.cards.find(c => c.id === cardId);
      if (!card) throw new Error('Card not found');
      
      // Get target column cards from board slice
      const targetCards = state.cardPages[columnId] || [];
      const orderIndex = calculateNewOrderIndex(targetCards, -1, newIndex);
      
      // Update in database
      await cardApi.moveCard(cardId, columnId, orderIndex.toString());
      
      // Create updated card
      const updatedCard: TeamKanbanCard = {
        ...card,
        column_id: columnId,
        order_index: orderIndex
      };
      
      // Update card slice
      set((state: CardState) => ({
        cards: state.cards.map(c => c.id === cardId ? updatedCard : c)
      }));
      
      // Update board slice
      set((state: BoardState) => {
        // Remove card from source column
        const sourceColumnId = card.column_id;
        const updatedCardPages = { ...state.cardPages };
        
        if (updatedCardPages[sourceColumnId]) {
          updatedCardPages[sourceColumnId] = updatedCardPages[sourceColumnId]
            .filter(c => c.id !== cardId);
        }
        
        // Add card to target column
        if (!updatedCardPages[columnId]) {
          updatedCardPages[columnId] = [];
        }
        
        // Insert at the correct position
        const targetCards = [...updatedCardPages[columnId]];
        targetCards.splice(newIndex, 0, updatedCard);
        updatedCardPages[columnId] = targetCards;
        
        return { cardPages: updatedCardPages };
      });
      
      return updatedCard;
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to move card' });
      throw error;
    }
  }
  ```

#### 1.3 Test Card Moving
- Test moving cards between columns
- Verify that both the card slice and board slice are updated correctly
- Ensure the UI reflects the changes properly

### Phase 2: Fix Subtask Management

#### 2.1 Analyze Current Implementation
- Review the current subtask functions (add, update, delete) in the card slice
- Understand how subtasks are stored in the card slice
- Identify how user assignments are handled in the user slice

#### 2.2 Implement Cross-Slice Updates for Subtasks
- Modify the subtask functions to update all relevant slices:
  ```typescript
  addSubtask: async (cardId: string, title: string) => {
    try {
      const state = get() as CardState;
      const card = state.cards.find(c => c.id === cardId);
      if (!card) throw new Error('Card not found');
      
      // Get last order index
      const cardSubtasks = card.subtasks || [];
      const lastOrderIndex = cardSubtasks.length > 0
        ? Math.max(...cardSubtasks.map(s => s.order_index))
        : 0;
      
      // Add subtask in database
      const newSubtask = await cardApi.addSubtask(cardId, title, lastOrderIndex + 1024);
      
      // Update card slice
      set((state: CardState) => {
        // Update cards array
        const updatedCards = state.cards.map(c => {
          if (c.id === cardId) {
            return {
              ...c,
              subtasks: [...(c.subtasks || []), newSubtask]
            };
          }
          return c;
        });
        
        // Update subtasks array
        const updatedSubtasks = [...state.subtasks, newSubtask];
        
        return {
          cards: updatedCards,
          subtasks: updatedSubtasks
        };
      });
      
      return newSubtask;
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to add subtask' });
      throw error;
    }
  }
  ```

#### 2.3 Test Subtask Management
- Test adding, updating, and deleting subtasks
- Verify that all slices are updated correctly
- Ensure the UI reflects the changes properly

### Phase 3: Fix Comment Management

#### 3.1 Analyze Current Implementation
- Review the current comment functions (add, update, delete) in the comment slice
- Understand how comments are stored in the comment slice and card slice

#### 3.2 Implement Cross-Slice Updates for Comments
- Modify the comment functions to update all relevant slices:
  ```typescript
  addComment: async (cardId: string, content: string) => {
    try {
      const state = get() as CommentState & UserState;
      const user = state.currentUser;
      if (!user) throw new Error('User not found');
      
      // Add comment in database
      const newComment = await commentApi.addComment(cardId, content, user.id);
      
      // Update comment slice
      set((state: CommentState) => {
        // Update comments array
        const updatedComments = [...state.comments, newComment];
        
        // Update comment pages
        const commentPages = { ...state.commentPages };
        if (!commentPages[cardId]) {
          commentPages[cardId] = [];
        }
        commentPages[cardId] = [newComment, ...commentPages[cardId]];
        
        return {
          comments: updatedComments,
          commentPages
        };
      });
      
      // Update card slice to reflect new comment count
      set((state: CardState) => {
        const updatedCards = state.cards.map(c => {
          if (c.id === cardId) {
            return {
              ...c,
              comments: [...(c.comments || []), newComment]
            };
          }
          return c;
        });
        
        return { cards: updatedCards };
      });
      
      return newComment;
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to add comment' });
      throw error;
    }
  }
  ```

#### 3.3 Test Comment Management
- Test adding, updating, and deleting comments
- Verify that all slices are updated correctly
- Ensure the UI reflects the changes properly

## Testing Strategy

### 1. Unit Tests
- Update unit tests for each slice to verify that cross-slice operations work correctly
- Test edge cases and error handling

### 2. Integration Tests
- Test the entire Kanban board functionality
- Verify that all operations that affect multiple slices work correctly
- Test with realistic data and user interactions

### 3. UI Testing
- Test the UI components to ensure they reflect the state changes correctly
- Verify that drag-and-drop, infinite scrolling, and other UI features work with the updated store

## Timeline
- **Phase 1 (Fix Card Moving)**: 1 day
- **Phase 2 (Fix Subtask Management)**: 1 day
- **Phase 3 (Fix Comment Management)**: 1 day
- **Testing and Refinement**: 1-2 days

Total estimated time: 4-5 days

## Conclusion
Fixing cross-slice communication is essential for ensuring the Kanban board functions correctly with the new sliced store architecture. By implementing the changes outlined in this plan, we will ensure data consistency across slices and improve the overall reliability of the Kanban board functionality. 