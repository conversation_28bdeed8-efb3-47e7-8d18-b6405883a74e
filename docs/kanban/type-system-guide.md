# TeamKanban Type System Guide

## Overview

The TeamKanban project has multiple type definitions for the same conceptual entities, which can lead to confusion and type errors. This guide explains the different type systems, their relationships, and how to use the type adapters to work with them safely.

## Type Systems

There are three main type systems in the project:

1. **Index Types** (`/types/index.ts`): The original type definitions used throughout most of the codebase.
2. **Kanban Types** (`/types/kanban.ts`): A parallel set of types with some structural differences.
3. **Unified Types** (`/types/unified.ts`): A newer type system designed to bridge the gap between the other two.

### Key Differences

The main differences between the type systems are:

#### TeamKanbanCard

| Feature | Index Type | Kanban Type |
|---------|------------|-------------|
| Subtasks | Uses `TeamKanbanSubtask[]` | Uses inline subtask objects with `assignee` property |
| Comments | References by ID | Includes inline comment objects |

#### TeamKanbanSubtask

| Feature | Index Type | Kanban Type |
|---------|------------|-------------|
| Assignee | Has both `assignee_id` and `assignee` object | Only has `assignee_id` |
| Due Date | Optional string | Optional string |

## Type Adapters

To bridge these differences, we've created type adapter utilities in `src/features/teams/utils/typeAdapters.ts`:

### Type Guards

```typescript
// Check if a card is of the kanban type
isKanbanCardType(card: any): card is KanbanCardType

// Check if a card is of the index type
isIndexCardType(card: any): card is IndexCardType

// Check if a subtask is of the kanban type
isKanbanSubtaskType(subtask: any): subtask is KanbanSubtaskType

// Check if a subtask is of the index type
isIndexSubtaskType(subtask: any): subtask is IndexSubtaskType
```

### Conversion Functions

```typescript
// Convert any card type to KanbanCardType
asKanbanCard(card: KanbanCardType | IndexCardType | UnifiedKanbanCard): KanbanCardType

// Convert any subtask to KanbanSubtaskType
asKanbanSubtask(subtask: KanbanSubtaskType | IndexSubtaskType | UnifiedKanbanSubtask): KanbanSubtaskType

// Convert any subtask to IndexSubtaskType
asIndexSubtask(subtask: KanbanSubtaskType | IndexSubtaskType | UnifiedKanbanSubtask): IndexSubtaskType
```

### Normalization Functions

```typescript
// Normalize a subtask to ensure consistent structure
normalizeSubtask(subtask: any): KanbanSubtaskType

// Normalize all subtasks in a card
normalizeCardSubtasks<T>(card: T): T
```

## Usage Examples

### Working with Cards

```typescript
import { asKanbanCard, normalizeCardSubtasks } from '@/features/teams/utils/typeAdapters';

// When receiving a card from an unknown source
function processCard(card: any) {
  // Convert to a known type
  const kanbanCard = asKanbanCard(card);
  
  // Now you can safely work with it
  console.log(kanbanCard.title);
  
  // If you need to access subtasks
  kanbanCard.subtasks.forEach(subtask => {
    console.log(subtask.title, subtask.is_completed);
  });
}

// When filtering cards
const filteredCards = cards.map(card => {
  // Ensure consistent subtask structure
  return normalizeCardSubtasks(card);
});
```

### Working with Subtasks

```typescript
import { asKanbanSubtask, asIndexSubtask } from '@/features/teams/utils/typeAdapters';

// When you need to work with a subtask in the kanban format
function updateSubtaskCompletion(subtask: any, isCompleted: boolean) {
  const kanbanSubtask = asKanbanSubtask(subtask);
  
  // Now update it
  return {
    ...kanbanSubtask,
    is_completed: isCompleted
  };
}

// When you need the assignee object
function getSubtaskAssignee(subtask: any) {
  const indexSubtask = asIndexSubtask(subtask);
  
  // Now you can access the assignee object
  return indexSubtask.assignee;
}
```

## Best Practices

1. **Always Use Type Guards**: When working with data from external sources, use type guards to check the type.

2. **Convert Early**: Convert to your preferred type as early as possible in your component or function.

3. **Be Consistent**: Choose one type system for each component and stick with it.

4. **Document Type Usage**: Add comments indicating which type system you're using in a component.

5. **Use Type Adapters**: Always use the type adapter functions instead of manual type assertions.

6. **Normalize Before Filtering**: Always normalize card subtasks before applying filters.

7. **Handle Edge Cases**: Be prepared for missing or null properties, especially in subtasks.

## Common Pitfalls

1. **Assuming Assignee Structure**: The `assignee` property exists in IndexSubtaskType but not in KanbanSubtaskType.

2. **Direct Type Assertions**: Avoid using `as` directly; use the adapter functions instead.

3. **Forgetting to Normalize**: Always normalize subtasks before filtering or sorting.

4. **Mixing Type Systems**: Don't mix different type systems within the same component.

5. **Ignoring Type Guards**: Always check the type before assuming its structure.

## Migration Path

We're gradually moving toward the Unified Type system. To help with this transition:

1. **Use Type Adapters**: Always use the adapter functions when working with cards and subtasks.

2. **Update Components**: When modifying a component, update it to use the Unified Type system.

3. **Add Type Documentation**: Document which type system each component uses.

4. **Test Type Conversions**: Add tests for type conversions in critical components.

## Troubleshooting

### Common Errors

1. **"Property 'assignee' is missing in type 'TeamKanbanSubtask'"**
   - You're trying to access the `assignee` property on a KanbanSubtaskType.
   - Solution: Use `asIndexSubtask` to convert it first.

2. **"Type 'TeamKanbanSubtask[]' is not assignable to type '{ ... }[]'"**
   - You're trying to assign a TeamKanbanSubtask array to a property expecting inline subtasks.
   - Solution: Use `normalizeCardSubtasks` to convert the subtasks.

3. **"Property 'assignee_id' does not exist on type '...'"**
   - You're trying to access `assignee_id` on a type that doesn't have it.
   - Solution: Use `asKanbanSubtask` to ensure the property exists.

### Debugging Tips

1. **Check the Source**: Determine where your data is coming from to know which type system it uses.

2. **Use Type Guards**: Use the type guard functions to check the actual type at runtime.

3. **Log the Structure**: Log the object structure to see what properties are available.

4. **Add Type Assertions**: Add explicit type assertions with comments to clarify your intent.

## Conclusion

The multiple type systems in the TeamKanban project can be challenging to work with, but the type adapters provide a safe way to convert between them. By following the best practices in this guide, you can avoid type errors and ensure your components work correctly with all types of data. 