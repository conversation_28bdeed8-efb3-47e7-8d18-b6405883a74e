# Health Metrics Components

This document provides an overview of the Health Metrics feature components, their usage, and how they interact with each other.

## Component Structure

The Health Metrics feature is built using a modular component architecture with clear separation of concerns:

```
health-metrics/
├── components/
│   ├── TeamHealthMetricsSummary/
│   │   ├── index.tsx                  # Main container component
│   │   ├── HealthMetricsHeader.tsx    # Header with expand/collapse functionality
│   │   ├── HealthMetricsCarousel.tsx  # Carousel for displaying multiple metrics
│   │   ├── HealthMetricsEmptyState.tsx # Empty state when no metrics exist
│   │   ├── HealthMetricsLoadingState.tsx # Loading state while fetching metrics
│   │   └── HealthMetricsActionButtons.tsx # Action buttons for metrics
│   ├── HealthMetricCard/
│   │   ├── index.tsx                  # Main card component
│   │   ├── HealthMetricCardHeader.tsx # Card header with title and actions
│   │   ├── HealthMetricValue.tsx      # Displays metric value and status
│   │   ├── HealthMetricChart.tsx      # Container for the micro chart
│   │   ├── HealthMetricThresholds.tsx # Displays threshold indicators
│   │   └── styles.css                 # Styles for the card components
│   └── HealthMetricMicroChart/
│       ├── index.tsx                  # Main chart component
│       ├── BarChartRenderer.tsx       # Renders the bar chart visualization
│       ├── LoadingState.tsx           # Loading state for the chart
│       └── EmptyState.tsx             # Empty state when no data exists
├── store/
│   └── healthMetricsStore.ts          # Zustand store for health metrics data
├── hooks/
│   └── useHealthMetricsSelectors.ts   # Custom hooks for accessing store data
├── utils/
│   └── trendUtils.ts                  # Utilities for trend calculations
├── api/
│   └── healthMetricsApi.ts            # API functions for health metrics
└── types/
    └── healthMetrics.types.ts         # TypeScript types for health metrics
```

## Main Components

### TeamHealthMetricsSummary

The main container component that displays a summary of health metrics for a team.

#### Usage

```tsx
import { TeamHealthMetricsSummary } from '@/features/health-metrics/components/TeamHealthMetricsSummary';

// Inside your component
return (
  <div>
    <h1>Team Dashboard</h1>
    <TeamHealthMetricsSummary teamId="team-123" />
  </div>
);
```

#### Props

| Prop | Type | Description |
|------|------|-------------|
| teamId | string | The ID of the team to display health metrics for |

### HealthMetricCard

A card component that displays a single health metric with its value, status, and trend chart.

#### Usage

```tsx
import { HealthMetricCard } from '@/features/health-metrics/components/HealthMetricCard';

// Inside your component
return (
  <div>
    <h2>Critical Metrics</h2>
    <HealthMetricCard 
      metric={metric}
      onCheckIn={(metricId) => handleCheckIn(metricId)}
      onSettings={(metricId) => handleSettings(metricId)}
    />
  </div>
);
```

#### Props

| Prop | Type | Description |
|------|------|-------------|
| metric | TeamHealthMetricWithValues | The health metric data to display |
| onCheckIn | (metricId: string) => void | Callback when the check-in button is clicked |
| onSettings | (metricId: string) => void | Callback when the settings button is clicked |

### HealthMetricMicroChart

A small chart component that displays historical data for a health metric.

#### Usage

```tsx
import { HealthMetricMicroChart } from '@/features/health-metrics/components/HealthMetricMicroChart';

// Inside your component
return (
  <div style={{ height: '100px', width: '200px' }}>
    <HealthMetricMicroChart 
      metricId="metric-123"
      limit={10} // Optional, defaults to 10
    />
  </div>
);
```

#### Props

| Prop | Type | Description |
|------|------|-------------|
| metricId | string | The ID of the metric to display history for |
| limit | number | (Optional) The number of historical data points to display |

## Component Interaction

The components are designed to work together in a hierarchical structure:

1. `TeamHealthMetricsSummary` is the top-level container that fetches and manages team metrics data
2. It renders a list of `HealthMetricCard` components inside a carousel
3. Each `HealthMetricCard` displays a single metric and contains a `HealthMetricMicroChart`
4. The `HealthMetricMicroChart` fetches and displays historical data for a specific metric

## Styling

The components use a combination of Tailwind CSS and custom CSS classes defined in `HealthMetricCard/styles.css`. The styling follows these principles:

- Responsive design that works on all screen sizes
- Consistent spacing and alignment
- Clear visual hierarchy
- Accessible color contrast
- Interactive elements with proper hover/focus states

## Accessibility

The components are designed with accessibility in mind:

- Proper ARIA attributes for interactive elements
- Keyboard navigation support
- Screen reader-friendly content
- Sufficient color contrast
- Focus management for interactive elements

## Performance Considerations

To ensure optimal performance, the components implement:

- Memoization with React.memo and useMemo
- Optimized re-renders with useCallback
- Efficient state management with Zustand
- Lazy loading for data fetching
- Proper dependency arrays for hooks
