# Kanban Store Structure Documentation

## Overview

The Kanban board store has been migrated from a monolithic structure to a sliced architecture for better maintainability, performance, and type safety. This document provides a comprehensive guide to the new store structure, how to use it, and best practices.

## Store Architecture

### Sliced Structure

The Kanban store is now divided into multiple slices, each responsible for a specific domain:

1. **Board Slice**: Manages columns and board-level state
2. **Card Slice**: Manages cards and their properties
3. **Comment Slice**: Manages comments on cards
4. **User Slice**: Manages user information and assignments

Each slice has its own:
- State
- Actions
- Selectors

### File Structure

```
src/features/teams/store/
├── teamKanbanStore.ts        # Main store file that combines all slices
├── slices/
│   ├── board/                # Board slice
│   │   ├── index.ts          # Exports the slice
│   │   ├── state.ts          # Initial state
│   │   ├── actions.ts        # Actions
│   │   └── types.ts          # TypeScript types
│   ├── card/                 # Card slice
│   │   ├── index.ts
│   │   ├── state.ts
│   │   ├── actions.ts
│   │   └── types.ts
│   ├── comment/              # Comment slice
│   │   ├── index.ts
│   │   ├── state.ts
│   │   ├── actions.ts
│   │   └── types.ts
│   └── user/                 # User slice
│       ├── index.ts
│       ├── state.ts
│       ├── actions.ts
│       └── types.ts
└── utils/                    # Utility functions
    └── typeAdapters.ts       # Type adapters for handling type inconsistencies
```

## Using the Store

### Importing the Store

```typescript
import { useKanbanStore, kanbanSelectors } from '@/features/teams/store/teamKanbanStore';
```

### Accessing State

Use selectors to access state efficiently:

```typescript
// Using a predefined selector
const columns = useKanbanStore(kanbanSelectors.selectColumns);

// Using a custom selector
const columnData = useKanbanStore(state => state.columnData.get(columnId));
```

### Performing Actions

Access actions directly from the store:

```typescript
// Get the action from the store
const moveCard = useKanbanStore(state => state.moveCard);

// Use the action
await moveCard(cardId, targetColumnId, targetIndex);
```

### Cross-Slice Operations

Some operations affect multiple slices. These are handled internally by the store:

```typescript
// This will update both the card slice and board slice
await moveCard(cardId, targetColumnId, targetIndex);

// This will update both the card slice and subtask arrays
await addSubtask(cardId, title);

// This will update both the comment slice and card slice
await addComment(cardId, content);
```

## Type Handling

### Type Inconsistencies

There are two different `TeamKanbanCard` types in the codebase:
- `TeamKanbanCard` from `@/features/teams/types/kanban`
- `TeamKanbanCard` from `@/features/teams/types`

To handle these inconsistencies, use the type adapters:

```typescript
import { asKanbanCard } from '@/features/teams/utils/typeAdapters';

// Convert a card to the kanban type
const kanbanCard = asKanbanCard(card);
```

### Type Safety

Always use proper typing when working with the store:

```typescript
// Specify the return type
const fetchCards = async (): Promise<TeamKanbanCardType[]> => {
  // ...
};

// Use type assertions when necessary
const card = cards.find(c => c.id === cardId) as TeamKanbanCardType;
```

## Best Practices

### Use Selectors

Always use selectors to access state for better performance:

```typescript
// Good: Uses a selector
const columns = useKanbanStore(kanbanSelectors.selectColumns);

// Bad: Accesses state directly
const columns = useKanbanStore(state => state.columns);
```

### Minimize Re-renders

Use shallow comparison for complex objects:

```typescript
import { shallow } from 'zustand/shallow';

// Only re-render when these specific values change
const { cards, loading } = useKanbanStore(
  state => ({
    cards: state.cards,
    loading: state.loading
  }),
  shallow
);
```

### Handle Errors

Always handle errors when performing actions:

```typescript
try {
  await moveCard(cardId, targetColumnId, targetIndex);
} catch (error) {
  console.error('Failed to move card:', error);
  // Show error message to user
}
```

### Clean Up

Perform cleanup when components unmount:

```typescript
useEffect(() => {
  // Initialize
  fetchBoard(teamId);
  
  return () => {
    // Clean up
    resetBoard();
  };
}, [teamId, fetchBoard, resetBoard]);
```

## Common Operations

### Fetching Data

```typescript
// Fetch the board
await fetchBoard(teamId);

// Fetch cards for a column
await fetchColumnCards(columnId, page, limit, includeArchived);

// Fetch comments for a card
await fetchComments(cardId, page, limit);
```

### Modifying Data

```typescript
// Add a new card
await addCard(columnId, teamId, title, description);

// Update a card
await updateCard(cardId, { title, description, priority });

// Move a card
await moveCard(cardId, targetColumnId, targetIndex);

// Add a subtask
await addSubtask(cardId, title);

// Add a comment
await addComment(cardId, content);
```

## Troubleshooting

### Type Errors

If you encounter type errors when working with cards or subtasks, use the type adapters:

```typescript
import { asKanbanCard } from '@/features/teams/utils/typeAdapters';

// Convert a card to the kanban type
const kanbanCard = asKanbanCard(card);
```

### State Not Updating

If the UI doesn't reflect state changes:

1. Check that you're using selectors correctly
2. Verify that the action is updating the correct slice
3. Ensure that cross-slice operations are updating all relevant slices

### Performance Issues

If you experience performance issues:

1. Use selectors to access only the state you need
2. Use `shallow` comparison for complex objects
3. Memoize expensive calculations with `useMemo`
4. Use `useCallback` for functions passed as props

## Conclusion

The sliced architecture of the Kanban store provides better maintainability, performance, and type safety. By following the guidelines in this document, you can effectively work with the store and avoid common pitfalls. 