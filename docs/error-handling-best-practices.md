# Error Handling Best Practices

This document outlines the standardized approach to error handling across the TeamKanban application.

## Core Error Handling Infrastructure

### 1. Error Handler Utility

Always use the centralized `errorHandler` utility for capturing and reporting errors:

```typescript
import { errorHandler, ErrorType, ErrorSeverity } from '@/utils/errorHandler';

try {
  // Potentially error-prone code
} catch (error) {
  errorHandler.handleApiError(error, {
    component: 'ComponentName',
    action: 'actionName',
    context: { /* relevant context */ }
  });
  throw new Error('User-friendly error message');
}
```

### 2. Error Types and Severity

Use the appropriate error type and severity for better categorization:

```typescript
// API errors
errorHandler.handleApiError(error, context);

// Auth errors
errorHandler.handleAuthError(error, context);

// Custom error with type and severity
errorHandler.captureError(error, ErrorType.VALIDATION, {
  ...context,
  severity: ErrorSeverity.HIGH
});
```

## Component Error Boundaries

### 1. Wrapping Components with Error Boundaries

Always wrap key components with error boundaries to isolate failures:

```typescript
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { errorHandler, ErrorType } from '@/utils/errorHandler';

const ComponentWithErrorBoundary = (props) => {
  return (
    <ErrorBoundary
      componentName="ComponentName"
      onError={(error, info) => {
        errorHandler.captureError(error, ErrorType.UNKNOWN, {
          component: 'ComponentName',
          action: 'render',
          context: { 
            // Include relevant props as context
            ...relevantProps,
            componentStack: info.componentStack 
          }
        });
      }}
      fallbackRender={({ error, resetErrorBoundary }) => (
        // Custom fallback UI with retry mechanism
        <FallbackComponent
          error={error}
          onRetry={resetErrorBoundary}
        />
      )}
    >
      <WrappedComponent {...props} />
    </ErrorBoundary>
  );
};
```

### 2. HOC Pattern for Error Boundaries

For reusable components, use the HOC pattern:

```typescript
import { withErrorBoundary } from '@/components/ui/error-boundary';

// Export the original component for testing
export { MyComponent };

// Export a wrapped version as the default export
export default withErrorBoundary(MyComponent, {
  componentName: 'MyComponent',
  onError: (error, info) => {
    errorHandler.captureError(error, ErrorType.UNKNOWN, {
      component: 'MyComponent',
      context: { componentStack: info.componentStack }
    });
  }
});
```

## API Error Handling

### 1. Consistent Try-Catch Pattern

Use a consistent try-catch pattern in all API calls:

```typescript
async function apiMethod(params) {
  try {
    const { data, error } = await supabase.from('table').select('*');
    
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    errorHandler.handleApiError(error, {
      component: 'apiModule',
      action: 'apiMethod',
      context: { params }
    });
    throw new Error('User-friendly error message');
  }
}
```

### 2. Error Recovery Mechanisms

Implement appropriate recovery mechanisms when possible:

```typescript
// Retry mechanism
const MAX_RETRIES = 3;
async function apiMethodWithRetry(params) {
  let retries = 0;
  while (retries < MAX_RETRIES) {
    try {
      // API call
      return data;
    } catch (error) {
      retries++;
      if (retries >= MAX_RETRIES) {
        errorHandler.handleApiError(error, {
          component: 'apiModule',
          action: 'apiMethod',
          context: { params, retries }
        });
        throw new Error('Failed after multiple attempts');
      }
      // Exponential backoff
      await new Promise(r => setTimeout(r, 2 ** retries * 100));
    }
  }
}
```

## User-Facing Error Messages

Always provide user-friendly error messages:

```typescript
// Getting a user-friendly message
const userMessage = errorHandler.getUserFriendlyMessage(
  error, 
  ErrorType.API,
  { component: 'ComponentName' }
);

// Display to user in UI
<ErrorAlert message={userMessage} />
```

## Testing Error Handling

Include tests for error cases in your test suite:

```typescript
it('should handle API errors gracefully', async () => {
  // Mock API to throw an error
  jest.spyOn(apiModule, 'apiMethod').mockRejectedValue(new Error('API Error'));
  
  // Verify error is handled properly
  await expect(functionThatCallsApi()).rejects.toThrow('User-friendly error message');
});
```

## Critical Components Requiring Error Boundaries

Always add error boundaries to these critical components:

1. **UI Layout Components**: 
   - Columns, Cards, Modals

2. **Data-Heavy Components**:
   - Lists, Tables, Data visualizations

3. **User Input Components**:
   - Forms, Rich text editors

4. **Interactive Elements**:
   - Drag and drop operations
   - Complex user interactions

5. **Asynchronous Components**:
   - Components that fetch/process data

## Benefits of Our Error Handling Approach

1. **Improved Stability**: Individual component failures don't cascade
2. **Enhanced User Experience**: Users can continue working even when parts of the UI fail
3. **Better Debugging**: Detailed error context is captured for each failure
4. **Visual Consistency**: Error states maintain layout and visual structure
5. **Error Recovery**: Retry mechanisms where appropriate 