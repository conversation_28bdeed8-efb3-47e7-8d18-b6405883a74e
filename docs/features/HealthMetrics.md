# Health Metrics Feature

This document provides an overview of the Health Metrics feature, its purpose, and how it fits into the OKR framework.

## Overview

Health Metrics are a key part of the OKR (Objectives and Key Results) framework. They track critical metrics with green/yellow/red status ranges to maintain existing business state while teams focus on new functionality.

## Purpose

Health metrics serve several important purposes:

1. **Maintain Stability**: Track ongoing stability without being tied to OKR targets
2. **Provide Visibility**: Give teams visibility into the health of their systems and processes
3. **Early Warning**: Identify potential issues before they become critical
4. **Focus Attention**: Help teams focus on areas that need attention
5. **Measure Progress**: Track improvements over time

## Key Concepts

### Status Ranges

Health metrics use a traffic light system to indicate status:

- **Green**: The metric is within the healthy range
- **Yellow**: The metric is in a warning state and needs attention
- **Red**: The metric is in a critical state and requires immediate action

### Thresholds

Each health metric has configurable thresholds that determine its status:

- **Green Threshold**: The value above/below which the metric is considered healthy
- **Yellow Threshold**: The value above/below which the metric is in a warning state
- **Red Threshold**: The value above/below which the metric is in a critical state

### Trends

Health metrics track trends over time to show whether a metric is:

- **Improving**: The metric is moving in a positive direction
- **Declining**: The metric is moving in a negative direction
- **Stable**: The metric is not changing significantly

## Feature Components

The Health Metrics feature consists of several key components:

1. **Team Health Metrics Summary**: A collapsible component that shows an overview of a team's health metrics
2. **Health Metric Card**: A card that displays a single health metric with its value, status, and trend
3. **Health Metric Micro Chart**: A small chart that shows the historical values of a metric
4. **Health Metrics Detail Page**: A page that shows detailed information about all health metrics for a team

## User Flows

### Viewing Health Metrics

1. User navigates to a team's dashboard
2. The Team Health Metrics Summary component shows an overview of the team's health metrics
3. User can expand the component to see all metrics or click "View All" to go to the detail page
4. On the detail page, user can see all metrics with their current values, statuses, and trends

### Creating a Health Metric

1. User clicks the "New" button in the Health Metrics component
2. User is taken to the Create Health Metric form
3. User enters the metric name, description, thresholds, and unit
4. User submits the form to create the new metric
5. The new metric appears in the Health Metrics component

### Checking In a Health Metric

1. User clicks the "Check-in" button on a health metric card
2. User is taken to the Check-in form
3. User enters the current value of the metric and optional notes
4. User submits the form to record the check-in
5. The metric is updated with the new value and status

## Implementation Details

### Data Model

The Health Metrics feature uses the following data model:

```typescript
// Health metric definition
interface TeamHealthMetric {
  id: string;
  team_id: string;
  name: string;
  description: string | null;
  green_threshold: number;
  yellow_threshold: number;
  red_threshold: number;
  unit: string | null;
  is_active: boolean;
  is_archived: boolean;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  soe: string | null; // Standard Operating Procedure
}

// Health metric value (check-in)
interface TeamHealthMetricValue {
  id: string;
  team_health_metric_id: string;
  value: number;
  status: 'green' | 'yellow' | 'red';
  notes: string | null;
  reported_at: string;
  reported_by: string | null;
}

// Combined type for display
interface TeamHealthMetricWithValues extends TeamHealthMetric {
  values?: TeamHealthMetricValue[];
  latest_value?: TeamHealthMetricValue;
}
```

### API Endpoints

The Health Metrics feature interacts with the following API endpoints:

- `GET /team-health-metrics?team_id={teamId}`: Get all health metrics for a team
- `GET /team-health-metric-values?team_health_metric_id={metricId}`: Get historical values for a metric
- `POST /team-health-metrics`: Create a new health metric
- `PUT /team-health-metrics/{id}`: Update an existing health metric
- `POST /team-health-metric-values`: Record a new check-in for a metric

## Best Practices

When working with Health Metrics, follow these best practices:

1. **Clear Thresholds**: Set clear, objective thresholds that are not stretch goals
2. **Regular Check-ins**: Check in metrics regularly to maintain accurate data
3. **Limited Metrics**: Keep the number of health metrics to 5-7 per team
4. **Actionable Metrics**: Choose metrics that teams can take action on
5. **Review Regularly**: Review health metrics regularly in team meetings
6. **Update Thresholds**: Adjust thresholds as needed based on changing business requirements

## Related Documentation

- [Health Metrics Components](../components/HealthMetrics.md)
- [Health Metrics State Management](../state-management/HealthMetricsStore.md)
- [OKR Framework Overview](./OKRFramework.md)
