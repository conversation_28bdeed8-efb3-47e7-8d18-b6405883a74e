# Health Metrics State Management

This document explains the state management approach used for the Health Metrics feature.

## Overview

The Health Metrics feature uses [Zustand](https://github.com/pmndrs/zustand) for state management. Zustand is a small, fast, and scalable state management solution that uses hooks and provides a simple API.

## Store Structure

The Health Metrics store is defined in `src/features/health-metrics/store/healthMetricsStore.ts` and follows this structure:

```typescript
interface HealthMetricsState {
  // Data
  teamMetrics: Record<string, TeamHealthMetricWithValues[]>;
  teamLoading: Record<string, boolean>;
  error: string | null;
  filters: HealthMetricFilters;
  
  // Actions
  fetchMetrics: (teamId: string) => Promise<void>;
  getTeamMetrics: (teamId: string) => TeamHealthMetricWithValues[];
  createMetric: (params: CreateHealthMetricParams) => Promise<void>;
  updateMetric: (params: UpdateHealthMetricParams) => Promise<void>;
  checkInMetric: (params: CheckInHealthMetricParams) => Promise<void>;
  setFilters: (filters: HealthMetricFilters) => void;
  clearError: () => void;
}
```

## Store Implementation

The store is implemented using Zustand's `create` function with middleware for devtools and persistence:

```typescript
export const useHealthMetricsStore = create<HealthMetricsState>(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        teamMetrics: {},
        teamLoading: {},
        error: null,
        filters: { is_active: true, is_archived: false },
        
        // Actions
        fetchMetrics: async (teamId) => {
          // Implementation...
        },
        // Other actions...
      }),
      {
        name: 'health-metrics-storage',
        storage: createJSONStorage(() => sessionStorage),
        partialize: (state) => ({
          filters: state.filters,
        }),
      }
    )
  )
);
```

## Custom Selectors

To optimize performance and prevent unnecessary re-renders, we use custom selector hooks defined in `src/features/health-metrics/hooks/useHealthMetricsSelectors.ts`:

```typescript
// Get team metrics with memoization
export const useTeamMetrics = (teamId: string) => {
  const { metrics, isLoading } = useHealthMetricsStore(
    (state) => ({
      metrics: state.teamMetrics[teamId] || [],
      isLoading: state.teamLoading[teamId] || false
    }),
    shallow
  );

  return { metrics, isLoading };
};

// Get overall health status with memoization
export const useOverallHealthStatus = (teamId: string) => {
  const { metrics } = useTeamMetrics(teamId);
  
  const overallStatus = useMemo(() => {
    // Implementation...
  }, [metrics]);
  
  return overallStatus;
};
```

## Data Flow

The data flow in the Health Metrics feature follows these steps:

1. Components call store actions (e.g., `fetchMetrics`) to load or update data
2. Actions make API calls using functions from `healthMetricsApi.ts`
3. When API calls complete, the store state is updated
4. Components access the updated state using custom selector hooks
5. Components re-render with the new data

## Optimizations

Several optimizations are implemented to ensure efficient state management:

1. **Memoization**: Custom selector hooks use memoization to prevent unnecessary re-renders
2. **Shallow Comparison**: The `shallow` function from Zustand is used to compare objects by their properties
3. **Selective Persistence**: Only specific parts of the state (filters) are persisted to storage
4. **Normalized State**: Team metrics are stored in a normalized structure using team IDs as keys
5. **Loading States**: Each team has its own loading state to prevent global loading indicators

## Usage Examples

### Fetching Team Metrics

```tsx
import { useEffect } from 'react';
import { useHealthMetricsStore } from '@/features/health-metrics/store/healthMetricsStore';
import { useTeamMetrics } from '@/features/health-metrics/hooks/useHealthMetricsSelectors';

const TeamMetrics = ({ teamId }) => {
  const { fetchMetrics, setFilters } = useHealthMetricsStore();
  const { metrics, isLoading } = useTeamMetrics(teamId);
  
  useEffect(() => {
    setFilters({ is_active: true, is_archived: false, team_id: teamId });
    fetchMetrics(teamId);
  }, [teamId, fetchMetrics, setFilters]);
  
  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      {metrics.map(metric => (
        <div key={metric.id}>{metric.name}</div>
      ))}
    </div>
  );
};
```

### Creating a New Metric

```tsx
import { useState } from 'react';
import { useHealthMetricsStore } from '@/features/health-metrics/store/healthMetricsStore';

const CreateMetricForm = ({ teamId, onSuccess }) => {
  const [name, setName] = useState('');
  const [greenThreshold, setGreenThreshold] = useState(0);
  const [yellowThreshold, setYellowThreshold] = useState(0);
  const [redThreshold, setRedThreshold] = useState(0);
  
  const { createMetric } = useHealthMetricsStore();
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    await createMetric({
      team_id: teamId,
      name,
      green_threshold: greenThreshold,
      yellow_threshold: yellowThreshold,
      red_threshold: redThreshold
    });
    
    onSuccess();
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      <button type="submit">Create Metric</button>
    </form>
  );
};
```

## Best Practices

When working with the Health Metrics state management, follow these best practices:

1. **Use Custom Selectors**: Always use the custom selector hooks instead of accessing the store directly
2. **Memoize Callbacks**: Use `useCallback` for functions that are passed to child components
3. **Optimize Dependencies**: Ensure effect dependencies are properly specified to prevent unnecessary re-renders
4. **Handle Loading States**: Always check and handle loading states to provide a good user experience
5. **Error Handling**: Check for and display errors from the store
6. **Cleanup**: Implement cleanup functions in useEffect hooks when necessary
