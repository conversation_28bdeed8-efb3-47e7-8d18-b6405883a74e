# Kanban Store Fix Plan

## Overview
This document outlines the plan to fix issues with the Kanban board functionality after migrating from a monolithic store to a sliced store structure.

## Current Issues
1. Components are still using the old monolithic store structure instead of the new sliced structure
2. Type inconsistencies between old and new store structures
3. Cross-slice communication issues affecting operations like moving cards and updating data
4. Component-specific issues with drag-and-drop and infinite scrolling

## Implementation Plan

### Phase 1: Store Structure Migration ✅
- [x] Create separate slices for board, card, comment, and user functionality
- [x] Implement proper selectors for accessing state
- [x] Add proper type definitions for the new store structure

### Phase 2: Fix Type Inconsistencies ✅
- [x] Add proper return types to async functions
- [x] Fix type for `priorityColors`
- [x] Fix comment type inconsistencies
- [x] Add helper functions for Boolean checks
- [x] Fix Boolean indexing issues
- [x] Fix implicit 'any' types in drag-and-drop functions
- [x] Fix type inconsistencies in `TeamKanbanColumn` component

### Phase 3: Fix Cross-Slice Communication ✅
- [x] Fix card moving functionality between columns
  - [x] Update `moveCard` function to update both card and board slices
  - [x] Handle type issues with cross-slice updates
- [x] Ensure proper subtask management across slices
  - [x] Update `addSubtask` function to update both card and subtask arrays
  - [x] Update `updateSubtask` function to update both card and subtask arrays
  - [x] Update `deleteSubtask` function to update both card and subtask arrays
  - [x] Add support for user slice updates when subtask assignments change
- [x] Fix comment management across slices
  - [x] Update `addComment` function to update both comment and card slices
  - [x] Update `updateComment` function to update both comment and card slices
  - [x] Update `deleteComment` function to update both comment and card slices
- [x] Fix initialization process to ensure users are loaded before the board

### Phase 4: Component Updates ✅
- [x] Update `TeamKanbanCard` to use `useKanbanStore` and proper selectors
- [x] Update `TeamCardDetailModal` to use `useKanbanStore` and proper selectors
- [x] Update `TeamKanbanBoard` to use `useKanbanStore` and proper selectors
- [x] Update `TeamKanbanColumn` to use `useKanbanStore` and proper selectors
- [x] Update `TeamKanbanBoard` to use `initializeStore` instead of directly calling `fetchBoard`

### Phase 5: Testing and Cleanup ⏳
- [ ] Test all Kanban board functionality
- [ ] Remove old monolithic card store (`src/features/teams/store/kanban/cardStore.ts`)
- [ ] Clean up diagnostic logging
- [ ] Document the new store structure and usage patterns

## Current Status
We have successfully migrated the Kanban store from a monolithic structure to a sliced architecture. The main components have been updated to use the new store, and we have fixed the empty assignee list issue by ensuring that users are loaded before the board.

## Next Steps
1. Conduct thorough testing of all Kanban functionality to ensure nothing was broken during the migration.
2. After confirming that the migration is working correctly, remove the old monolithic card store.
3. Update documentation to reflect the new store structure and usage patterns.

## Testing Plan
1. **Card Management**
   - Create new cards
   - Edit card details (title, description)
   - Move cards between columns
   - Archive and delete cards

2. **Subtask Management**
   - Create new subtasks
   - Mark subtasks as complete/incomplete
   - Reorder subtasks
   - Delete subtasks

3. **Comment Management**
   - Add comments to cards
   - Edit comments
   - Delete comments
   - Load more comments (pagination)

4. **User Assignment**
   - Assign users to cards
   - Assign users to subtasks
   - Filter cards by assignee

5. **Column Management**
   - Create new columns
   - Edit column titles
   - Reorder columns
   - Archive and delete columns

6. **Filtering and Sorting**
   - Filter cards by priority
   - Filter cards by assignee
   - Sort cards by due date
   - Show/hide archived cards 