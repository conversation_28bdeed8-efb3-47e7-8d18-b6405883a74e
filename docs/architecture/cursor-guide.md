# Working with Cursor AI and cadence.ai

This guide explains how to effectively use Cursor AI with our project structure and ensure consistent development practices.

## Understanding Project Structure with Cursor

Cursor uses several mechanisms to understand the project structure:

1. **`.cursorrules` file**: Contains coding conventions, directory structure, and best practices
2. **Documentation**: References the `docs/` folder for specific implementation details
3. **Available Rules**: Custom instructions that Cursor can reference for best practices

## How Cursor Uses These Files

When you interact with Cursor AI:

1. It reads the `.cursorrules` file to understand project architecture and conventions
2. It can access documentation in the `docs/` folder for detailed implementation guidance
3. It can use available rules that define best practices for various technologies

## How to Ask Cursor for Help

When requesting help from Cursor, follow these guidelines to ensure consistent results:

### Specify the Feature Context

Always mention which feature you're working on:

```
// Good
"I need help implementing a drag and drop feature for the Kanban board in the teams feature."

// Less specific
"I need help implementing drag and drop."
```

### Reference Documentation

Point Cursor to relevant documentation:

```
// Good
"Please implement this following the pattern in docs/kanban-store-README.md"

// Less specific
"Please implement this feature."
```

### Specify Rule Names

Reference specific rules when applicable:

```
// Good
"Help me implement this component following the shadcn-ui-best-practices."

// Less specific
"Help me create a new component."
```

### Specify File Paths

Be specific about file locations and naming conventions:

```
// Good
"Create a new component at src/features/teams/components/TeamAssignee.tsx"

// Less specific
"Create a team assignee component."
```

## Common Cursor Commands

Here are some helpful Cursor commands to assist with development:

1. **Explain code**: Select code and ask Cursor to explain it
2. **Refactor code**: Ask Cursor to refactor code following project conventions
3. **Create tests**: Request test creation for specific components or utilities
4. **Debug issues**: Show Cursor the error and relevant code for debugging help

## Examples of Effective Requests

### Creating a New Feature

```
"I need to create a new feature for task filtering. Please help me:
1. Create the necessary files in src/features/tasks
2. Implement the filter component following our component structure
3. Set up the store following the Zustand slice pattern in docs/kanban-store-README.md
4. Add appropriate tests"
```

### Debugging an Issue

```
"I'm getting this error when trying to update a task. The error happens in the TaskCard component when clicking the edit button. Here's the component code and the error message. Can you help me debug it following our error handling best practices?"
```

### Refactoring Code

```
"Can you help me refactor this component to follow our performance best practices? It's re-rendering too often when data changes."
```

## Maintaining Consistency

To ensure Cursor maintains consistency with the project structure:

1. Keep the `.cursorrules` file updated when architecture decisions change
2. Document significant implementations in the `docs/` folder
3. Reference existing code patterns when asking for help
4. Be specific in your requests about file locations and naming

## When Cursor Suggestions Don't Match Conventions

If Cursor suggests code that doesn't match our conventions:

1. Point out the specific convention it missed
2. Reference the relevant documentation or rule
3. Ask it to revise the suggestion

For example:

```
"This looks good, but our naming convention uses PascalCase for component files. 
Can you update this to follow that convention as specified in our .cursorrules file?"
```

## Enhancing Cursor's Understanding Over Time

To improve Cursor's understanding of the project:

1. Update the `.cursorrules` file when new conventions are established
2. Create example implementations for complex patterns
3. Document architectural decisions in the `docs/architecture/` folder
4. Provide feedback when Cursor misunderstands the project structure 