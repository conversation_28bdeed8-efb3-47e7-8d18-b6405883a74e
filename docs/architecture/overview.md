# cadence.ai Architecture Overview

This document provides a comprehensive overview of the architecture, structure, and development approach for the Cadence.ai application.

## Application Architecture

cadence.ai follows a feature-based modular architecture with clear separation of concerns. The application is built with React, TypeScript, and leverages modern tools and libraries for state management, styling, and backend services.

### Key Architectural Principles

1. **Feature-Based Organization**: Code is organized by feature rather than by technical role
2. **Modular Components**: Components are designed to be reusable and composable
3. **Centralized State Management**: Zustand is used for global state management
4. **Clean API Layer**: Backend services are abstracted through a clean API layer
5. **Type Safety**: TypeScript is used throughout the application for type safety

## Directory Structure

```
src/
├── app/                    # App-wide setup and configuration
│   ├── providers/         # All context providers
│   ├── routes/           # Route definitions and configurations
│   └── layout/           # Root layout components
├── assets/               # Static assets (images, fonts, etc.)
├── components/
│   ├── ui/              # Reusable UI components (shadcn)
│   └── common/          # Shared components used across features
├── features/            # Feature-based modules
│   ├── auth/           
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── store/
│   │   ├── types/
│   │   └── __tests__/
│   ├── teams/
│   ├── tasks/
│   └── calendar/
├── hooks/               # Global custom hooks
├── lib/                # Third-party library configurations
│   ├── supabase/
│   └── utils/
├── services/           # API and external service integrations
│   ├── api/
│   └── storage/
├── store/              # Global state management (Zustand stores)
├── styles/             # Global styles and Tailwind configurations
├── types/              # Global TypeScript types/interfaces
└── utils/              # Shared utility functions
```

## Tech Stack

- **Frontend Framework**: React
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn UI / Radix UI
- **State Management**: Zustand
- **Backend/Database**: Supabase
- **Build Tool**: Vite

## Conventions and Standards

### Naming Conventions

- **Directories**: lowercase with dashes (e.g., `form-wizard/`)
- **Component Files**: PascalCase (e.g., `TeamCard.tsx`)
- **Utility Files**: camelCase (e.g., `formatDate.ts`)
- **Database Tables**: snake_case (e.g., `team_members`)
- **Exports**: Named exports preferred over default exports

### Component Structure

Components follow a consistent structure:

```tsx
// Import statements
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import type { ComponentProps } from '@/types/common';

// Type definitions
interface ExampleProps extends ComponentProps {
  title: string;
  onAction: () => void;
}

// Component definition
export function Example({ title, onAction, className }: ExampleProps) {
  // State and hooks
  const [isActive, setIsActive] = useState(false);
  
  // Handlers
  const handleClick = () => {
    setIsActive(!isActive);
    onAction();
  };
  
  // Render
  return (
    <div className={className}>
      <h2>{title}</h2>
      <Button onClick={handleClick}>
        {isActive ? 'Active' : 'Inactive'}
      </Button>
    </div>
  );
}
```

### State Management

The application uses Zustand for state management with a slice pattern for complex stores:

```typescript
// Store definition
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

// Define state interface
interface ExampleState {
  // State
  items: Item[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchItems: () => Promise<void>;
  addItem: (item: Item) => Promise<void>;
  removeItem: (id: string) => Promise<void>;
  
  // Utilities
  cleanup: () => void;
}

// Create store
export const useExampleStore = create<ExampleState>()(
  immer((set, get) => ({
    // Initial state
    items: [],
    isLoading: false,
    error: null,
    
    // Actions
    fetchItems: async () => {
      set({ isLoading: true, error: null });
      try {
        const items = await api.getItems();
        set({ items, isLoading: false });
      } catch (error) {
        set({ error: error.message, isLoading: false });
      }
    },
    
    addItem: async (item) => {
      // Implementation
    },
    
    removeItem: async (id) => {
      // Implementation
    },
    
    // Cleanup
    cleanup: () => {
      set({ items: [], isLoading: false, error: null });
    }
  }))
);

// Define selectors
export const exampleSelectors = {
  selectItems: (state: ExampleState) => state.items,
  selectIsLoading: (state: ExampleState) => state.isLoading,
  selectError: (state: ExampleState) => state.error,
  selectItemById: (id: string) => (state: ExampleState) => 
    state.items.find(item => item.id === id)
};
```

### API and Services

Services are organized by domain and follow a consistent pattern:

```typescript
// Service definition
import { supabase } from '@/lib/supabase';

export async function getItems() {
  const { data, error } = await supabase
    .from('items')
    .select('*');
    
  if (error) throw new Error(error.message);
  return data;
}

export async function createItem(item) {
  const { data, error } = await supabase
    .from('items')
    .insert(item)
    .select()
    .single();
    
  if (error) throw new Error(error.message);
  return data;
}
```

## Feature Implementation Examples

### Kanban Board

The Kanban board feature demonstrates the application's architecture approach. See `docs/kanban-store-README.md` for detailed implementation.

Key aspects:
- Store divided into slices (board, card, comment, user)
- Each slice has its own state and actions
- Selectors for optimized state access
- Cleanup functions to prevent memory leaks

## Development Workflow

### Adding a New Feature

1. Create a new directory in `src/features/`
2. Define types in `types/` directory
3. Implement components in `components/` directory
4. Create hooks in `hooks/` directory
5. Implement state management in `store/` directory
6. Write tests in `__tests__/` directory
7. Document the feature in `docs/`

### Modifying Existing Features

1. Understand the current implementation
2. Follow the existing patterns and conventions
3. Update tests to reflect changes
4. Update documentation as needed

## Best Practices

### Performance

- Use `React.memo()` for performance-critical components
- Implement `useCallback` for function props
- Use `useMemo` for expensive computations
- Use selectors for state access to ensure proper memoization

### Security

- Sanitize user inputs
- Handle sensitive data properly
- Implement proper CORS handling
- Use Supabase RLS policies
- Handle authentication tokens securely
- Never expose database credentials in client code

### Accessibility

- Ensure proper semantic HTML
- Implement ARIA attributes where necessary
- Test with screen readers
- Ensure keyboard navigation works properly

## Documentation

- All features should be documented in the `docs/` directory
- Complex components should have JSDoc comments
- Hooks should include usage examples
- Update documentation when implementing significant changes
- Document database schema changes in migration files

## Reference Implementations

For new development, refer to these implementations as examples:

- **Feature Structure**: `src/features/auth/`
- **Component Design**: `src/components/common/`
- **Hook Pattern**: `src/hooks/`
- **Store Implementation**: `src/features/teams/store/teamKanbanStore.ts`
- **API Service**: `src/services/api/` 