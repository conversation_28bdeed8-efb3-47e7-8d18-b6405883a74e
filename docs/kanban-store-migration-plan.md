# Kanban Store Migration Plan

## Overview
This document outlines the plan to refactor the monolithic `teamKanbanStore.ts` into a more maintainable and scalable structure using a hybrid approach of slices and API layer separation.

## New Structure
```
src/features/teams/
├── api/
│   ├── boardApi.ts       # Board-related Supabase calls
│   ├── cardApi.ts        # Card-related Supabase calls
│   ├── commentApi.ts     # Comment-related Supabase calls
│   └── userApi.ts        # User-related Supabase calls
├── store/
│   ├── slices/
│   │   ├── boardSlice.ts    # Board state and actions
│   │   ├── cardSlice.ts     # Card state and actions
│   │   ├── commentSlice.ts  # Comment state and actions
│   │   └── userSlice.ts     # User state and actions
│   └── teamKanbanStore.ts   # Main store combining slices
├── types/
│   └── index.ts         # All types in one file
└── utils/
    └── transformers.ts  # Data transformation utilities
```

## Migration Steps

### Phase 1: Setup (Day 1)
1. Create new directory structure
2. Move existing types to `types/index.ts`
3. Create empty files for all components
4. Setup basic exports/imports

### Phase 2: API Layer (Days 2-3)
1. Extract Supabase calls to API files:
   - `boardApi.ts`: Column operations
   - `cardApi.ts`: Card and subtask operations
   - `commentApi.ts`: Comment operations
   - `userApi.ts`: User and assignment operations

2. Create utility functions for data transformation
   - Move transformation logic from store to `utils/transformers.ts`
   - Create reusable transformation functions

### Phase 3: Store Slices (Days 4-5)
1. Create slice interfaces:
```typescript
// Example for cardSlice
interface CardSlice {
  cards: TeamKanbanCard[];
  addCard: (columnId: string, title: string) => Promise<void>;
  updateCard: (cardId: string, updates: Partial<TeamKanbanCard>) => Promise<void>;
  // ... other card-related state and actions
}
```

2. Implement slices:
   - `boardSlice.ts`: Column management, board layout
   - `cardSlice.ts`: Cards and subtasks
   - `commentSlice.ts`: Comments
   - `userSlice.ts`: Users and assignments

### Phase 4: Store Integration (Day 6)
1. Update main store to use slices:
```typescript
export const useTeamKanbanStore = create<TeamKanbanState>((set, get) => ({
  ...createBoardSlice(set, get),
  ...createCardSlice(set, get),
  ...createCommentSlice(set, get),
  ...createUserSlice(set, get)
}));
```

2. Implement cross-slice communication patterns
3. Add error handling and loading states

### Phase 5: Testing (Days 7-8)
1. Write tests for API layer
2. Write tests for transformers
3. Write tests for each slice
4. Write integration tests
5. Update existing tests

### Phase 6: Documentation & Cleanup (Day 9)
1. Add JSDoc comments to all exports
2. Create usage examples
3. Update README
4. Remove old code
5. Final testing

## Detailed Slice Breakdown

### Board Slice
```typescript
interface BoardSlice {
  columns: TeamKanbanColumn[];
  columnData: Map<string, ColumnData>;
  showArchivedColumns: Set<string>;
  
  // Actions
  fetchBoard: (teamId: string) => Promise<void>;
  addColumn: (teamId: string, title: string) => Promise<void>;
  updateColumn: (columnId: string, title: string) => Promise<void>;
  deleteColumn: (columnId: string) => Promise<void>;
  reorderColumns: (teamId: string, columnIds: string[]) => Promise<void>;
  toggleColumnArchived: (columnId: string) => Promise<void>;
}
```

### Card Slice
```typescript
interface CardSlice {
  cards: TeamKanbanCard[];
  subtasks: TeamKanbanSubtask[];
  
  // Actions
  addCard: (columnId: string, title: string) => Promise<void>;
  updateCard: (cardId: string, updates: Partial<TeamKanbanCard>) => Promise<void>;
  deleteCard: (cardId: string) => Promise<void>;
  moveCard: (cardId: string, columnId: string, newIndex: number) => Promise<void>;
  addSubtask: (cardId: string, title: string) => Promise<void>;
  updateSubtask: (subtaskId: string, updates: Partial<TeamKanbanSubtask>) => Promise<void>;
  deleteSubtask: (subtaskId: string) => Promise<void>;
}
```

### Comment Slice
```typescript
interface CommentSlice {
  comments: TeamKanbanComment[];
  commentPages: Record<string, TeamKanbanComment[]>;
  hasMoreComments: Record<string, boolean>;
  loadingComments: Record<string, boolean>;
  
  // Actions
  fetchComments: (cardId: string, page?: number) => Promise<void>;
  addComment: (cardId: string, content: string) => Promise<void>;
  updateComment: (commentId: string, content: string) => Promise<void>;
  deleteComment: (commentId: string) => Promise<void>;
}
```

### User Slice
```typescript
interface UserSlice {
  users: User[];
  
  // Actions
  updateUsers: () => Promise<void>;
  assignUser: (userId: string, cardId: string) => Promise<void>;
  unassignUser: (cardId: string) => Promise<void>;
}
```

## Error Handling
- Each slice will implement its own error handling
- Errors will be propagated to the UI layer
- Common error types will be defined in `types/index.ts`

## State Management Patterns
1. Use immer for immutable updates
2. Implement optimistic updates where appropriate
3. Use proper TypeScript types for type safety
4. Implement proper loading states

## Performance Considerations
1. Implement proper memoization
2. Use selective subscriptions
3. Implement proper pagination
4. Cache results where appropriate

## Testing Strategy
1. Unit tests for API layer
2. Unit tests for transformers
3. Unit tests for each slice
4. Integration tests for store
5. E2E tests for critical flows

## Rollback Plan
1. Keep old store implementation until new one is fully tested
2. Implement feature flags for gradual rollout
3. Monitor for errors and performance issues
4. Have clear rollback procedures

## Timeline
- Total Duration: 9 days
- Phase 1: 1 day
- Phase 2: 2 days
- Phase 3: 2 days
- Phase 4: 1 day
- Phase 5: 2 days
- Phase 6: 1 day

## Success Metrics
1. Reduced file sizes (< 300 lines per file)
2. Improved test coverage (> 80%)
3. Reduced complexity (cyclomatic complexity < 10)
4. Improved performance metrics
5. Easier maintenance (measured by time to implement new features)

## Future Considerations
1. Potential for code splitting
2. Real-time updates optimization
3. Cache management
4. State persistence
5. Offline support 