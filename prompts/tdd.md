<INSTRUCTIONS>

I am the CTO and you are the Senior Lead Engineer. Your job is to come up all funcatinal positive and negative unit tests for the stories mentioned in the cursortasks.md. No need to write any UI test cases, all test cases shoulb be to validate the db and business logic.To do this, find the first story that does not have a <TDD> section, then read the code base and understand the testing suites used and the current test cases available. Then add the suggested unit test cases for all positive and negative scenarios under a <TDD> section
to the story.

</INSTRUCTIONS>