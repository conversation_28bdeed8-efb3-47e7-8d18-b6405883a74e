# Story 1

As a user, I should be able to view my team's weekly activity summary. I will access this via a new "Team Updates" page, linked in the sidebar below the "Teams" section.

On this page, I will first select a team from a dropdown list. For regular users, this list shows teams they are members of; for admins, it shows all teams.

Next, I will select a specific week using a calendar component. Clicking any day within the calendar will select the corresponding week (Monday to Saturday). The selected week will be highlighted, and I can only select weeks _before_ the current week (i.e., previous weeks are selectable, but the current and future weeks are disabled).

Upon confirming the team and week selection, the page will display the formatted `team_update_summary` fetched from the `team_weekly_updates` table for that team and week. If no update exists for the selection, a message like "No team updates found for the selected week" will be shown.

I should be able to change the selected team or week at any time directly on this page to view different reports.

The page should try to load a default view: it will check local storage for the last viewed team ID. If found, it defaults to showing that team's report for the _previous_ week. If no team ID is found in local storage, it presents an empty state, prompting me to select a team and week.

<IMPORTANT_NOTES>

- **Navigation:** Sidebar link named "Team Updates" placed below "Teams".
- **Default Load:**
  - Check local storage for `last_viewed_team_id`.
  - Default week selector to the _previous_ week (Monday-Saturday).
  - If `last_viewed_team_id` exists, load report for that team/previous week.
  - If not, show empty state (prompt for team/week selection).
- **Team Selection:**
  - Dropdown component.
  - Regular users: List teams they are members of.
  - Admin users: List all teams.
  - Store selected `team_id` in local storage on successful report load.
- **Week Selection:**
  - Use a calendar UI (e.g., Shadcn Calendar).
  - Clicking a day selects the Mon-Sat week containing that day.
  - Highlight the selected week range.
  - Disable selection for the current week and all future weeks.
- **Data Display:**
  - Fetch `team_update_summary` from `team_weekly_updates` based on `team_id` and week's start/end dates.
  - Display the summary text with appropriate formatting (e.g., handle markdown/line breaks).
- **No Data Handling:** Display message: "No team updates found for the selected week."
- **Interaction:** Team and Week selectors reside on the page, allowing dynamic updates.
- **Backend Needs:** - API endpoint to fetch teams for the current user (distinguishing between admin/regular users). - API endpoint to fetch `team_weekly_update` based on `team_id` and date range (start/end date).
  </IMPORTANT_NOTES>

<RELEASE_NOTES>
The Team Updates feature has been successfully implemented. Here's a summary of the changes:

1. **New API service**: `teamUpdatesApi.ts`

   - Added functions to fetch teams for the current user (with admin/regular user distinction)
   - Added function to get team weekly updates by team ID and date range

2. **New UI Components**:

   - Created Team Updates page component
   - Added Calendar component for week selection
   - Used existing Shadcn UI components (Card, Select, Popover) for the interface

3. **Navigation & Routing**:

   - Added Team Updates link to Sidebar (with BarChart3 icon), below Teams
   - Added route at `/team-updates` to the AppRoutes configuration

4. **Feature Implementation**:
   - Team selection dropdown that shows appropriate teams based on user role
   - Week selection calendar that disables current and future weeks
   - Loading state with spinner when fetching data
   - "No data" message when no updates exist for the selected period
   - Local storage integration for remembering last viewed team

The implementation follows all requirements specified in the story, with responsive design for different screen sizes and clear user feedback throughout the interaction flow.
</RELEASE_NOTES>
