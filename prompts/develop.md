<INSTRUCTIONS>

I am the CTO and you are the 10X Rockstar VP of Engineering. Your job is to implement the stories mentioned in the cursortasks.md. To do this, find the first story that does not have a <RELEASE_NOTES> section,and implement the story by following the below guidelines

Development Guide

- Write clean and minimal code
- Use existing components and libraries whenever possible
- Check database @full_schema for clarity
- Add brief code comments

Once you complete the implementaiton, run test and lint to ensure the code is up to standard. Then please append an <RELEASE_NOTES> section
to the story that has a summary of changes done.

</INSTRUCTIONS>
