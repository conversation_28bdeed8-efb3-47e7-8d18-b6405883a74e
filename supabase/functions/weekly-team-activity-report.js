import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js";
// Hoist the normalizeEmails function
const normalizeEmails = (input)=>{
  if (!input) return [];
  // Ensure consistent handling for both strings and arrays
  const emails = Array.isArray(input) ? input : String(input).split(',');
  return emails.map((e)=>String(e).trim()) // Ensure elements are strings before trimming
  .filter(Boolean).map((email)=>({
      email
    }));
};
// Team map: { teamId: { name: string, to: string | string[], bcc?: string | string[] } }
// 
const teams = {
  "c6096049-65bb-49a3-ba75-0eb18fa3b726": {
    name: "Social Media",
    to: "<EMAIL>"
  },
  "c04d1e2a-294c-4419-a86b-24b58f902916": {
    name: "Leadership",
    to: "<EMAIL>"
  },
  "6c0bcbaa-6d3f-4fef-84cf-6037beb6221d": {
    name: "HR",
    to: "<EMAIL>"
  },
  "d4347f94-d280-44dc-8279-59096dee5d08": {
    name: "Portfolio Management",
    to: "<EMAIL>"
  },
  "2a5c6dee-67f0-48fa-93d4-329d4d4f5064": {
    name: "Finance",
    to: "<EMAIL>"
  },
  "bdca8a3a-5693-46a6-b24e-0afd895ce7f5": {
    name: "fundfolio",
    to: "<EMAIL>"
  },
  "9a7fd40f-cf27-4e51-bc8b-c495f852db0e": {
    name: "Retention",
    to: "<EMAIL>"
  },
  "d20336c4-fd05-43f1-813f-9be6b48b1e58": {
    name: "Activation",
    to: "<EMAIL>"
  },
  "73d3be9e-dec7-401b-a84c-c4a6326080a7": {
    name: "TAP",
    to: "<EMAIL>"
  },
  "ce06754f-e7f4-431b-aba5-2b0002423bef": {
    name: "Quant Research",
    to: "<EMAIL>"
  },
  "dddc0a8d-8127-40f3-8b73-0944247ca4f7": {
    name: "Acquisition",
    to: "<EMAIL>"
  }
};
Deno.serve(async (_req)=>{
  const supabaseUrl = Deno.env.get("SUPABASE_URL");
  const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY");
  const llmApiKey = Deno.env.get("LLM_API_KEY");
  const llmApiUrl = "https://openrouter.ai/api/v1/chat/completions";
  if (!supabaseUrl || !supabaseAnonKey || !llmApiKey) {
    console.error("Missing required environment variables: SUPABASE_URL, SUPABASE_ANON_KEY, LLM_API_KEY");
    return new Response(JSON.stringify({
      error: "Internal configuration error"
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
  const supabaseClient = createClient(supabaseUrl, supabaseAnonKey);
  const { startDate, endDate } = getPreviousWeekRange();

  const promises = Object.entries(teams).map(async ([teamId, config]) => {
      const recipientEmails = normalizeEmails(config.to);
      const bccEmails = normalizeEmails(config.bcc);
      const teamName = config.name || `Team ${teamId.substring(0, 6)}`;

      if (recipientEmails.length === 0) {
        console.warn(`Skipping team ${teamId}: No valid 'to' recipients found.`);
      return {
        teamId,
        status: "skipped",
        reason: "No valid 'to' recipients"
      };
      }

      try {
        const { data, error } = await supabaseClient.rpc("team_activity_report", {
          input_team_id: teamId,
          start_date: startDate,
          end_date: endDate
        });
        console.log(`Fetching data Team: "${teamId}", from :"${startDate}" to :"${endDate}" `);
      console.log(data);
        if (error) {
          console.error(`RPC failed for team ${teamId}:`, error);
        return {
          teamId,
          status: "failed",
          reason: "RPC error",
          error: error.message,
          input_team_id: teamId,
          start_date: startDate,
          end_date: endDate
        };
        }

        const formattedMessage = await formatMessageWithLLM(data, llmApiUrl, llmApiKey);
      const weekNumber = getWeekNumber(new Date(startDate));
      const subject = `${teamName} Team Week ${weekNumber} Summary by cadence.ai`;

      await sendEmail({
        to: recipientEmails.map(r => r.email),
        bcc: bccEmails.map(r => r.email),
        subject: subject,
        body: formattedMessage
      });
        await supabaseClient.from("team_weekly_updates").insert({
          team_id: teamId,
          start_date: startDate,
          end_date: endDate,
        week_number: weekNumber.toString(),
          team_updates: data,
          team_update_summary: formattedMessage
        });
      return {
        teamId,
        status: "sent",
        recipients: recipientEmails.map(r => r.email),
        bcc: bccEmails.map(r => r.email)
      };
    } catch (err) {
      console.error(`Error processing team ${teamId}:`, err);
      return {
        teamId,
        status: "failed",
        reason: err.message,
        errorDetails: err.cause || err.stack
      };
    }
  });

  const settledResults = await Promise.allSettled(promises);
  const results = settledResults.map(result => result.status === "fulfilled" ? result.value : { status: "failed", reason: result.reason?.message || "Unknown error" });

  return new Response(JSON.stringify({
    message: "Execution completed",
    results
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json'
    }
  });
});
function getPreviousWeekRange() {
  const now = new Date();
  const IST_OFFSET = 5.5 * 60 * 60 * 1000;
  const istNow = new Date(now.getTime() + IST_OFFSET);

  const day = istNow.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  const offsetToMonday = day === 0 ? 6 : day - 1;

  const lastMonday = new Date(istNow);
  lastMonday.setDate(istNow.getDate() - offsetToMonday - 7);
  lastMonday.setHours(0, 0, 0, 0);

  const lastSunday = new Date(lastMonday);
  lastSunday.setDate(lastMonday.getDate() + 6);
  lastSunday.setHours(23, 59, 59, 999);

  return {
    startDate: lastMonday.toISOString().slice(0, 10),
    endDate: lastSunday.toISOString().slice(0, 10)
  };
}
async function formatMessageWithLLM(data, apiUrl, apiKey) {
  // Basic check for data structure, adjust as needed
  if (!data || typeof data !== 'object') {
    console.warn("formatMessageWithLLM received invalid data:", data);
    // Return a default message or throw an error
    return "No activity data available to format.";
  }
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), 20000); // 20s timeout

  let response;
  try {
    response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`,
        "HTTP-Referer": "https://cadence.marketfeed.com"
      },
      body: JSON.stringify({
        model: Deno.env.get("LLM_MODEL"),
        messages: [
          {
            role: "system",
            content: `You are a helpful assistant that summarizes team activity data into a professional weekly email report. The summary should be in a format suitable for email clients (not markdown). Use ✅, 📌, 📈, etc. where relevant to highlight sections and key items.

Output format:
- Use clear headings like “✅ Key Accomplishments, “📌 Analysis, “⚠️ Bottlenecks, “🔍 Next Steps”, “👏 Shoutouts”, “🏥 Health Metrics”
- Use bullet points (•) under each section
- Group related tasks and add short analysis when relevant
- Avoid using Markdown syntax (###, **bold**, etc.)
- Structure should be: heading → 1-line summary → bullets if needed → next heading

Tone: Friendly, constructive, and professional. Keep it readable and polished.

Wrap up with a closing like:
—
Regards,  
cadence-ai`
          },
          {
            role: "user",
            content: `Generate a formal weekly summary draft email for the team.

Key Activities to Analyze:
1. Completed Tasks: ${data.completedTasks}
2. Ongoing Work: ${data.cardMovements}
3. New Initiatives: ${data.newCards}
4. Key Discussions: ${data.comments}
5. Sub Tasks Updates: ${data.subtask_updates}
6. Health Metrics: ${data.health_metrics}
Please include:
1. A concise summary of key accomplishments
2. Analysis of work patterns
3. Bottlenecks or blockers observed
4. Recommendations or next steps
5. Shoutouts or recognition for contributors
6. Health Metrics: Highlight each metric's status (green ->✅ /yellow->⚠️ /red->🔴) with current value and unit
Email Signature:
          —
          Regards,  
cadence-ai

Raw data:
${JSON.stringify(data)}`
          }
        ]
      }),
      signal: controller.signal
    });
  } catch (err) {
    if (err.name === 'AbortError') {
      console.error("LLM request timed out.");
      throw new Error("LLM API request timed out.");
    } else {
      throw err;
    }
  } finally {
    clearTimeout(timeout);
  }

  const resultText = await response.text(); // Always get text first for debugging
  if (!response.ok) {
    console.error("OpenRouter LLM Error:", response.status, resultText);
    // Provide more context in the error
    throw new Error(`LLM API request failed with status ${response.status}. Response: ${resultText}`);
  }
  let result;
  try {
    result = JSON.parse(resultText);
  } catch (e) {
    console.error("Failed to parse LLM response JSON:", resultText);
    // Include the parsing error and the text that failed
    throw new Error(`LLM response format invalid. Parse Error: ${e.message}. Response Text: ${resultText}`);
  }
  // More robust check for the expected output structure
  const output = result?.choices?.[0]?.message?.content;
  if (typeof output !== 'string' || output.trim() === '') {
    console.error("No valid message content returned from LLM. Full response:", JSON.stringify(result, null, 2));
    throw new Error("No valid message content returned from LLM.");
  }
  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>Weekly Summary</title>
</head>
<body style="font-family: sans-serif; line-height: 1.6; background-color: #f9f9f9; padding: 20px;">
  <div style="max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 0 5px rgba(0,0,0,0.05); white-space: pre-wrap;">
    ${output
      .replace(/^Here’s your polished weekly team summary email:\s*\\n?/i, '')
      .replace(/^---+$/gm, '')
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/^([^\w\s]{1,2}) (.+)$/gm, '<h3>$1 $2</h3>')
      .replace(/^• (.+)$/gm, '<li>$1</li>')
      .replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>')}
  </div>
</body>
</html>
`;
}
// Update sendEmail to accept arrays of email strings directly
async function sendEmail({ to = [], bcc = [], subject, body }) {
  // Ensure inputs are arrays, provide defaults if undefined/null
  const safeTo = Array.isArray(to) ? to : [];
  const safeBcc = Array.isArray(bcc) ? bcc : [];
  console.log(`Preparing to send email. To: ${safeTo.join(', ') || 'None'}. BCC: ${safeBcc.join(', ') || 'None'}. Subject: "${subject}"`);
  const apiKey = Deno.env.get("BREVO_API_KEY");
  const senderEmail = Deno.env.get("BREVO_SENDER_EMAIL") || "<EMAIL>"; // Use env var or default
  const senderName = Deno.env.get("BREVO_SENDER_NAME") || "cadence.ai"; // Use env var or default
  if (!apiKey) {
    throw new Error("Missing Brevo API Key (BREVO_API_KEY) in environment variables");
  }
  // Map emails to the required Brevo format { email: string }
  const toArray = safeTo.filter(Boolean).map((email)=>({
      email: email.trim()
    }));
  const bccArray = safeBcc.filter(Boolean).map((email)=>({
      email: email.trim()
    }));
  if (toArray.length === 0) {
    // Log a warning instead of throwing an error if called with no recipients
    console.warn("sendEmail called with no valid 'to' recipients. Skipping send.");
    return; // Don't proceed if there's no one to send to
  }
  const payload = {
    sender: {
      name: senderName,
      email: senderEmail
    },
    to: toArray,
    // Only include bcc if it has valid entries
    ...bccArray.length > 0 && {
      bcc: bccArray
    },
    subject,
    htmlContent: `<pre style="font-family: monospace; white-space: pre-wrap; word-wrap: break-word;">${body}</pre>` // Basic styling for <pre>
  };
  // Optional: Conditionally log payload for debugging
  // if (Deno.env.get("DEBUG_MODE") === "true") {
  //   console.log("Brevo payload:", JSON.stringify(payload, null, 2));
  // }
  const response = await fetch("https://api.brevo.com/v3/smtp/email", {
    method: "POST",
    headers: {
      "api-key": apiKey,
      "Content-Type": "application/json",
      "Accept": "application/json"
    },
    body: JSON.stringify(payload)
  });
  const resultText = await response.text(); // Read text response for logging/errors
  if (!response.ok) {
    console.error("Brevo Email Send Error:", response.status, resultText);
    // Log the payload that failed for easier debugging
    // console.error("Failed Brevo payload:", JSON.stringify(payload, null, 2));
    throw new Error(`Brevo send failed: ${response.statusText}. Response: ${resultText}`);
  }
  console.log(`Email sent successfully via Brevo. Subject: "${subject}". To: ${safeTo.join(', ')}. BCC: ${safeBcc.join(', ') || 'None'}`);
}
// Function to calculate ISO 8601 week number
function getWeekNumber(d) {
  // Copy date so don't modify original
  d = new Date(Date.UTC(d.getFullYear(), d.getMonth(), d.getDate()));
  // Set to nearest Thursday: current date + 4 - current day number
  // Make Sunday's day number 7
  d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay() || 7));
  // Get first day of year
  var yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  // Calculate full weeks to nearest Thursday
  var weekNo = Math.ceil(((d - yearStart) / 86400000 + 1) / 7);
  // Return week number
  return weekNo;
}