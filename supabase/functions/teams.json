{"period": {"start": "2025-04-07T00:00:00", "end": "2025-04-13T00:00:00"}, "completed_tasks": [{"card_id": "6e07467e-cabb-49af-b6cb-3ae2924df8d0", "card_title": "Abhay - Retool - Read only access", "completed_at": "2025-04-09T04:50:46.443+00:00", "completed_by": "aslam.nc", "archived_at": "2025-04-09T12:47:12.348+00:00"}, {"card_id": "f17e2c23-0a1b-4b12-97b3-b0b9843cc880", "card_title": "<PERSON><PERSON><PERSON>'s Training", "completed_at": "2025-04-09T04:53:09.346+00:00", "completed_by": "junaid.pp", "archived_at": "2025-04-09T11:59:21.962+00:00"}], "card_movements": [{"card_id": "6e07467e-cabb-49af-b6cb-3ae2924df8d0", "card_title": "Abhay - Retool - Read only access", "moved_at": "2025-04-09T04:50:46.443+00:00", "moved_by": "aslam.nc", "current_column": "This Week's Priorities"}, {"card_id": "91526114-3b17-4aae-98c7-6564a48066f7", "card_title": "Optimise TAP Delay ", "moved_at": "2025-04-08T11:37:48.35+00:00", "moved_by": "junaid.pp", "current_column": "Upcoming Projects"}], "new_cards": [{"card_id": "6e07467e-cabb-49af-b6cb-3ae2924df8d0", "card_title": "Abhay - Retool - Read only access", "created_at": "2025-04-09T04:50:24.218892+00:00", "created_by": "aslam.nc", "column_title": "This Week's Priorities"}, {"card_id": "1976132b-8624-46c7-a0b0-4326bad073bf", "card_title": "Performance Review", "created_at": "2025-04-09T05:17:59.435301+00:00", "created_by": "aslam.nc", "column_title": "Current Projects"}], "subtask_updates": [{"subtask_id": "1f51b218-a1f9-4d01-9d0f-af1aa3a9f409", "subtask_title": " Review", "card_title": "PnL Tracker for Discretionary Actions", "card_id": "9d27a2e5-a2f4-43f2-8128-c55aa9320fbb", "updated_at": "2025-04-07T05:47:06.334569+00:00", "is_completed": true, "updated_by": null}, {"subtask_id": "a57e74d4-def3-4bfa-a0a3-9b1a2946974a", "subtask_title": " Policy", "card_title": "Policy on Prolonged events", "card_id": "8e25dfcb-e983-4627-9395-012fdc5d7a02", "updated_at": "2025-04-08T04:47:08.395402+00:00", "is_completed": true, "updated_by": null}], "comments": [{"comment_id": "71965233-99ec-423b-b6af-2860498a1f39", "card_title": "Abhay - Retool - Read only access", "card_id": "6e07467e-cabb-49af-b6cb-3ae2924df8d0", "content": "✅ Task was marked as completed.", "created_at": "2025-04-09T12:47:12.729899+00:00", "is_system": true, "commented_by": "aslam.nc"}, {"comment_id": "faecc27e-19e6-40aa-9bae-3aebbe87654c", "card_title": "Abhay - Retool - Read only access", "card_id": "6e07467e-cabb-49af-b6cb-3ae2924df8d0", "content": "✅ Subtask \" Access\" was marked as completed.", "created_at": "2025-04-09T12:47:10.166771+00:00", "is_system": true, "commented_by": "aslam.nc"}], "health_metrics": [{"id": "83ce14a0-5d05-4d59-b2d1-dee0e519cd8f", "name": "Bundle Health Metrics ", "unit": "", "green_threshold": 2, "yellow_threshold": 1, "red_threshold": 0, "value": 1, "status": "yellow", "reported_at": "2025-04-09T12:45:25.766961+00:00"}]}