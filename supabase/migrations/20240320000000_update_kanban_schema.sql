-- Update kanban_cards table
ALTER TABLE kanban_cards
ADD COLUMN IF NOT EXISTS due_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS assignees TEXT[],
ADD COLUMN IF NOT EXISTS archived_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Create index for better performance on archived and deleted filters
CREATE INDEX IF NOT EXISTS idx_kanban_cards_archived_deleted ON kanban_cards (archived_at, deleted_at);

-- Update kanban_subtasks table
ALTER TABLE kanban_subtasks
ADD COLUMN IF NOT EXISTS due_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS assignee_id TEXT;

-- Update kanban_comments table
ALTER TABLE kanban_comments
ADD COLUMN IF NOT EXISTS is_markdown BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS edited_at TIMESTAMP WITH TIME ZONE;

-- Update RLS policies
ALTER TABLE kanban_cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE kanban_subtasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE kanban_comments ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if any
DROP POLICY IF EXISTS "Team members can manage cards" ON kanban_cards;
DROP POLICY IF EXISTS "Team members can manage subtasks" ON kanban_subtasks;
DROP POLICY IF EXISTS "Team members can manage comments" ON kanban_comments;

-- Create new policies
CREATE POLICY "Team members can manage cards"
ON kanban_cards
FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM team_members
        WHERE team_members.team_id = kanban_cards.team_id
        AND team_members.user_id = auth.uid()
    )
)
WITH CHECK (
    EXISTS (
        SELECT 1 FROM team_members
        WHERE team_members.team_id = kanban_cards.team_id
        AND team_members.user_id = auth.uid()
    )
);

CREATE POLICY "Team members can manage subtasks"
ON kanban_subtasks
FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM kanban_cards
        JOIN team_members ON team_members.team_id = kanban_cards.team_id
        WHERE kanban_cards.id = kanban_subtasks.card_id
        AND team_members.user_id = auth.uid()
    )
)
WITH CHECK (
    EXISTS (
        SELECT 1 FROM kanban_cards
        JOIN team_members ON team_members.team_id = kanban_cards.team_id
        WHERE kanban_cards.id = kanban_subtasks.card_id
        AND team_members.user_id = auth.uid()
    )
);

CREATE POLICY "Team members can manage comments"
ON kanban_comments
FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM kanban_cards
        JOIN team_members ON team_members.team_id = kanban_cards.team_id
        WHERE kanban_cards.id = kanban_comments.card_id
        AND team_members.user_id = auth.uid()
    )
)
WITH CHECK (
    EXISTS (
        SELECT 1 FROM kanban_cards
        JOIN team_members ON team_members.team_id = kanban_cards.team_id
        WHERE kanban_cards.id = kanban_comments.card_id
        AND team_members.user_id = auth.uid()
    )
); 