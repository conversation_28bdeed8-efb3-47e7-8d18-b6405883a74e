-- Create teams table
CREATE TABLE IF NOT EXISTS teams (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    avatar_url TEXT,
    created_by UUID REFERENCES auth.users(id),
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    progress INTEGER NOT NULL DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    end_date TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days')
);

-- Create team_members table with role management
CREATE TABLE IF NOT EXISTS team_members (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('admin', 'manager', 'member')),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (team_id, user_id)
);

-- Create team_audit_logs table
CREATE TABLE IF NOT EXISTS team_audit_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id),
    action TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_team_members_team_id ON team_members(team_id);
CREATE INDEX IF NOT EXISTS idx_team_members_user_id ON team_members(user_id);
CREATE INDEX IF NOT EXISTS idx_team_audit_logs_team_id ON team_audit_logs(team_id);

-- Enable Row Level Security
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_audit_logs ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies and triggers first
DROP TRIGGER IF EXISTS team_members_audit ON team_members;
DROP TRIGGER IF EXISTS teams_audit ON teams;
DROP POLICY IF EXISTS "Users can view teams they are members of" ON teams;
DROP POLICY IF EXISTS "Admins can create teams" ON teams;
DROP POLICY IF EXISTS "Team admins can update their teams" ON teams;
DROP POLICY IF EXISTS "Team admins can delete their teams" ON teams;
DROP POLICY IF EXISTS "Users can view team members" ON team_members;
DROP POLICY IF EXISTS "Users can view their own teams members" ON team_members;
DROP POLICY IF EXISTS "Team admins can manage team members" ON team_members;
DROP POLICY IF EXISTS "Admins and managers can manage team members" ON team_members;
DROP POLICY IF EXISTS "Admins can view audit logs" ON team_audit_logs;
DROP POLICY IF EXISTS "Team access policy" ON teams;
DROP POLICY IF EXISTS "Team members access policy" ON team_members;
DROP POLICY IF EXISTS "Team members insert policy" ON team_members;
DROP POLICY IF EXISTS "Team members update policy" ON team_members;
DROP POLICY IF EXISTS "Team members delete policy" ON team_members;
DROP POLICY IF EXISTS "Audit logs access policy" ON team_audit_logs;
DROP POLICY IF EXISTS "teams_select_policy" ON teams;
DROP POLICY IF EXISTS "teams_insert_policy" ON teams;
DROP POLICY IF EXISTS "teams_update_policy" ON teams;
DROP POLICY IF EXISTS "teams_delete_policy" ON teams;
DROP POLICY IF EXISTS "team_members_select_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_insert_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_update_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_delete_policy" ON team_members;
DROP POLICY IF EXISTS "audit_logs_select_policy" ON team_audit_logs;

-- Basic policies for teams
CREATE POLICY "teams_policy" ON teams
    FOR ALL USING (
        created_by = auth.uid() OR
        auth.uid() IN (
            SELECT user_id FROM team_members WHERE team_id = id
        )
    );

-- Basic policies for team members
CREATE POLICY "team_members_policy" ON team_members
    FOR ALL USING (
        user_id = auth.uid() OR
        auth.uid() IN (
            SELECT created_by FROM teams WHERE id = team_id
        )
    );

-- Basic policy for audit logs
CREATE POLICY "audit_logs_policy" ON team_audit_logs
    FOR SELECT USING (
        auth.uid() IN (
            SELECT created_by FROM teams WHERE id = team_id
        )
    );

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for teams table
CREATE TRIGGER update_teams_updated_at
    BEFORE UPDATE ON teams
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Modified audit function to prevent recursion
CREATE OR REPLACE FUNCTION log_team_audit_event()
RETURNS TRIGGER AS $$
DECLARE
    v_team_id UUID;
BEGIN
    -- Skip if this is a recursive call from the audit logging itself
    IF current_setting('app.audit_logging', true) = 'true' THEN
        RETURN NULL;
    END IF;

    -- Set the flag to prevent recursion
    PERFORM set_config('app.audit_logging', 'true', true);

    -- Determine the team_id based on the table being audited
    IF TG_TABLE_NAME = 'teams' THEN
        v_team_id := COALESCE(NEW.id, OLD.id);
    ELSE
        v_team_id := COALESCE(NEW.team_id, OLD.team_id);
    END IF;

    -- Insert the audit log
    INSERT INTO team_audit_logs (team_id, user_id, action, details)
    VALUES (
        v_team_id,
        auth.uid(),
        TG_OP,
        jsonb_build_object(
            'old_data', to_jsonb(OLD),
            'new_data', to_jsonb(NEW),
            'table', TG_TABLE_NAME
        )
    );

    -- Reset the flag
    PERFORM set_config('app.audit_logging', 'false', true);

    RETURN NULL;
END;
$$ language 'plpgsql';

-- Create audit triggers
CREATE TRIGGER team_members_audit
    AFTER INSERT OR UPDATE OR DELETE ON team_members
    FOR EACH ROW EXECUTE FUNCTION log_team_audit_event();

CREATE TRIGGER teams_audit
    AFTER INSERT OR UPDATE OR DELETE ON teams
    FOR EACH ROW EXECUTE FUNCTION log_team_audit_event(); 