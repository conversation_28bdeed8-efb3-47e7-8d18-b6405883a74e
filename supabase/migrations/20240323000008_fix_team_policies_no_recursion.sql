-- Enable RLS on both tables
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;

-- Teams policies
CREATE POLICY "authenticated users can view teams" ON teams
    FOR SELECT USING (
        auth.uid() IS NOT NULL
    );

CREATE POLICY "authenticated users can create teams" ON teams
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

CREATE POLICY "team creator can update teams" ON teams
    FOR UPDATE USING (
        auth.uid() = created_by
    );

CREATE POLICY "team creator can delete teams" ON teams
    FOR DELETE USING (
        auth.uid() = created_by
    );

-- Team members policies - Simplified to avoid recursion
CREATE POLICY "authenticated users can view team members" ON team_members
    FOR SELECT USING (
        -- Users can see team members if:
        -- 1. They are a member themselves
        -- 2. They created the team
        user_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM teams
            WHERE id = team_id
            AND created_by = auth.uid()
        )
    );

CREATE POLICY "team creator can manage members" ON team_members
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM teams
            WHERE id = team_id
            AND created_by = auth.uid()
        )
    );

-- Add policy for team admins and managers to manage members
CREATE POLICY "team admins and managers can manage members" ON team_members
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM team_members tm
            WHERE tm.team_id = team_members.team_id
            AND tm.user_id = auth.uid()
            AND tm.role IN ('admin', 'manager')
        )
    ); 