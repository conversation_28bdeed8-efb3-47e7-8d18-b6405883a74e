-- Drop existing policies
DROP POLICY IF EXISTS "teams_policy" ON teams;
DROP POLICY IF EXISTS "team_members_policy" ON team_members;

-- Teams policies
CREATE POLICY "teams_select_policy" ON teams
    FOR SELECT USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM team_members 
            WHERE team_members.team_id = teams.id 
            AND team_members.user_id = auth.uid()
        )
    );

CREATE POLICY "teams_insert_policy" ON teams
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL  -- Any authenticated user can create a team
    );

CREATE POLICY "teams_update_policy" ON teams
    FOR UPDATE USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM team_members 
            WHERE team_members.team_id = teams.id 
            AND team_members.user_id = auth.uid()
            AND team_members.role IN ('admin', 'manager')
        )
    );

CREATE POLICY "teams_delete_policy" ON teams
    FOR DELETE USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM team_members 
            WHERE team_members.team_id = teams.id 
            AND team_members.user_id = auth.uid()
            AND team_members.role = 'admin'
        )
    );

-- Team members policies
CREATE POLICY "team_members_select_policy" ON team_members
    FOR SELECT USING (
        user_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = team_members.team_id 
            AND teams.created_by = auth.uid()
        )
    );

CREATE POLICY "team_members_insert_policy" ON team_members
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = team_id 
            AND (
                teams.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM team_members tm 
                    WHERE tm.team_id = team_id 
                    AND tm.user_id = auth.uid()
                    AND tm.role IN ('admin', 'manager')
                )
            )
        )
    );

CREATE POLICY "team_members_update_policy" ON team_members
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = team_id 
            AND (
                teams.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM team_members tm 
                    WHERE tm.team_id = team_id 
                    AND tm.user_id = auth.uid()
                    AND tm.role IN ('admin', 'manager')
                )
            )
        )
    );

CREATE POLICY "team_members_delete_policy" ON team_members
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = team_id 
            AND (
                teams.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM team_members tm 
                    WHERE tm.team_id = team_id 
                    AND tm.user_id = auth.uid()
                    AND tm.role IN ('admin', 'manager')
                )
            )
        )
    ); 