-- Drop existing view if exists
DROP VIEW IF EXISTS public.user_profiles;

-- Create a more comprehensive user profiles view
CREATE OR REPLACE VIEW public.user_profiles AS
SELECT 
    id,
    email,
    raw_user_meta_data,
    COALESCE(raw_user_meta_data->>'full_name', email) as display_name,
    updated_at,
    created_at
FROM auth.users;

-- Grant access to the view
GRANT SELECT ON public.user_profiles TO authenticated;

-- Enable RLS on the view
ALTER VIEW public.user_profiles SECURITY INVOKER;

-- Create foreign key references to user_profiles instead of auth.users
ALTER TABLE team_members DROP CONSTRAINT IF EXISTS team_members_user_id_fkey;
ALTER TABLE team_members
    ADD CONSTRAINT team_members_user_id_fkey
    FOREIGN KEY (user_id)
    REFERENCES auth.users(id)
    ON DELETE CASCADE;

ALTER TABLE team_audit_logs DROP CONSTRAINT IF EXISTS team_audit_logs_user_id_fkey;
ALTER TABLE team_audit_logs
    ADD CONSTRAINT team_audit_logs_user_id_fkey
    FOREIGN KEY (user_id)
    REFERENCES auth.users(id)
    ON DELETE SET NULL;

-- Create policies for team_audit_logs
CREATE POLICY "Users can view audit logs for their teams" ON team_audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM teams
            WHERE id = team_id
            AND (
                created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM team_members
                    WHERE team_id = teams.id
                    AND user_id = auth.uid()
                )
            )
        )
    );

-- Create function to get user profile
CREATE OR REPLACE FUNCTION get_user_profile(user_id uuid)
RETURNS SETOF public.user_profiles
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT * FROM public.user_profiles WHERE id = user_id;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_user_profile(uuid) TO authenticated;

COMMENT ON VIEW public.user_profiles IS 'Public view of user profiles with limited fields';
COMMENT ON FUNCTION get_user_profile(uuid) IS 'Function to safely fetch user profile data'; 