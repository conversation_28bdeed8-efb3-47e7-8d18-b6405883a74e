-- Enable RLS on both tables
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;

-- Create a function to check if user is admin or manager of a team
CREATE OR REPLACE FUNCTION is_team_admin_or_manager(team_id uuid)
RET<PERSON>NS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM team_members
        WHERE team_id = $1
        AND user_id = auth.uid()
        AND role IN ('admin', 'manager')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM team_members
        WHERE user_id = auth.uid()
        AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Teams policies
CREATE POLICY "authenticated users can view teams" ON teams
    FOR SELECT USING (
        auth.uid() IS NOT NULL
    );

CREATE POLICY "admins can create teams" ON teams
    FOR INSERT WITH CHECK (
        is_admin()
    );

CREATE POLICY "team admins and managers can update teams" ON teams
    FOR UPDATE USING (
        is_team_admin_or_manager(id)
    );

CREATE POLICY "team admins can delete teams" ON teams
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM team_members
            WHERE team_id = id
            AND user_id = auth.uid()
            AND role = 'admin'
        )
    );

-- Team members policies
CREATE POLICY "authenticated users can view team members" ON team_members
    FOR SELECT USING (
        auth.uid() IS NOT NULL
    );

CREATE POLICY "team admins and managers can insert team members" ON team_members
    FOR INSERT WITH CHECK (
        is_team_admin_or_manager(team_id)
    );

CREATE POLICY "team admins and managers can update team members" ON team_members
    FOR UPDATE USING (
        is_team_admin_or_manager(team_id)
    );

CREATE POLICY "team admins and managers can delete team members" ON team_members
    FOR DELETE USING (
        is_team_admin_or_manager(team_id)
    ); 