-- Drop existing function first
DROP FUNCTION IF EXISTS public.get_user_profiles(uuid[]);

-- Drop existing view if exists
DROP VIEW IF EXISTS public.user_profiles;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view all user profiles" ON auth.users;

-- Create a more comprehensive user profiles view with <PERSON>LS
CREATE OR REPLACE VIEW public.user_profiles AS
SELECT 
    id,
    email,
    raw_user_meta_data,
    COALESCE(raw_user_meta_data->>'full_name', email) as display_name,
    updated_at,
    created_at
FROM auth.users;

-- Enable RLS on the view
ALTER VIEW public.user_profiles SET (security_invoker = true);

-- Grant access to the view
GRANT SELECT ON public.user_profiles TO authenticated;

-- Create policy for accessing user profiles
CREATE POLICY "Users can view all user profiles"
ON auth.users
FOR SELECT
TO authenticated
USING (
    -- Allow users to see basic info of all users
    -- This is needed for team management and collaboration features
    auth.uid() IS NOT NULL
);

-- Create a more efficient function to get user profiles
CREATE OR REPLACE FUNCTION public.get_user_profiles(user_ids uuid[])
RETURNS TABLE (
    id uuid,
    email varchar(255),
    raw_user_meta_data jsonb,
    display_name text,
    updated_at timestamptz,
    created_at timestamptz
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, auth
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.email::varchar(255),
        u.raw_user_meta_data,
        COALESCE(u.raw_user_meta_data->>'full_name', u.email) as display_name,
        u.updated_at,
        u.created_at
    FROM auth.users u
    WHERE u.id = ANY(user_ids);
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.get_user_profiles(uuid[]) TO authenticated;

-- Enable RLS on auth.users if not already enabled
ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

COMMENT ON VIEW public.user_profiles IS 'Public view of user profiles with limited fields';
COMMENT ON FUNCTION public.get_user_profiles(uuid[]) IS 'Function to safely fetch multiple user profiles data'; 