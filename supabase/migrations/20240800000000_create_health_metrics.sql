-- First, create the function to update timestamps if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create team_audit_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS team_audit_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id),
    action TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_team_audit_logs_team_id ON team_audit_logs(team_id);

-- Enable Row Level Security
ALTER TABLE team_audit_logs ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "audit_logs_policy" ON team_audit_logs;

-- Create policy for team_audit_logs
CREATE POLICY "audit_logs_policy" ON team_audit_logs
    FOR ALL USING (
        auth.uid() IN (
            SELECT created_by FROM teams WHERE id = team_id
        ) OR
        auth.uid() IN (
            SELECT user_id FROM team_members WHERE team_id = team_audit_logs.team_id
        )
    );

-- Create audit function with SECURITY DEFINER to bypass RLS
CREATE OR REPLACE FUNCTION log_team_audit_event()
RETURNS TRIGGER AS $$
DECLARE
    v_team_id UUID;
    v_metric_team_id UUID;
BEGIN
    -- Skip if this is a recursive call from the audit logging itself
    IF current_setting('app.audit_logging', true) = 'true' THEN
        RETURN NULL;
    END IF;

    -- Set the flag to prevent recursion
    PERFORM set_config('app.audit_logging', 'true', true);

    -- Determine the team_id based on the table being audited
    IF TG_TABLE_NAME = 'teams' THEN
        v_team_id := COALESCE(NEW.id, OLD.id);
    ELSIF TG_TABLE_NAME = 'team_health_metric_values' THEN
        -- For health metric values, we need to get the team_id from the parent metric
        SELECT team_id INTO v_team_id
        FROM team_health_metrics
        WHERE id = COALESCE(NEW.team_health_metric_id, OLD.team_health_metric_id);
    ELSE
        v_team_id := COALESCE(NEW.team_id, OLD.team_id);
    END IF;

    -- Insert the audit log
    INSERT INTO team_audit_logs (team_id, user_id, action, details)
    VALUES (
        v_team_id,
        auth.uid(),
        TG_OP,
        jsonb_build_object(
            'old_data', to_jsonb(OLD),
            'new_data', to_jsonb(NEW),
            'table', TG_TABLE_NAME
        )
    );

    -- Reset the flag
    PERFORM set_config('app.audit_logging', 'false', true);

    RETURN NULL;
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Create team_health_metrics table
CREATE TABLE IF NOT EXISTS team_health_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id UUID REFERENCES teams(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    green_threshold NUMERIC NOT NULL,
    yellow_threshold NUMERIC NOT NULL,
    red_threshold NUMERIC NOT NULL,
    unit TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_archived BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    soe TEXT -- Standard Operating Procedure for when metric is red
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_team_health_metrics_team_id ON team_health_metrics(team_id);
CREATE INDEX IF NOT EXISTS idx_team_health_metrics_is_active ON team_health_metrics(is_active);
CREATE INDEX IF NOT EXISTS idx_team_health_metrics_is_archived ON team_health_metrics(is_archived);

-- Enable Row Level Security
ALTER TABLE team_health_metrics ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "team_health_metrics_policy" ON team_health_metrics;

-- Create policy for team_health_metrics
CREATE POLICY "team_health_metrics_policy" ON team_health_metrics
    FOR ALL USING (
        created_by = auth.uid() OR
        auth.uid() IN (
            SELECT user_id FROM team_members WHERE team_id = team_health_metrics.team_id
        )
    );

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS update_team_health_metrics_updated_at ON team_health_metrics;
DROP TRIGGER IF EXISTS team_health_metrics_audit ON team_health_metrics;

-- Create trigger for team_health_metrics table to update updated_at
CREATE TRIGGER update_team_health_metrics_updated_at
    BEFORE UPDATE ON team_health_metrics
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create audit trigger for team_health_metrics
CREATE TRIGGER team_health_metrics_audit
    AFTER INSERT OR UPDATE OR DELETE ON team_health_metrics
    FOR EACH ROW EXECUTE FUNCTION log_team_audit_event();

-- Create function to determine health metric status
CREATE OR REPLACE FUNCTION determine_health_metric_status()
RETURNS TRIGGER AS $$
DECLARE
    v_green_threshold NUMERIC;
    v_yellow_threshold NUMERIC;
    v_red_threshold NUMERIC;
    v_team_id UUID;
BEGIN
    -- Get the thresholds from the parent metric
    SELECT
        green_threshold,
        yellow_threshold,
        red_threshold,
        team_id
    INTO
        v_green_threshold,
        v_yellow_threshold,
        v_red_threshold,
        v_team_id
    FROM team_health_metrics
    WHERE id = NEW.team_health_metric_id;

    -- Determine status based on thresholds
    -- Assuming higher values are better (green > yellow > red)
    IF v_green_threshold > v_yellow_threshold THEN
        -- Higher is better
        IF NEW.value >= v_green_threshold THEN
            NEW.status := 'green';
        ELSIF NEW.value >= v_yellow_threshold THEN
            NEW.status := 'yellow';
        ELSE
            NEW.status := 'red';
        END IF;
    ELSE
        -- Lower is better
        IF NEW.value <= v_green_threshold THEN
            NEW.status := 'green';
        ELSIF NEW.value <= v_yellow_threshold THEN
            NEW.status := 'yellow';
        ELSE
            NEW.status := 'red';
        END IF;
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create team_health_metric_values table
CREATE TABLE IF NOT EXISTS team_health_metric_values (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_health_metric_id UUID REFERENCES team_health_metrics(id) ON DELETE CASCADE,
    value NUMERIC NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('green', 'yellow', 'red')),
    notes TEXT,
    reported_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reported_by UUID REFERENCES auth.users(id)
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_team_health_metric_values_metric_id ON team_health_metric_values(team_health_metric_id);
CREATE INDEX IF NOT EXISTS idx_team_health_metric_values_status ON team_health_metric_values(status);
CREATE INDEX IF NOT EXISTS idx_team_health_metric_values_reported_at ON team_health_metric_values(reported_at);

-- Enable Row Level Security
ALTER TABLE team_health_metric_values ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "team_health_metric_values_policy" ON team_health_metric_values;

-- Create policy for team_health_metric_values
CREATE POLICY "team_health_metric_values_policy" ON team_health_metric_values
    FOR ALL USING (
        reported_by = auth.uid() OR
        auth.uid() IN (
            SELECT tm.user_id
            FROM team_members tm
            JOIN team_health_metrics thm ON tm.team_id = thm.team_id
            WHERE thm.id = team_health_metric_id
        )
    );

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS set_health_metric_status ON team_health_metric_values;
DROP TRIGGER IF EXISTS team_health_metric_values_audit ON team_health_metric_values;

-- Create trigger to automatically set status based on thresholds
CREATE TRIGGER set_health_metric_status
    BEFORE INSERT OR UPDATE ON team_health_metric_values
    FOR EACH ROW
    EXECUTE FUNCTION determine_health_metric_status();

-- Create audit trigger for team_health_metric_values
CREATE TRIGGER team_health_metric_values_audit
    AFTER INSERT OR UPDATE OR DELETE ON team_health_metric_values
    FOR EACH ROW EXECUTE FUNCTION log_team_audit_event();
