-- Fix the foreign key reference for assignee_id in kanban_cards table
-- First, drop the existing foreign key constraint
ALTER TABLE kanban_cards
DROP CONSTRAINT IF EXISTS kanban_cards_assignee_id_fkey;

-- Then, add a new foreign key constraint referencing profiles table
ALTER TABLE kanban_cards
ADD CONSTRAINT kanban_cards_assignee_id_fkey
FOREIGN KEY (assignee_id)
REFERENCES profiles(id)
ON DELETE SET NULL;

-- Create index for assignee_id to improve lookup performance (if it doesn't exist)
DROP INDEX IF EXISTS idx_kanban_cards_assignee;
CREATE INDEX idx_kanban_cards_assignee ON kanban_cards (assignee_id);

-- Add comment explaining the change
COMMENT ON CONSTRAINT kanban_cards_assignee_id_fkey ON kanban_cards IS 'Foreign key reference to profiles table for card assignee';

-- Fix the foreign key reference for assignee_id in kanban_subtasks table
-- First, drop the existing foreign key constraint
ALTER TABLE kanban_subtasks
DROP CONSTRAINT IF EXISTS kanban_subtasks_assignee_id_fkey;

-- Then, add a new foreign key constraint referencing profiles table
ALTER TABLE kanban_subtasks
ADD CONSTRAINT kanban_subtasks_assignee_id_fkey
FOREIGN KEY (assignee_id)
REFERENCES profiles(id)
ON DELETE SET NULL;

-- Create index for assignee_id to improve lookup performance (if it doesn't exist)
DROP INDEX IF EXISTS idx_kanban_subtasks_assignee;
CREATE INDEX idx_kanban_subtasks_assignee ON kanban_subtasks (assignee_id);

-- Add comment explaining the change
COMMENT ON CONSTRAINT kanban_subtasks_assignee_id_fkey ON kanban_subtasks IS 'Foreign key reference to profiles table for subtask assignee'; 