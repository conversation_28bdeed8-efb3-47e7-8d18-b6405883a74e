-- Drop user_profiles view
DROP VIEW IF EXISTS public.user_profiles;

-- Drop audit-related functions and triggers
DROP TRIGGER IF EXISTS team_members_audit ON team_members;
DROP TRIGGER IF EXISTS teams_audit ON teams;
DROP FUNCTION IF EXISTS log_team_audit_event();

-- Drop team_audit_logs table and related policies
DROP POLICY IF EXISTS "Users can view audit logs for their teams" ON team_audit_logs;
DROP POLICY IF EXISTS "audit_logs_policy" ON team_audit_logs;
DROP TABLE IF EXISTS team_audit_logs;

-- Update foreign key references to use profiles table
ALTER TABLE teams DROP CONSTRAINT IF EXISTS teams_created_by_fkey;
ALTER TABLE teams
    ADD CONSTRAINT teams_created_by_fkey
    FOREIGN KEY (created_by)
    REFERENCES profiles(id)
    ON DELETE SET NULL;

ALTER TABLE team_members DROP CONSTRAINT IF EXISTS team_members_user_id_fkey;
ALTER TABLE team_members
    ADD CONSTRAINT team_members_user_id_fkey
    FOREIGN KEY (user_id)
    REFERENCES profiles(id)
    ON DELETE CASCADE;

-- Enable RLS on profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles table
CREATE POLICY "Users can view all profiles"
    ON profiles
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can update their own profile"
    ON profiles
    FOR UPDATE
    TO authenticated
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can delete their own profile"
    ON profiles
    FOR DELETE
    TO authenticated
    USING (auth.uid() = id);

-- Update team policies to use profiles
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT EXISTS (
        SELECT 1
        FROM profiles
        WHERE id = auth.uid()
        AND role = 'admin'
    );
$$;

CREATE OR REPLACE FUNCTION public.is_manager_or_admin()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT EXISTS (
        SELECT 1
        FROM profiles
        WHERE id = auth.uid()
        AND role IN ('admin', 'manager')
    );
$$;

-- Grant necessary permissions
GRANT SELECT ON profiles TO authenticated;
GRANT UPDATE (full_name, avatar_url) ON profiles TO authenticated;

-- Add helpful comments
COMMENT ON TABLE profiles IS 'Public profiles of users with additional details';
COMMENT ON COLUMN profiles.role IS 'User role for system-wide permissions'; 