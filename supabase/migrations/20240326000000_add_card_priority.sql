-- Add priority column to kanban_cards table
ALTER TABLE kanban_cards
ADD COLUMN IF NOT EXISTS priority TEXT NOT NULL DEFAULT 'P1'
CHECK (priority IN ('P1', 'P2', 'P3'));

-- Create index for priority to improve sorting performance
CREATE INDEX IF NOT EXISTS idx_kanban_cards_priority ON kanban_cards (priority);

-- Change assignees array to single assignee_id
ALTER TABLE kanban_cards 
DROP COLUMN IF EXISTS assignees,
ADD COLUMN assignee_id UUID REFERENCES auth.users(id);

-- Create index for assignee_id to improve lookup performance
CREATE INDEX IF NOT EXISTS idx_kanban_cards_assignee ON kanban_cards (assignee_id); 