-- Create triggers to maintain user_team_roles consistency
DROP TRIGGER IF EXISTS team_members_role_change ON team_members;
DROP TRIGGER IF EXISTS teams_role_change ON teams;

-- Create index on team_members for better trigger performance
CREATE INDEX IF NOT EXISTS idx_team_members_team_user_role ON team_members(team_id, user_id, role);

-- <PERSON><PERSON> triggers
CREATE TRIGGER team_members_role_change
    AFTER INSERT OR UPDATE OR DELETE ON team_members
    FOR EACH ROW
    EXECUTE FUNCTION refresh_user_team_roles();

CREATE TRIGGER teams_role_change
    AFTER INSERT OR UPDATE OR DELETE ON teams
    FOR EACH ROW
    EXECUTE FUNCTION refresh_user_team_roles();

-- Do an initial refresh of the materialized view
REFRESH MATERIALIZED VIEW user_team_roles; 