-- Drop existing team policies
DROP POLICY IF EXISTS "teams_policy" ON teams;
DROP POLICY IF EXISTS "team_members_policy" ON team_members;

-- Create new team policies
CREATE POLICY "teams_select_policy" ON teams
    FOR SELECT
    TO authenticated
    USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM team_members WHERE team_id = id AND user_id = auth.uid()
        )
    );

CREATE POLICY "teams_insert_policy" ON teams
    FOR INSERT
    TO authenticated
    WITH CHECK (
        is_manager_or_admin()
    );

CREATE POLICY "teams_update_policy" ON teams
    FOR UPDATE
    TO authenticated
    USING (
        created_by = auth.uid() OR
        (EXISTS (
            SELECT 1 FROM team_members
            WHERE team_id = id
            AND user_id = auth.uid()
            AND role IN ('admin', 'manager')
        ))
    );

CREATE POLICY "teams_delete_policy" ON teams
    FOR DELETE
    TO authenticated
    USING (
        created_by = auth.uid() OR
        (EXISTS (
            SELECT 1 FROM team_members
            WHERE team_id = id
            AND user_id = auth.uid()
            AND role = 'admin'
        ))
    );

-- Create new team members policies
CREATE POLICY "team_members_select_policy" ON team_members
    FOR SELECT
    TO authenticated
    USING (
        user_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM teams WHERE id = team_id AND created_by = auth.uid()
        )
    );

CREATE POLICY "team_members_insert_policy" ON team_members
    FOR INSERT
    TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM teams
            WHERE id = team_id
            AND (
                created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM team_members tm
                    WHERE tm.team_id = team_id
                    AND tm.user_id = auth.uid()
                    AND tm.role IN ('admin', 'manager')
                )
            )
        )
    );

CREATE POLICY "team_members_update_policy" ON team_members
    FOR UPDATE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teams
            WHERE id = team_id
            AND (
                created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM team_members tm
                    WHERE tm.team_id = team_id
                    AND tm.user_id = auth.uid()
                    AND tm.role IN ('admin', 'manager')
                )
            )
        )
    );

CREATE POLICY "team_members_delete_policy" ON team_members
    FOR DELETE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teams
            WHERE id = team_id
            AND (
                created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM team_members tm
                    WHERE tm.team_id = team_id
                    AND tm.user_id = auth.uid()
                    AND tm.role IN ('admin', 'manager')
                )
            )
        )
    ); 