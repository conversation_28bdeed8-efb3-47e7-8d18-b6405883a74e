-- Add indexes for better performance with pagination and ordering
CREATE INDEX IF NOT EXISTS idx_kanban_comments_card_id_created_at ON kanban_comments(card_id, created_at);

-- Add a function to extract URLs from comment content
CREATE OR REPLACE FUNCTION extract_urls(content text)
RETURNS text[] AS $$
DECLARE
    url_regex text := 'https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)';
    matches text[];
BEGIN
    SELECT array_agg(match[1])
    INTO matches
    FROM regexp_matches(content, url_regex, 'g') AS match;
    RETURN matches;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Add column for storing extracted URLs
ALTER TABLE kanban_comments
ADD COLUMN IF NOT EXISTS urls text[] GENERATED ALWAYS AS (extract_urls(content)) STORED;

-- Add index for URL search
CREATE INDEX IF NOT EXISTS idx_kanban_comments_urls ON kanban_comments USING gin(urls); 