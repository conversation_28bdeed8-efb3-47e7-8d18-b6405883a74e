-- Drop existing view if exists
DROP VIEW IF EXISTS public.user_profiles;

-- Create a more comprehensive user profiles view
CREATE OR REPLACE VIEW public.user_profiles AS
SELECT 
    id,
    email,
    raw_user_meta_data,
    COALESCE(raw_user_meta_data->>'full_name', email) as display_name,
    updated_at,
    created_at
FROM auth.users;

-- Grant access to the view
GRANT SELECT ON public.user_profiles TO authenticated;

-- Create foreign key references to auth.users
ALTER TABLE team_members DROP CONSTRAINT IF EXISTS team_members_user_id_fkey;
ALTER TABLE team_members
    ADD CONSTRAINT team_members_user_id_fkey
    FOREIGN KEY (user_id)
    REFERENCES auth.users(id)
    ON DELETE CASCADE;

ALTER TABLE team_audit_logs DROP CONSTRAINT IF EXISTS team_audit_logs_user_id_fkey;
ALTER TABLE team_audit_logs
    ADD CONSTRAINT team_audit_logs_user_id_fkey
    FOREIGN KEY (user_id)
    REFERENCES auth.users(id)
    ON DELETE SET NULL;

-- Create policies for team_audit_logs if not exists
DROP POLICY IF EXISTS "Users can view audit logs for their teams" ON team_audit_logs;
CREATE POLICY "Users can view audit logs for their teams" ON team_audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM teams
            WHERE id = team_id
            AND (
                created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM team_members
                    WHERE team_id = teams.id
                    AND user_id = auth.uid()
                )
            )
        )
    );

-- Create function to get user profile
CREATE OR REPLACE FUNCTION public.get_user_profile(user_id uuid)
RETURNS TABLE (
    id uuid,
    email text,
    raw_user_meta_data jsonb,
    display_name text,
    updated_at timestamptz,
    created_at timestamptz
)
LANGUAGE sql
SECURITY DEFINER
SET search_path = public, auth
AS $$
    SELECT 
        u.id,
        u.email,
        u.raw_user_meta_data,
        COALESCE(u.raw_user_meta_data->>'full_name', u.email) as display_name,
        u.updated_at,
        u.created_at
    FROM auth.users u
    WHERE u.id = user_id;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.get_user_profile(uuid) TO authenticated;

-- Enable RLS on team_audit_logs if not already enabled
ALTER TABLE team_audit_logs ENABLE ROW LEVEL SECURITY;

COMMENT ON VIEW public.user_profiles IS 'Public view of user profiles with limited fields';
COMMENT ON FUNCTION public.get_user_profile(uuid) IS 'Function to safely fetch user profile data'; 