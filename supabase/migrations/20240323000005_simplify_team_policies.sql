-- Drop all existing policies
DROP POLICY IF EXISTS "teams_select_policy" ON teams;
DROP POLICY IF EXISTS "teams_insert_policy" ON teams;
DROP POLICY IF EXISTS "teams_update_policy" ON teams;
DROP POLICY IF EXISTS "teams_delete_policy" ON teams;
DROP POLICY IF EXISTS "team_members_select_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_insert_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_update_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_delete_policy" ON team_members;

-- Drop materialized view and related objects
DROP MATERIALIZED VIEW IF EXISTS user_team_roles;
DROP FUNCTION IF EXISTS refresh_user_team_roles() CASCADE;
DROP FUNCTION IF EXISTS check_team_member_role(uuid, text[]);

-- Create function to check team access
CREATE OR REPLACE FUNCTION has_team_access(team_id uuid)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM teams t
        WHERE t.id = team_id
        AND (
            t.created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM team_members tm
                WHERE tm.team_id = t.id
                AND tm.user_id = auth.uid()
            )
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check team admin access
CREATE OR REPLACE FUNCTION has_team_admin_access(team_id uuid)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM teams t
        WHERE t.id = team_id
        AND (
            t.created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM team_members tm
                WHERE tm.team_id = t.id
                AND tm.user_id = auth.uid()
                AND tm.role IN ('admin', 'manager')
            )
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Teams policies
CREATE POLICY "teams_select_policy" ON teams
    FOR SELECT USING (
        created_by = auth.uid() OR
        has_team_access(id)
    );

CREATE POLICY "teams_insert_policy" ON teams
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

CREATE POLICY "teams_update_policy" ON teams
    FOR UPDATE USING (
        created_by = auth.uid() OR
        has_team_admin_access(id)
    );

CREATE POLICY "teams_delete_policy" ON teams
    FOR DELETE USING (
        created_by = auth.uid() OR
        has_team_admin_access(id)
    );

-- Team members policies
CREATE POLICY "team_members_select_policy" ON team_members
    FOR SELECT USING (
        user_id = auth.uid() OR
        has_team_access(team_id)
    );

CREATE POLICY "team_members_insert_policy" ON team_members
    FOR INSERT WITH CHECK (
        has_team_admin_access(team_id)
    );

CREATE POLICY "team_members_update_policy" ON team_members
    FOR UPDATE USING (
        has_team_admin_access(team_id)
    );

CREATE POLICY "team_members_delete_policy" ON team_members
    FOR DELETE USING (
        has_team_admin_access(team_id)
    ); 