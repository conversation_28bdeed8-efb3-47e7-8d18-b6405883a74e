-- First, drop all existing policies and functions
DROP POLICY IF EXISTS "teams_select_policy" ON teams;
DROP POLICY IF EXISTS "teams_insert_policy" ON teams;
DROP POLICY IF EXISTS "teams_update_policy" ON teams;
DROP POLICY IF EXISTS "teams_delete_policy" ON teams;
DROP POLICY IF EXISTS "team_members_select_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_insert_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_update_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_delete_policy" ON team_members;
DROP FUNCTION IF EXISTS check_team_member_role(uuid, text[]);

-- Drop existing view if exists
DROP MATERIALIZED VIEW IF EXISTS user_team_roles;

-- Create materialized view for user team roles
CREATE MATERIALIZED VIEW user_team_roles AS
SELECT DISTINCT
    tm.team_id,
    tm.user_id,
    tm.role,
    t.created_by = tm.user_id as is_creator
FROM team_members tm
JOIN teams t ON t.id = tm.team_id;

-- Create unique index required for concurrent refresh
CREATE UNIQUE INDEX idx_user_team_roles_unique ON user_team_roles(team_id, user_id);

-- Create additional indexes for performance
CREATE INDEX idx_user_team_roles_user ON user_team_roles(user_id);

-- Create base access policies first
CREATE POLICY "teams_select_policy" ON teams
    FOR SELECT USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM team_members 
            WHERE team_id = teams.id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "teams_insert_policy" ON teams
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

CREATE POLICY "teams_update_policy" ON teams
    FOR UPDATE USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM user_team_roles
            WHERE team_id = id
            AND user_id = auth.uid()
            AND (role IN ('admin', 'manager') OR is_creator)
        )
    );

CREATE POLICY "teams_delete_policy" ON teams
    FOR DELETE USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM user_team_roles
            WHERE team_id = id
            AND user_id = auth.uid()
            AND (role = 'admin' OR is_creator)
        )
    );

-- Simplified team members policies
CREATE POLICY "team_members_select_policy" ON team_members
    FOR SELECT USING (
        user_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM user_team_roles
            WHERE team_id = team_members.team_id
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "team_members_insert_policy" ON team_members
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_team_roles
            WHERE team_id = team_id
            AND user_id = auth.uid()
            AND (role IN ('admin', 'manager') OR is_creator)
        )
    );

CREATE POLICY "team_members_update_policy" ON team_members
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM user_team_roles
            WHERE team_id = team_id
            AND user_id = auth.uid()
            AND (role IN ('admin', 'manager') OR is_creator)
        )
    );

CREATE POLICY "team_members_delete_policy" ON team_members
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM user_team_roles
            WHERE team_id = team_id
            AND user_id = auth.uid()
            AND (role IN ('admin', 'manager') OR is_creator)
        )
    );

-- Create a function to refresh user roles
CREATE OR REPLACE FUNCTION refresh_user_team_roles()
RETURNS TRIGGER AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY user_team_roles;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql; 