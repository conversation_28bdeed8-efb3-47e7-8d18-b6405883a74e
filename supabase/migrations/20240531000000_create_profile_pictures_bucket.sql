   -- Drop existing policies to avoid conflicts
   DROP POLICY IF EXISTS "Authenticated users can view any profile picture" ON storage.objects;
   DROP POLICY IF EXISTS "Users can upload their own avatar" ON storage.objects;
   DROP POLICY IF EXISTS "Users can update their own avatar" ON storage.objects;
   DROP POLICY IF EXISTS "Users can delete their own avatar" ON storage.objects;

   -- Set up security policies
   -- Allow authenticated users to view any profile picture
   CREATE POLICY "Authenticated users can view any profile picture" 
   ON storage.objects FOR SELECT
   TO authenticated
   USING (bucket_id = 'profile-pictures');

   -- Allow authenticated users to upload their own avatar
   CREATE POLICY "Users can upload their own avatar" 
   ON storage.objects FOR INSERT 
   TO authenticated
   WITH CHECK (
     bucket_id = 'profile-pictures' AND 
     (storage.foldername(name))[1] LIKE auth.uid()::text || '%'
   );

   -- Allow users to update/delete their own avatar
   CREATE POLICY "Users can update their own avatar" 
   ON storage.objects FOR UPDATE
   TO authenticated 
   USING (
     bucket_id = 'profile-pictures' AND 
     (storage.foldername(name))[1] LIKE auth.uid()::text || '%'
   );

   CREATE POLICY "Users can delete their own avatar" 
   ON storage.objects FOR DELETE
   TO authenticated 
   USING (
     bucket_id = 'profile-pictures' AND 
     (storage.foldername(name))[1] LIKE auth.uid()::text || '%'
   );