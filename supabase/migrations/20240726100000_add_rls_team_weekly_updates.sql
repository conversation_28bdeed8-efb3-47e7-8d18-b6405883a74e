-- Enable Row Level Security on the team_weekly_updates table
ALTER TABLE public.team_weekly_updates ENABLE ROW LEVEL SECURITY;

-- Allow read access for admin users
DROP POLICY IF EXISTS "Allow admin users to read all team weekly updates" ON public.team_weekly_updates;
CREATE POLICY "Allow admin users to read all team weekly updates"
ON public.team_weekly_updates
FOR SELECT
USING (
    (SELECT role FROM public.profiles WHERE id = auth.uid()) = 'admin'
);

-- Allow read access for team members to their own team's updates
DROP POLICY IF EXISTS "Allow team members to read their team weekly updates" ON public.team_weekly_updates;
CREATE POLICY "Allow team members to read their team weekly updates"
ON public.team_weekly_updates
FOR SELECT
USING (
    team_id IN (SELECT team_id FROM public.team_members WHERE user_id = auth.uid())
);

-- Note: By default, if no policies match, access is denied.
-- Consider adding policies for INSERT, UPDATE, DELETE operations if needed.
-- For example, you might want team members to be able to INSERT updates for their own team. 

create policy "Allow insert from edge functions"
on team_weekly_updates
for insert
to public
with check (true);