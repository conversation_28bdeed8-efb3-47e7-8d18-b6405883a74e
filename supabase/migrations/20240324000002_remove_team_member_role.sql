-- First drop all dependent policies
DROP POLICY IF EXISTS "Ad<PERSON> can create teams" ON teams;
DROP POLICY IF EXISTS "Team admins can update their teams" ON teams;
DROP POLICY IF EXISTS "Team admins can delete their teams" ON teams;
DROP POLICY IF EXISTS "Admins and managers can manage team members" ON team_members;
DROP POLICY IF EXISTS "team admins can delete teams" ON teams;
DROP POLICY IF EXISTS "teams_update_policy" ON teams;
DROP POLICY IF EXISTS "teams_delete_policy" ON teams;
DROP POLICY IF EXISTS "team_members_insert_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_update_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_delete_policy" ON team_members;

-- Remove role column from team_members
ALTER TABLE team_members DROP COLUMN role;

-- Update team policies to use profile roles instead
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT EXISTS (
        SELECT 1
        FROM profiles
        WHERE id = auth.uid()
        AND role = 'admin'
    );
$$;

CREATE OR REPLACE FUNCTION public.is_manager_or_admin()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT EXISTS (
        SELECT 1
        FROM profiles
        WHERE id = auth.uid()
        AND role IN ('admin', 'manager')
    );
$$;

-- Update team policies
CREATE POLICY "teams_update_policy" ON teams
    FOR UPDATE
    TO authenticated
    USING (
        created_by = auth.uid() OR
        (EXISTS (
            SELECT 1 FROM team_members tm
            JOIN profiles p ON p.id = tm.user_id
            WHERE tm.team_id = teams.id
            AND tm.user_id = auth.uid()
            AND p.role IN ('admin', 'manager')
        ))
    );

CREATE POLICY "teams_delete_policy" ON teams
    FOR DELETE
    TO authenticated
    USING (
        created_by = auth.uid() OR
        (EXISTS (
            SELECT 1 FROM team_members tm
            JOIN profiles p ON p.id = tm.user_id
            WHERE tm.team_id = teams.id
            AND tm.user_id = auth.uid()
            AND p.role = 'admin'
        ))
    );

-- Update team members policies
CREATE POLICY "team_members_insert_policy" ON team_members
    FOR INSERT
    TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = team_id
            AND (
                t.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM team_members tm
                    JOIN profiles p ON p.id = tm.user_id
                    WHERE tm.team_id = team_id
                    AND tm.user_id = auth.uid()
                    AND p.role IN ('admin', 'manager')
                )
            )
        )
    );

CREATE POLICY "team_members_update_policy" ON team_members
    FOR UPDATE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = team_id
            AND (
                t.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM team_members tm
                    JOIN profiles p ON p.id = tm.user_id
                    WHERE tm.team_id = team_id
                    AND tm.user_id = auth.uid()
                    AND p.role IN ('admin', 'manager')
                )
            )
        )
    );

CREATE POLICY "team_members_delete_policy" ON team_members
    FOR DELETE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = team_id
            AND (
                t.created_by = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM team_members tm
                    JOIN profiles p ON p.id = tm.user_id
                    WHERE tm.team_id = team_id
                    AND tm.user_id = auth.uid()
                    AND p.role IN ('admin', 'manager')
                )
            )
        )
    ); 