-- First, drop all existing policies and functions
DROP POLICY IF EXISTS "teams_select_policy" ON teams;
DROP POLICY IF EXISTS "teams_insert_policy" ON teams;
DROP POLICY IF EXISTS "teams_update_policy" ON teams;
DROP POLICY IF EXISTS "teams_delete_policy" ON teams;
DROP POLICY IF EXISTS "team_members_select_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_insert_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_update_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_delete_policy" ON team_members;
DROP FUNCTION IF EXISTS check_team_member_role(uuid, text[]);

-- Create base access policies first
CREATE POLICY "teams_select_policy" ON teams
    FOR SELECT USING (
        created_by = auth.uid() OR
        id IN (
            SELECT team_id 
            FROM team_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "teams_insert_policy" ON teams
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

CREATE POLICY "teams_update_policy" ON teams
    FOR UPDATE USING (
        created_by = auth.uid() OR
        id IN (
            SELECT team_id 
            FROM team_members 
            WHERE user_id = auth.uid()
            AND role IN ('admin', 'manager')
        )
    );

CREATE POLICY "teams_delete_policy" ON teams
    FOR DELETE USING (
        created_by = auth.uid() OR
        id IN (
            SELECT team_id 
            FROM team_members 
            WHERE user_id = auth.uid()
            AND role = 'admin'
        )
    );

-- Team members policies - Using direct conditions
CREATE POLICY "team_members_select_policy" ON team_members
    FOR SELECT USING (
        -- User can see their own membership
        user_id = auth.uid() OR
        -- Team creator can see all members
        team_id IN (
            SELECT id 
            FROM teams 
            WHERE created_by = auth.uid()
        ) OR
        -- Team admins and managers can see members
        team_id IN (
            SELECT team_id 
            FROM team_members 
            WHERE user_id = auth.uid()
            AND role IN ('admin', 'manager')
        )
    );

CREATE POLICY "team_members_insert_policy" ON team_members
    FOR INSERT WITH CHECK (
        -- Team creator can add members
        team_id IN (
            SELECT id 
            FROM teams 
            WHERE created_by = auth.uid()
        ) OR
        -- Admins and managers can add members
        team_id IN (
            SELECT team_id 
            FROM team_members 
            WHERE user_id = auth.uid()
            AND role IN ('admin', 'manager')
        )
    );

CREATE POLICY "team_members_update_policy" ON team_members
    FOR UPDATE USING (
        -- Team creator can update members
        team_id IN (
            SELECT id 
            FROM teams 
            WHERE created_by = auth.uid()
        ) OR
        -- Admins and managers can update members
        team_id IN (
            SELECT team_id 
            FROM team_members 
            WHERE user_id = auth.uid()
            AND role IN ('admin', 'manager')
        )
    );

CREATE POLICY "team_members_delete_policy" ON team_members
    FOR DELETE USING (
        -- Team creator can delete members
        team_id IN (
            SELECT id 
            FROM teams 
            WHERE created_by = auth.uid()
        ) OR
        -- Admins and managers can delete members
        team_id IN (
            SELECT team_id 
            FROM team_members 
            WHERE user_id = auth.uid()
            AND role IN ('admin', 'manager')
        )
    ); 