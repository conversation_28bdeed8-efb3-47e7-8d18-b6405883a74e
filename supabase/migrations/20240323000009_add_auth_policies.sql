-- Enable access to auth.users for authenticated users
CREATE POLICY "Allow authenticated users to read basic user info"
    ON auth.users
    FOR SELECT
    USING (
        auth.uid() IS NOT NULL
    );

-- Create a view for safe user data access
CREATE OR REPLACE VIEW public.user_profiles AS
SELECT 
    id,
    email,
    raw_user_meta_data
FROM auth.users;

-- Grant access to the view
GRANT SELECT ON public.user_profiles TO authenticated;

-- Update your teams query to use the view instead
COMMENT ON VIEW public.user_profiles IS 'Safe view for accessing basic user information'; 