-- Add is_system column to kanban_comments table
ALTER TABLE kanban_comments
ADD COLUMN IF NOT EXISTS is_system BOOLEAN DEFAULT false;

-- Create index for better performance when querying system comments
CREATE INDEX IF NOT EXISTS idx_kanban_comments_is_system ON kanban_comments(is_system);

-- Update RLS policies to prevent editing/deletion of system comments
DROP POLICY IF EXISTS "Users can soft delete their own comments" ON kanban_comments;
CREATE POLICY "Users can soft delete their own comments"
ON kanban_comments
FOR UPDATE
USING (auth.uid() = user_id AND deleted_at IS NULL AND is_system = false)
WITH CHECK (auth.uid() = user_id AND is_system = false);

-- Update update policy to prevent editing system comments
DROP POLICY IF EXISTS "Users can update their own comments" ON kanban_comments;
CREATE POLICY "Users can update their own comments"
ON kanban_comments
FOR UPDATE
USING (auth.uid() = user_id AND is_system = false)
WITH CHECK (auth.uid() = user_id AND is_system = false); 