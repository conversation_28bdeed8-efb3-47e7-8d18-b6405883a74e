-- First, drop all existing policies again to ensure clean slate
DROP POLICY IF EXISTS "teams_select_policy" ON teams;
DROP POLICY IF EXISTS "teams_insert_policy" ON teams;
DROP POLICY IF EXISTS "teams_update_policy" ON teams;
DROP POLICY IF EXISTS "teams_delete_policy" ON teams;
DROP POLICY IF EXISTS "team_members_select_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_insert_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_update_policy" ON team_members;
DROP POLICY IF EXISTS "team_members_delete_policy" ON team_members;

-- Create a function to check team member role
CREATE OR REPLACE FUNCTION check_team_member_role(team_id uuid, required_roles text[])
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM team_members 
        WHERE team_members.team_id = $1 
        AND team_members.user_id = auth.uid()
        AND team_members.role = ANY($2)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Teams policies - Simplified
CREATE POLICY "teams_select_policy" ON teams
    FOR SELECT USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM team_members 
            WHERE team_members.team_id = teams.id 
            AND team_members.user_id = auth.uid()
        )
    );

CREATE POLICY "teams_insert_policy" ON teams
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

CREATE POLICY "teams_update_policy" ON teams
    FOR UPDATE USING (
        created_by = auth.uid() OR
        check_team_member_role(id, ARRAY['admin', 'manager'])
    );

CREATE POLICY "teams_delete_policy" ON teams
    FOR DELETE USING (
        created_by = auth.uid() OR
        check_team_member_role(id, ARRAY['admin'])
    );

-- Team members policies - Simplified
CREATE POLICY "team_members_select_policy" ON team_members
    FOR SELECT USING (
        user_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = team_members.team_id 
            AND teams.created_by = auth.uid()
        )
    );

CREATE POLICY "team_members_insert_policy" ON team_members
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = team_id 
            AND teams.created_by = auth.uid()
        ) OR
        check_team_member_role(team_id, ARRAY['admin', 'manager'])
    );

CREATE POLICY "team_members_update_policy" ON team_members
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = team_id 
            AND teams.created_by = auth.uid()
        ) OR
        check_team_member_role(team_id, ARRAY['admin', 'manager'])
    );

CREATE POLICY "team_members_delete_policy" ON team_members
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = team_id 
            AND teams.created_by = auth.uid()
        ) OR
        check_team_member_role(team_id, ARRAY['admin', 'manager'])
    ); 