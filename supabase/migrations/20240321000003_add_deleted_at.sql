-- Add deleted_at column to kanban_comments table
ALTER TABLE kanban_comments
ADD COLUMN IF NOT EXISTS deleted_at timestamptz;

-- Add index for better performance when querying deleted comments
CREATE INDEX IF NOT EXISTS idx_kanban_comments_deleted_at ON kanban_comments(deleted_at);

-- Update RLS policies to handle soft deletion
CREATE POLICY "Users can soft delete their own comments"
ON kanban_comments
FOR UPDATE
USING (auth.uid() = user_id AND deleted_at IS NULL)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Everyone can view non-deleted comments"
ON kanban_comments
FOR SELECT
USING (deleted_at IS NULL OR auth.uid() = user_id); 