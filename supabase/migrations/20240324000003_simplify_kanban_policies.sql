-- Drop all existing kanban card policies
DROP POLICY IF EXISTS "Team members can manage cards" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_update_admin_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_update_member_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_select_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_insert_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_update_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_delete_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_team_member_access" ON kanban_cards;

-- Drop existing functions
DROP FUNCTION IF EXISTS is_admin();
DROP FUNCTION IF EXISTS can_access_team(uuid);

-- Create function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
R<PERSON><PERSON>NS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    is_admin_user boolean;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid()
        AND role = 'admin'
    ) INTO is_admin_user;
    
    -- Log the result for debugging
    RAISE NOTICE 'User % is admin: %', auth.uid(), is_admin_user;
    
    RETURN is_admin_user;
END;
$$;

-- Create function to check team membership
CREATE OR REPLACE FUNCTION can_access_team(check_team_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    has_access boolean;
BEGIN
    SELECT EXISTS (
        SELECT 1 FROM teams t
        LEFT JOIN team_members tm ON tm.team_id = t.id
        WHERE t.id = check_team_id
        AND tm.user_id = auth.uid()
    ) INTO has_access;
    
    -- Log the result for debugging
    RAISE NOTICE 'User % access to team %: %', auth.uid(), check_team_id, has_access;
    
    RETURN has_access OR is_admin();
END;
$$;

-- Create separate policies for different operations
CREATE POLICY "kanban_cards_select_policy" ON kanban_cards
    FOR SELECT
    TO authenticated
    USING (
        can_access_team(kanban_cards.team_id)
    );

CREATE POLICY "kanban_cards_insert_policy" ON kanban_cards
    FOR INSERT
    TO authenticated
    WITH CHECK (
        can_access_team(kanban_cards.team_id)
    );

CREATE POLICY "kanban_cards_update_policy" ON kanban_cards
    FOR UPDATE
    TO authenticated
    USING (
        can_access_team(kanban_cards.team_id)
    );

CREATE POLICY "kanban_cards_delete_policy" ON kanban_cards
    FOR DELETE
    TO authenticated
    USING (
        can_access_team(kanban_cards.team_id)
    );

-- Ensure RLS is enabled
ALTER TABLE kanban_cards ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions
GRANT ALL ON kanban_cards TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION can_access_team(uuid) TO authenticated; 