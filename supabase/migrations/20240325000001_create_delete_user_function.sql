-- Function to delete all user data
CREATE OR REPLACE FUNCTION delete_user_data(user_id_to_delete UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_team_ids UUID[];
BEGIN
    -- Check if the executing user is an admin
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Only administrators can delete user data';
    END IF;

    -- Get all teams where the user is the only member or creator
    SELECT ARRAY_AGG(DISTINCT t.id)
    INTO v_team_ids
    FROM teams t
    LEFT JOIN team_members tm ON tm.team_id = t.id
    WHERE t.created_by = user_id_to_delete
    OR (
        tm.user_id = user_id_to_delete
        AND NOT EXISTS (
            SELECT 1
            FROM team_members tm2
            WHERE tm2.team_id = t.id
            AND tm2.user_id != user_id_to_delete
        )
    );

    -- Begin cascading deletion
    -- Delete team memberships
    DELETE FROM team_members
    WHERE user_id = user_id_to_delete;

    -- Delete teams where user was the only member
    IF v_team_ids IS NOT NULL THEN
        -- Delete all kanban subtasks for these teams
        DELETE FROM kanban_subtasks
        WHERE card_id IN (
            SELECT id FROM kanban_cards
            WHERE team_id = ANY(v_team_ids)
        );

        -- Delete all kanban cards
        DELETE FROM kanban_cards
        WHERE team_id = ANY(v_team_ids);

        -- Delete all kanban columns
        DELETE FROM kanban_columns
        WHERE team_id = ANY(v_team_ids);

        -- Finally delete the teams
        DELETE FROM teams
        WHERE id = ANY(v_team_ids);
    END IF;

    -- Delete user's profile
    DELETE FROM profiles
    WHERE id = user_id_to_delete;

    -- Delete from auth.users (this will cascade to identities and other auth-related tables)
    DELETE FROM auth.users
    WHERE id = user_id_to_delete;

    -- Raise notice for logging
    RAISE NOTICE 'Successfully deleted all data for user %', user_id_to_delete;

EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise
        RAISE NOTICE 'Error deleting user data: %', SQLERRM;
        RAISE;
END;
$$;

-- Grant execute permission to authenticated users (but function will still check for admin role)
GRANT EXECUTE ON FUNCTION delete_user_data(UUID) TO authenticated;

-- Example usage:
-- SELECT delete_user_data('1da64d7b-cc5f-4e3b-9963-014f85f0d9c9'); 