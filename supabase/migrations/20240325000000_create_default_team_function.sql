-- Function to create default team and cards for new users
CREATE OR REPLACE FUNCTION create_default_user_team(p_user_id uuid DEFAULT NULL)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, auth
AS $$
DECLARE
    v_team_id uuid;
    v_column_id uuid;
    v_card_id uuid;
    v_user_id uuid;
    v_user_fullname text;
    v_start_time timestamptz;
BEGIN
    v_start_time := clock_timestamp();
    
    -- Use provided user_id if available, otherwise use authenticated user
    v_user_id := COALESCE(p_user_id, auth.uid());
    RAISE LOG 'Starting create_default_user_team() for user: %', v_user_id;

    -- Ensure user is authenticated or test user_id is provided
    IF v_user_id IS NULL THEN
        RAISE LOG 'Authentication failed: user_id is NULL';
        RAISE EXCEPTION 'Not authenticated';
    END IF;



    -- Ensure the executing user has permission (either admin or the user themselves)
    IF p_user_id IS NOT NULL AND auth.uid() IS NOT NULL AND 
       auth.uid() != p_user_id AND 
       NOT EXISTS (
           SELECT 1 FROM profiles 
           WHERE id = auth.uid() AND role = 'admin'
       ) THEN
        RAISE EXCEPTION 'Unauthorized: Only admins can create teams for other users';
    END IF;

    -- Get user's full name from profiles
    BEGIN
        SELECT COALESCE(full_name, email) INTO v_user_fullname
        FROM profiles
        WHERE id = v_user_id;
        
        RAISE LOG 'Retrieved from profiles - user_id: %, fullname: %', v_user_id, v_user_fullname;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error getting profile data: %', SQLERRM;
    END;

    -- If user doesn't exist in profiles, get email from auth.users
    IF v_user_fullname IS NULL THEN
        BEGIN
            SELECT email INTO v_user_fullname
            FROM auth.users
            WHERE id = v_user_id;
            
            RAISE LOG 'Retrieved from auth.users - user_id: %, email: %', v_user_id, v_user_fullname;
        EXCEPTION WHEN OTHERS THEN
            RAISE LOG 'Error getting auth user data: %', SQLERRM;
            RAISE EXCEPTION 'Could not find user information';
        END;
    END IF;

    -- Create default team
    BEGIN
        INSERT INTO teams (name,objective, created_by, status)
        VALUES (INITCAP(
            CASE 
            WHEN position('.' IN v_user_fullname) > 0 
                THEN split_part(v_user_fullname, '.', 1) 
            ELSE v_user_fullname 
            END) || '''s Goals',INITCAP(
            CASE 
            WHEN position('.' IN v_user_fullname) > 0 
                THEN split_part(v_user_fullname, '.', 1) 
            ELSE v_user_fullname 
            END) || '''s Personal Goals', v_user_id, 'Active')
        RETURNING id INTO v_team_id;
        
        RAISE LOG 'Created team - id: %, name: %, user: %', v_team_id, v_user_fullname || '''s Goals', v_user_id;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error creating team: %', SQLERRM;
        RAISE;
    END;

    -- Add user as a team member with admin role
    BEGIN
        INSERT INTO team_members (team_id, user_id)
        VALUES (v_team_id, v_user_id);
    
        RAISE LOG 'Added team member - team: %, user: %', v_team_id, v_user_id;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error adding team member: %', SQLERRM;
        RAISE;
    END;

    -- Create default column
    BEGIN
        INSERT INTO kanban_columns (team_id, title, order_index)
        VALUES (v_team_id, 'This Week''s Goals', 0)
        RETURNING id INTO v_column_id;
        
        RAISE LOG 'Created column - id: %, team: %', v_column_id, v_team_id;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error creating column: %', SQLERRM;
        RAISE;
    END;

    -- Create default card
    BEGIN
        INSERT INTO kanban_cards (team_id, column_id, title, description, order_index, assignee_id)
        VALUES (
            v_team_id,
            v_column_id,
            'Get Access to your teams',
            'Ping you manager to get access to you teams.',
            0,
            v_user_id
        )
        RETURNING id INTO v_card_id;
        
        RAISE LOG 'Created card - id: %, column: %', v_card_id, v_column_id;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error creating card: %', SQLERRM;
        RAISE;
    END;

    -- Create subtasks
    BEGIN
        INSERT INTO kanban_subtasks (card_id, title, order_index)
        VALUES
            (v_card_id, 'Get access to teams', 0),
            (v_card_id, 'Try out product', 1),
            (v_card_id, 'Share feedback', 2);
            
        RAISE LOG 'Created subtasks for card: %', v_card_id;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error creating subtasks: %', SQLERRM;
        RAISE;
    END;

    RAISE LOG 'Successfully completed create_default_user_team() in % ms', 
        EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000;

EXCEPTION
    WHEN OTHERS THEN
        -- Log the complete error state
        RAISE LOG 'Error in create_default_user_team(): SQLSTATE: %, SQLERRM: %, User: %',
            SQLSTATE, SQLERRM, v_user_id;
        RAISE;
END;
$$;

-- Create a trigger to automatically call this function when a user signs up
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, auth
AS $$
DECLARE
    v_start_time timestamptz;
BEGIN
    v_start_time := clock_timestamp();
    RAISE LOG 'Starting handle_new_user() for user: %', NEW.id;

    -- Create user profile first with role "user"
    BEGIN
        INSERT INTO public.profiles (id, email, full_name, role, avatar_url)
        VALUES (
            NEW.id,
            NEW.email,
            split_part(NEW.email, '@', 1),
            'user',
            'https://ui-avatars.com/api/?name=' || split_part(NEW.email, '@', 1)
        )
        ON CONFLICT (id) DO NOTHING;
        RAISE LOG 'Created profile for user: %', NEW.id;
    EXCEPTION WHEN OTHERS THEN
        RAISE LOG 'Error creating profile in handle_new_user(): SQLSTATE: %, SQLERRM: %, User: %',
            SQLSTATE, SQLERRM, NEW.id;
    END;

    -- Check if this is a new confirmed user (magic link signup)
    IF NEW.confirmed_at IS NOT NULL AND 
       (OLD.confirmed_at IS NULL OR OLD IS NULL) AND
       NEW.is_anonymous = false THEN
        
        RAISE LOG 'User confirmed via magic link - id: %, email: %', NEW.id, NEW.email;
        
        BEGIN
             -- Set user role  to admin member
            UPDATE public.profiles
            SET role = 'admin'
            WHERE id = NEW.id;
            RAISE LOG 'Reset user role to member after team creation: %', NEW.id;
            -- Create team and related entities
            PERFORM create_default_user_team(NEW.id);
            RAISE LOG 'Successfully created default team for user: %', NEW.id;
            
            -- Set user role back to regular member
            UPDATE public.profiles
            SET role = 'user'
            WHERE id = NEW.id;
            RAISE LOG 'Reset user role to member after team creation: %', NEW.id;
            
        EXCEPTION WHEN OTHERS THEN
            RAISE LOG 'Error creating default team in handle_new_user(): SQLSTATE: %, SQLERRM: %, User: %',
                SQLSTATE, SQLERRM, NEW.id;
                
            -- Ensure we reset the role to member even if there was an error
            BEGIN
                UPDATE public.profiles
                SET role = 'user'
                WHERE id = NEW.id;
                RAISE LOG 'Reset user role to member after error: %', NEW.id;
            EXCEPTION WHEN OTHERS THEN
                RAISE LOG 'Error resetting user role: SQLSTATE: %, SQLERRM: %, User: %',
                    SQLSTATE, SQLERRM, NEW.id;
            END;
        END;
    ELSE
        RAISE LOG 'Skipping team creation - User: %, Confirmed: %, Anonymous: %',
            NEW.id, NEW.confirmed_at IS NOT NULL, NEW.is_anonymous;
    END IF;

    RAISE LOG 'Completed handle_new_user() in % ms',
        EXTRACT(EPOCH FROM (clock_timestamp() - v_start_time)) * 1000;

    RETURN NEW;
END;
$$;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_user_confirmation ON auth.users;

-- Add the trigger to the auth.users table
CREATE TRIGGER on_user_confirmation
    AFTER INSERT OR UPDATE ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION create_default_user_team(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION handle_new_user() TO authenticated; 