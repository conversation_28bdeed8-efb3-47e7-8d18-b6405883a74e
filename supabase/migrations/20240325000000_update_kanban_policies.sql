-- Drop existing policies
DROP POLICY IF EXISTS "kanban_cards_select_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_insert_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_update_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_delete_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_team_member_access" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_columns_select_policy" ON kanban_columns;
DROP POLICY IF EXISTS "kanban_columns_insert_policy" ON kanban_columns;
DROP POLICY IF EXISTS "kanban_columns_update_policy" ON kanban_columns;
DROP POLICY IF EXISTS "kanban_columns_delete_policy" ON kanban_columns;
DROP POLICY IF EXISTS "kanban_columns_view_policy" ON kanban_columns;
DROP POLICY IF EXISTS "kanban_cards_view_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_move_policy" ON kanban_cards;
DROP POLICY IF EXISTS "kanban_cards_reorder_policy" ON kanban_cards;

-- Create a function to handle card movement and reordering
CREATE OR REPLACE FUNCTION handle_card_movement()
RETURNS TRIGGER AS $$
BEGIN
  -- Preserve existing values for required fields if they're not being explicitly updated
  NEW.title = COALESCE(NEW.title, OLD.title);
  NEW.team_id = COALESCE(NEW.team_id, OLD.team_id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS preserve_card_fields_trigger ON kanban_cards;
CREATE TRIGGER preserve_card_fields_trigger
  BEFORE UPDATE ON kanban_cards
  FOR EACH ROW
  EXECUTE FUNCTION handle_card_movement();

-- Enable RLS
ALTER TABLE kanban_cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE kanban_columns ENABLE ROW LEVEL SECURITY;

-- Policies for kanban_columns

-- View policy for team members and admins
CREATE POLICY "kanban_columns_view_policy" ON kanban_columns
FOR SELECT
USING (
  (
    -- Team members can view columns of teams they belong to
    EXISTS (
      SELECT 1 FROM team_members tm 
      WHERE tm.team_id = kanban_columns.team_id 
      AND tm.user_id = auth.uid()
    )
  ) OR 
  -- Admins can view all columns
  (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND p.role = 'admin'
    )
  )
);

-- Insert policy for team members and admins
CREATE POLICY "kanban_columns_insert_policy" ON kanban_columns
FOR INSERT
WITH CHECK (
  (
    -- Team members can create columns for their teams
    EXISTS (
      SELECT 1 FROM team_members tm 
      WHERE tm.team_id = kanban_columns.team_id 
      AND tm.user_id = auth.uid()
    )
  ) OR 
  -- Admins can create columns for any team
  (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND p.role = 'admin'
    )
  )
);

-- Update policy for team members and admins
CREATE POLICY "kanban_columns_update_policy" ON kanban_columns
FOR UPDATE
USING (
  (
    -- Team members can update columns of their teams
    EXISTS (
      SELECT 1 FROM team_members tm 
      WHERE tm.team_id = kanban_columns.team_id 
      AND tm.user_id = auth.uid()
    )
  ) OR 
  -- Admins can update any column
  (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND p.role = 'admin'
    )
  )
)
WITH CHECK (
  (
    -- Team members can update columns of their teams
    EXISTS (
      SELECT 1 FROM team_members tm 
      WHERE tm.team_id = kanban_columns.team_id 
      AND tm.user_id = auth.uid()
    )
  ) OR 
  -- Admins can update any column
  (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND p.role = 'admin'
    )
  )
);

-- Policies for kanban_cards

-- View policy for team members and admins
CREATE POLICY "kanban_cards_view_policy" ON kanban_cards
FOR SELECT
USING (
  (
    -- Team members can view cards of teams they belong to
    EXISTS (
      SELECT 1 FROM team_members tm 
      WHERE tm.team_id = kanban_cards.team_id 
      AND tm.user_id = auth.uid()
    )
  ) OR 
  -- Admins can view all cards
  (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND p.role = 'admin'
    )
  )
);

-- Insert policy for team members and admins
CREATE POLICY "kanban_cards_insert_policy" ON kanban_cards
FOR INSERT
WITH CHECK (
  (
    -- Team members can create cards for their teams
    EXISTS (
      SELECT 1 FROM team_members tm 
      WHERE tm.team_id = kanban_cards.team_id 
      AND tm.user_id = auth.uid()
    )
  ) OR 
  -- Admins can create cards for any team
  (
    EXISTS (
      SELECT 1 FROM profiles p 
      WHERE p.id = auth.uid() 
      AND p.role = 'admin'
    )
  )
);

-- Update policy for team members and admins
CREATE POLICY "kanban_cards_update_policy" ON kanban_cards
FOR UPDATE
USING (
  (
    -- Team members can update cards if:
    -- 1. They are a member of the team the card belongs to
    EXISTS (
      SELECT 1 FROM team_members tm 
      WHERE tm.team_id = kanban_cards.team_id 
      AND tm.user_id = auth.uid()
    )
    AND
    -- 2. If moving to a different column, they have access to the destination column
    (
      -- Not changing column_id OR have access to destination column
      column_id IS NOT DISTINCT FROM kanban_cards.column_id
      OR 
      EXISTS (
        SELECT 1 FROM kanban_columns kc
        JOIN team_members tm ON tm.team_id = kc.team_id
        WHERE kc.id = column_id
        AND tm.user_id = auth.uid()
      )
    )
  ) OR 
  -- Admins can update any card
  EXISTS (
    SELECT 1 FROM profiles p 
    WHERE p.id = auth.uid() 
    AND p.role = 'admin'
  )
)
WITH CHECK (
  (
    -- Team members can update cards if:
    -- 1. They are a member of the team the card belongs to
    EXISTS (
      SELECT 1 FROM team_members tm 
      WHERE tm.team_id = kanban_cards.team_id 
      AND tm.user_id = auth.uid()
    )
    AND
    -- 2. If moving to a different column, they have access to the destination column
    (
      -- Not changing column_id OR have access to destination column
      column_id IS NOT DISTINCT FROM kanban_cards.column_id
      OR 
      EXISTS (
        SELECT 1 FROM kanban_columns kc
        JOIN team_members tm ON tm.team_id = kc.team_id
        WHERE kc.id = column_id
        AND tm.user_id = auth.uid()
      )
    )
  ) OR 
  -- Admins can update any card
  EXISTS (
    SELECT 1 FROM profiles p 
    WHERE p.id = auth.uid() 
    AND p.role = 'admin'
  )
); 