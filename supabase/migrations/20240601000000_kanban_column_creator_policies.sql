-- Add RLS policies to allow team members to update and delete kanban columns for teams they created
-- Description: This migration adds additional policies to allow team members to update and delete
-- kanban columns for teams they personally created as team creator.

-- Policy to allow team members to update kanban columns for teams they created
CREATE POLICY "Team creators can update kanban columns" ON public.kanban_columns
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1
            FROM teams
            WHERE 
                teams.id = kanban_columns.team_id AND
                teams.created_by = auth.uid()
        )
    );

-- Policy to allow team members to delete kanban columns for teams they created
CREATE POLICY "Team creators can delete kanban columns" ON public.kanban_columns
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1
            FROM teams
            WHERE 
                teams.id = kanban_columns.team_id AND
                teams.created_by = auth.uid()
        )
    );

-- Note: These policies are additive, meaning they work alongside existing policies
-- The existing policies for managers and admins will still be in effect 