teams table
1. Read
   - All authenticated users can read the teams they are part of
   - <PERSON><PERSON> can read all teams
2. Create
   - Only admins can create teams
3. Update
   - All managers can update the teams they are part of
   - OAdmins can update all teams
4. Delete
   - Only admins can delete teams

team_members table
1. Read
   - All authenticated users can read the team_members of the teams they are part of
   - Ad<PERSON> can read all team_members of any team
2. Create
   - All managers can add team members to the teams they are part of
   - <PERSON><PERSON> can add team members to any team
3. Update
   - All managers can update the team_members of the teams they are part of
   - <PERSON><PERSON> can update any team_members
4. Delete
   - All managers can remove team members from the teams they are part of
   - Ad<PERSON> can remove any team members

profiles table
1. Read
   - All authenticated users can read all profiles
2. Create
   - All authenticated users can create their own profiles
   - Ad<PERSON> can create profiles for any user
3. Update
   - All authenticated users can update their own profiles
   - Ad<PERSON> can update any profiles
4. Delete
   - All authenticated users can delete their own profiles
   - <PERSON><PERSON> can delete any profiles

kanban_columns table
1. Read
   - All authenticated users can read all kanban_columns of the teams they are part of
   - <PERSON><PERSON> can read all kanban_columns of any team
2. Create
   - All managers can create kanban_columns for the teams they are part of
   - <PERSON><PERSON> can create kanban_columns for any team
3. Update
   - All managers can update the kanban_columns of the teams they are part of
   - <PERSON><PERSON> can update any kanban_columns
4. Delete
   - All managers can delete the kanban_columns of the teams they are part of
   - Admins can delete any kanban_columns

kanban_cards table
1. Read
   - All authenticated users can read all kanban_cards of the teams they are part of
   - Admins can read all kanban_cards of any team
2. Create
   - All authenticated users can create kanban_cards for the teams they are part of
   - Admins can create kanban_cards for any team
3. Update
   - All authenticated users can update the kanban_cards of the teams they are part of
   - Admins can update any kanban_cards
4. Delete
   - All authenticated users can delete the kanban_cards of the teams they are part of
   - Admins can delete any kanban_cards

kanban_comments table
1. Read
   - All authenticated users can read all kanban_comments of the teams they are part of
   - Admins can read all kanban_comments of any team
2. Create
   - All authenticated users can create kanban_comments for the teams they are part of
   - Admins can create kanban_comments for any team
3. Update
   - All authenticated users can update the kanban_comments they created
4. Delete
   - All authenticated users can delete the kanban_comments they created
   - Admins can delete any kanban_comments

kanban_subtasks table
1. Read
   - All authenticated users can read all kanban_subtasks of the teams they are part of
   - Admins can read all kanban_subtasks of any team
2. Create
   - All authenticated users can create kanban_subtasks for the teams they are part of
   - Admins can create kanban_subtasks for any team
3. Update
   - All authenticated users can update the kanban_subtasks of the teams they are part of
   - Admins can update any kanban_subtasks
4. Delete
   - All authenticated users can delete the kanban_subtasks of the teams they are part of
   - Admins can delete any kanban_subtasks

