| policy_id | schema_name | table_name      | policy_name                                       | command | policy_using                                                                                                                                                                                                                                                                                                                                                                                                                                    | policy_check                                                                                                                                                                                                                                                                                                                                                                                                                   |
| --------- | ----------- | --------------- | ------------------------------------------------- | ------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 32108     | auth        | users           | Allow authenticated users to read basic user info | r       | (auth.uid() IS NOT NULL)                                                                                                                                                                                                                                                                                                                                                                                                                        | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 33356     | auth        | users           | Users can view all user profiles                  | r       | (auth.uid() IS NOT NULL)                                                                                                                                                                                                                                                                                                                                                                                                                        | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 29330     | public      | goals           | Allow authenticated users to insert goals         | a       | null                                                                                                                                                                                                                                                                                                                                                                                                                                            | (auth.role() = 'authenticated'::text)                                                                                                                                                                                                                                                                                                                                                                                          |
| 29329     | public      | goals           | Allow public read access                          | r       | true                                                                                                                                                                                                                                                                                                                                                                                                                                            | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 29331     | public      | goals           | Allow users to update their own goals             | w       | (auth.uid() IN ( SELECT auth.uid() AS uid
   FROM auth.users
  WHERE ((users.email)::text = goals.full_name)))                                                                                                                                                                                                                                                                                                                                  | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45112     | public      | kanban_cards    | Read kanban cards for team members                | r       | (EXISTS ( SELECT 1
   FROM team_members
  WHERE ((team_members.team_id = kanban_cards.team_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL))))                                                                                                                                                                                                                                                                 | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45113     | public      | kanban_cards    | Team members can create kanban cards              | a       | null                                                                                                                                                                                                                                                                                                                                                                                                                                            | (EXISTS ( SELECT 1
   FROM team_members
  WHERE ((team_members.team_id = kanban_cards.team_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL))))                                                                                                                                                                                                                                                |
| 45116     | public      | kanban_cards    | Team members can delete kanban cards              | d       | (EXISTS ( SELECT 1
   FROM team_members
  WHERE ((team_members.team_id = kanban_cards.team_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL))))                                                                                                                                                                                                                                                                 | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45114     | public      | kanban_cards    | Team members can update kanban cards              | w       | (EXISTS ( SELECT 1
   FROM team_members
  WHERE ((team_members.team_id = kanban_cards.team_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL))))                                                                                                                                                                                                                                                                 | (EXISTS ( SELECT 1
   FROM team_members
  WHERE ((team_members.team_id = kanban_cards.team_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL))))                                                                                                                                                                                                                                                |
| 45105     | public      | kanban_columns  | Managers can create kanban columns                | a       | null                                                                                                                                                                                                                                                                                                                                                                                                                                            | ((EXISTS ( SELECT 1
   FROM (team_members
     JOIN profiles ON ((profiles.id = team_members.user_id)))
  WHERE ((team_members.team_id = kanban_columns.team_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL) AND (profiles.role = 'manager'::text)))) OR (EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = 'admin'::text)))))                     |
| 45110     | public      | kanban_columns  | Managers can delete kanban columns                | d       | ((EXISTS ( SELECT 1
   FROM (team_members
     JOIN profiles ON ((profiles.id = team_members.user_id)))
  WHERE ((team_members.team_id = kanban_columns.team_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL) AND (profiles.role = 'manager'::text)))) OR (EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = 'admin'::text)))))                                      | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45107     | public      | kanban_columns  | Managers can update kanban columns                | w       | ((EXISTS ( SELECT 1
   FROM (team_members
     JOIN profiles ON ((profiles.id = team_members.user_id)))
  WHERE ((team_members.team_id = kanban_columns.team_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL) AND (profiles.role = 'manager'::text)))) OR (EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = 'admin'::text)))))                                      | ((EXISTS ( SELECT 1
   FROM (team_members
     JOIN profiles ON ((profiles.id = team_members.user_id)))
  WHERE ((team_members.team_id = kanban_columns.team_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL) AND (profiles.role = 'manager'::text)))) OR (EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.id = auth.uid()) AND (profiles.role = 'admin'::text)))))                     |
| 45104     | public      | kanban_columns  | Read kanban columns for team members              | r       | (EXISTS ( SELECT 1
   FROM team_members
  WHERE ((team_members.team_id = kanban_columns.team_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL))))                                                                                                                                                                                                                                                               | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 50579     | public      | kanban_columns  | Team creators can delete kanban columns           | d       | (EXISTS ( SELECT 1
   FROM teams
  WHERE ((teams.id = kanban_columns.team_id) AND (teams.created_by = auth.uid()))))                                                                                                                                                                                                                                                                                                                            | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 50578     | public      | kanban_columns  | Team creators can update kanban columns           | w       | (EXISTS ( SELECT 1
   FROM teams
  WHERE ((teams.id = kanban_columns.team_id) AND (teams.created_by = auth.uid()))))                                                                                                                                                                                                                                                                                                                            | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 31195     | public      | kanban_columns  | Team members can create columns                   | a       | null                                                                                                                                                                                                                                                                                                                                                                                                                                            | (auth.uid() IN ( SELECT team_members.user_id
   FROM team_members
  WHERE (team_members.team_id = kanban_columns.team_id)))                                                                                                                                                                                                                                                                                                    |
| 77218     | public      | kanban_comments | Users can soft delete their own comments          | w       | ((auth.uid() = user_id) AND (deleted_at IS NULL) AND (is_system = false))                                                                                                                                                                                                                                                                                                                                                                       | ((auth.uid() = user_id) AND (is_system = false))                                                                                                                                                                                                                                                                                                                                                                               |
| 77219     | public      | kanban_comments | Users can update their own comments               | w       | ((auth.uid() = user_id) AND (is_system = false))                                                                                                                                                                                                                                                                                                                                                                                                | ((auth.uid() = user_id) AND (is_system = false))                                                                                                                                                                                                                                                                                                                                                                               |
| 48028     | public      | kanban_comments | admin_full_access_policy                          | *       | is_admin()                                                                                                                                                                                                                                                                                                                                                                                                                                      | is_admin()                                                                                                                                                                                                                                                                                                                                                                                                                     |
| 48030     | public      | kanban_comments | team_members_can_create_comments                  | a       | null                                                                                                                                                                                                                                                                                                                                                                                                                                            | ((EXISTS ( SELECT 1
   FROM (kanban_cards c
     JOIN team_members tm ON ((tm.team_id = c.team_id)))
  WHERE ((c.id = kanban_comments.card_id) AND (tm.user_id = auth.uid())))) AND (auth.uid() = user_id))                                                                                                                                                                                                                    |
| 48029     | public      | kanban_comments | team_members_can_read_comments                    | r       | (EXISTS ( SELECT 1
   FROM (kanban_cards c
     JOIN team_members tm ON ((tm.team_id = c.team_id)))
  WHERE ((c.id = kanban_comments.card_id) AND (tm.user_id = auth.uid()))))                                                                                                                                                                                                                                                                  | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 48032     | public      | kanban_comments | users_can_update_own_comments                     | w       | (auth.uid() = user_id)                                                                                                                                                                                                                                                                                                                                                                                                                          | (auth.uid() = user_id)                                                                                                                                                                                                                                                                                                                                                                                                         |
| 45123     | public      | kanban_subtasks | Read kanban subtasks for team members             | r       | (EXISTS ( SELECT 1
   FROM (kanban_cards
     JOIN team_members ON ((team_members.team_id = kanban_cards.team_id)))
  WHERE ((kanban_cards.id = kanban_subtasks.card_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL))))                                                                                                                                                                                       | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45124     | public      | kanban_subtasks | Team members can create subtasks                  | a       | null                                                                                                                                                                                                                                                                                                                                                                                                                                            | (EXISTS ( SELECT 1
   FROM (kanban_cards
     JOIN team_members ON ((team_members.team_id = kanban_cards.team_id)))
  WHERE ((kanban_cards.id = kanban_subtasks.card_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL))))                                                                                                                                                                      |
| 45128     | public      | kanban_subtasks | Team members can delete subtasks                  | d       | (EXISTS ( SELECT 1
   FROM (kanban_cards
     JOIN team_members ON ((team_members.team_id = kanban_cards.team_id)))
  WHERE ((kanban_cards.id = kanban_subtasks.card_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL))))                                                                                                                                                                                       | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45125     | public      | kanban_subtasks | Team members can update subtasks                  | w       | (EXISTS ( SELECT 1
   FROM (kanban_cards
     JOIN team_members ON ((team_members.team_id = kanban_cards.team_id)))
  WHERE ((kanban_cards.id = kanban_subtasks.card_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL))))                                                                                                                                                                                       | (EXISTS ( SELECT 1
   FROM (kanban_cards
     JOIN team_members ON ((team_members.team_id = kanban_cards.team_id)))
  WHERE ((kanban_cards.id = kanban_subtasks.card_id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL))))                                                                                                                                                                      |
| 45063     | public      | profiles        | profiles_delete_policy                            | d       | ((auth.uid() = id) OR (EXISTS ( SELECT 1
   FROM profiles profiles_1
  WHERE ((profiles_1.id = auth.uid()) AND (profiles_1.role = 'admin'::text)))))                                                                                                                                                                                                                                                                                            | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45061     | public      | profiles        | profiles_insert_policy                            | a       | null                                                                                                                                                                                                                                                                                                                                                                                                                                            | (((auth.uid() = id) AND (EXISTS ( SELECT 1
   FROM auth.users
  WHERE (users.id = auth.uid())))) OR (EXISTS ( SELECT 1
   FROM profiles profiles_1
  WHERE ((profiles_1.id = auth.uid()) AND (profiles_1.role = 'admin'::text)))))                                                                                                                                                                                             |
| 45060     | public      | profiles        | profiles_read_policy                              | r       | (auth.role() = 'authenticated'::text)                                                                                                                                                                                                                                                                                                                                                                                                           | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45062     | public      | profiles        | profiles_update_policy                            | w       | ((auth.uid() = id) OR (EXISTS ( SELECT 1
   FROM profiles profiles_1
  WHERE ((profiles_1.id = auth.uid()) AND (profiles_1.role = 'admin'::text)))))                                                                                                                                                                                                                                                                                            | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45656     | public      | team_members    | team_members_create_policy                        | a       | null                                                                                                                                                                                                                                                                                                                                                                                                                                            | 
CASE get_user_role()
    WHEN 'admin'::text THEN true
    WHEN 'manager'::text THEN (EXISTS ( SELECT 1
       FROM ONLY teams
      WHERE ((teams.id = team_members.team_id) AND ((teams.created_by = auth.uid()) OR (EXISTS ( SELECT 1
               FROM ONLY team_members tm
              WHERE ((tm.team_id = team_members.team_id) AND (tm.user_id = auth.uid()) AND (tm.deleted_at IS NULL))))))))
    ELSE false
END |
| 45658     | public      | team_members    | team_members_delete_policy                        | d       | 
CASE get_user_role()
    WHEN 'admin'::text THEN true
    WHEN 'manager'::text THEN (EXISTS ( SELECT 1
       FROM ONLY teams
      WHERE ((teams.id = team_members.team_id) AND ((teams.created_by = auth.uid()) OR (EXISTS ( SELECT 1
               FROM ONLY team_members tm
              WHERE ((tm.team_id = team_members.team_id) AND (tm.user_id = auth.uid()) AND (tm.deleted_at IS NULL))))))))
    ELSE (user_id = auth.uid())
END | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45655     | public      | team_members    | team_members_read_policy                          | r       | (auth.uid() IS NOT NULL)                                                                                                                                                                                                                                                                                                                                                                                                                        | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45657     | public      | team_members    | team_members_update_policy                        | w       | 
CASE get_user_role()
    WHEN 'admin'::text THEN true
    WHEN 'manager'::text THEN (EXISTS ( SELECT 1
       FROM ONLY teams
      WHERE ((teams.id = team_members.team_id) AND ((teams.created_by = auth.uid()) OR (EXISTS ( SELECT 1
               FROM ONLY team_members tm
              WHERE ((tm.team_id = team_members.team_id) AND (tm.user_id = auth.uid()) AND (tm.deleted_at IS NULL))))))))
    ELSE (user_id = auth.uid())
END | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 43207     | public      | teams           | Team access policy                                | *       | ((created_by = auth.uid()) OR (id IN ( SELECT team_members.team_id
   FROM team_members
  WHERE (team_members.user_id = auth.uid()))))                                                                                                                                                                                                                                                                                                          | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45652     | public      | teams           | teams_create_policy                               | a       | null                                                                                                                                                                                                                                                                                                                                                                                                                                            | (get_user_role() = 'admin'::text)                                                                                                                                                                                                                                                                                                                                                                                              |
| 45654     | public      | teams           | teams_delete_policy                               | d       | (get_user_role() = 'admin'::text)                                                                                                                                                                                                                                                                                                                                                                                                               | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45651     | public      | teams           | teams_read_policy                                 | r       | 
CASE get_user_role()
    WHEN 'admin'::text THEN true
    ELSE ((created_by = auth.uid()) OR (EXISTS ( SELECT 1
       FROM ONLY team_members
      WHERE ((team_members.team_id = team_members.id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL)))))
END                                                                                                                                                      | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 45653     | public      | teams           | teams_update_policy                               | w       | 
CASE get_user_role()
    WHEN 'admin'::text THEN true
    WHEN 'manager'::text THEN ((created_by = auth.uid()) OR (EXISTS ( SELECT 1
       FROM ONLY team_members
      WHERE ((team_members.team_id = team_members.id) AND (team_members.user_id = auth.uid()) AND (team_members.deleted_at IS NULL)))))
    ELSE (created_by = auth.uid())
END                                                                                              | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 77244     | storage     | objects         | Authenticated users can view any profile picture  | r       | (bucket_id = 'profile-pictures'::text)                                                                                                                                                                                                                                                                                                                                                                                                          | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 77247     | storage     | objects         | Users can delete their own avatar                 | d       | ((bucket_id = 'profile-pictures'::text) AND ((storage.foldername(name))[1] ~~ ((auth.uid())::text || '%'::text)))                                                                                                                                                                                                                                                                                                                               | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 77246     | storage     | objects         | Users can update their own avatar                 | w       | ((bucket_id = 'profile-pictures'::text) AND ((storage.foldername(name))[1] ~~ ((auth.uid())::text || '%'::text)))                                                                                                                                                                                                                                                                                                                               | null                                                                                                                                                                                                                                                                                                                                                                                                                           |
| 77245     | storage     | objects         | Users can upload their own avatar                 | a       | null                                                                                                                                                                                                                                                                                                                                                                                                                                            | ((bucket_id = 'profile-pictures'::text) AND ((storage.foldername(name))[1] ~~ ((auth.uid())::text || '%'::text)))                                                                                                                                                                                                                                                                                                              |