| table_schema        | table_name                 | column_name                 | data_type                   | is_nullable | column_default                                     |
| ------------------- | -------------------------- | --------------------------- | --------------------------- | ----------- | -------------------------------------------------- |
| extensions          | pg_stat_statements_info    | dealloc                     | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements_info    | stats_reset                 | timestamp with time zone    | YES         | null                                               |
| public              | goals                      | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| public              | kanban_comments            | is_system                   | boolean                     | YES         | false                                              |
| public              | kanban_subtasks            | id                          | uuid                        | NO          | uuid_generate_v4()                                 |
| public              | kanban_subtasks            | card_id                     | uuid                        | YES         | null                                               |
| public              | kanban_subtasks            | is_completed                | boolean                     | YES         | false                                              |
| public              | kanban_subtasks            | order_index                 | integer                     | NO          | null                                               |
| public              | kanban_subtasks            | created_at                  | timestamp with time zone    | YES         | CURRENT_TIMESTAMP                                  |
| public              | kanban_subtasks            | updated_at                  | timestamp with time zone    | YES         | CURRENT_TIMESTAMP                                  |
| public              | kanban_subtasks            | due_date                    | timestamp with time zone    | YES         | null                                               |
| public              | kanban_subtasks            | assignee_id                 | uuid                        | YES         | null                                               |
| cron                | job_run_details            | jobid                       | bigint                      | YES         | null                                               |
| cron                | job_run_details            | runid                       | bigint                      | NO          | nextval('cron.runid_seq'::regclass)                |
| cron                | job_run_details            | job_pid                     | integer                     | YES         | null                                               |
| cron                | job_run_details            | start_time                  | timestamp with time zone    | YES         | null                                               |
| cron                | job_run_details            | end_time                    | timestamp with time zone    | YES         | null                                               |
| cron                | job                        | jobid                       | bigint                      | NO          | nextval('cron.jobid_seq'::regclass)                |
| cron                | job                        | nodeport                    | integer                     | NO          | inet_server_port()                                 |
| cron                | job                        | active                      | boolean                     | NO          | true                                               |
| public              | team_members               | id                          | uuid                        | NO          | uuid_generate_v4()                                 |
| public              | team_members               | team_id                     | uuid                        | YES         | null                                               |
| public              | team_members               | user_id                     | uuid                        | NO          | null                                               |
| public              | team_members               | created_at                  | timestamp with time zone    | YES         | CURRENT_TIMESTAMP                                  |
| public              | team_members               | updated_at                  | timestamp with time zone    | YES         | CURRENT_TIMESTAMP                                  |
| public              | team_members               | joined_at                   | timestamp with time zone    | YES         | now()                                              |
| public              | team_members               | deleted_at                  | timestamp with time zone    | YES         | null                                               |
| public              | kanban_columns             | id                          | uuid                        | NO          | uuid_generate_v4()                                 |
| public              | kanban_columns             | team_id                     | uuid                        | YES         | null                                               |
| public              | kanban_columns             | order_index                 | integer                     | NO          | null                                               |
| public              | kanban_columns             | created_at                  | timestamp with time zone    | YES         | CURRENT_TIMESTAMP                                  |
| public              | kanban_columns             | updated_at                  | timestamp with time zone    | YES         | CURRENT_TIMESTAMP                                  |
| realtime            | messages_2025_02_21        | payload                     | jsonb                       | YES         | null                                               |
| realtime            | messages_2025_02_21        | private                     | boolean                     | YES         | false                                              |
| realtime            | messages_2025_02_21        | updated_at                  | timestamp without time zone | NO          | now()                                              |
| realtime            | messages_2025_02_21        | inserted_at                 | timestamp without time zone | NO          | now()                                              |
| realtime            | messages_2025_02_21        | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| realtime            | messages_2025_02_23        | payload                     | jsonb                       | YES         | null                                               |
| realtime            | messages_2025_02_23        | private                     | boolean                     | YES         | false                                              |
| realtime            | messages_2025_02_23        | updated_at                  | timestamp without time zone | NO          | now()                                              |
| realtime            | messages_2025_02_23        | inserted_at                 | timestamp without time zone | NO          | now()                                              |
| realtime            | messages_2025_02_23        | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| realtime            | messages_2025_02_25        | payload                     | jsonb                       | YES         | null                                               |
| realtime            | messages_2025_02_25        | private                     | boolean                     | YES         | false                                              |
| realtime            | messages_2025_02_25        | updated_at                  | timestamp without time zone | NO          | now()                                              |
| realtime            | messages_2025_02_25        | inserted_at                 | timestamp without time zone | NO          | now()                                              |
| realtime            | messages_2025_02_25        | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| net                 | http_request_queue         | id                          | bigint                      | NO          | nextval('net.http_request_queue_id_seq'::regclass) |
| net                 | http_request_queue         | headers                     | jsonb                       | NO          | null                                               |
| net                 | http_request_queue         | body                        | bytea                       | YES         | null                                               |
| net                 | http_request_queue         | timeout_milliseconds        | integer                     | NO          | null                                               |
| net                 | \_http_response            | id                          | bigint                      | YES         | null                                               |
| net                 | \_http_response            | status_code                 | integer                     | YES         | null                                               |
| net                 | \_http_response            | headers                     | jsonb                       | YES         | null                                               |
| net                 | \_http_response            | timed_out                   | boolean                     | YES         | null                                               |
| net                 | \_http_response            | created                     | timestamp with time zone    | NO          | now()                                              |
| realtime            | messages_2025_02_22        | payload                     | jsonb                       | YES         | null                                               |
| realtime            | messages_2025_02_22        | private                     | boolean                     | YES         | false                                              |
| realtime            | messages_2025_02_22        | updated_at                  | timestamp without time zone | NO          | now()                                              |
| realtime            | messages_2025_02_22        | inserted_at                 | timestamp without time zone | NO          | now()                                              |
| realtime            | messages_2025_02_22        | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| realtime            | messages_2025_02_24        | payload                     | jsonb                       | YES         | null                                               |
| realtime            | messages_2025_02_24        | private                     | boolean                     | YES         | false                                              |
| realtime            | messages_2025_02_24        | updated_at                  | timestamp without time zone | NO          | now()                                              |
| realtime            | messages_2025_02_24        | inserted_at                 | timestamp without time zone | NO          | now()                                              |
| realtime            | messages_2025_02_24        | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| public              | team_weekly_updates        | id                          | bigint                      | NO          | null                                               |
| public              | team_weekly_updates        | created_at                  | timestamp with time zone    | NO          | now()                                              |
| public              | team_weekly_updates        | team_id                     | uuid                        | YES         | null                                               |
| public              | team_weekly_updates        | start_date                  | timestamp with time zone    | YES         | null                                               |
| public              | team_weekly_updates        | end_date                    | timestamp with time zone    | YES         | null                                               |
| public              | team_weekly_updates        | team_updates                | json                        | YES         | null                                               |
| public              | profiles                   | id                          | uuid                        | NO          | null                                               |
| public              | profiles                   | updated_at                  | timestamp with time zone    | YES         | timezone('utc'::text, now())                       |
| public              | profiles                   | created_at                  | timestamp with time zone    | YES         | timezone('utc'::text, now())                       |
| auth                | mfa_factors                | id                          | uuid                        | NO          | null                                               |
| auth                | mfa_factors                | user_id                     | uuid                        | NO          | null                                               |
| auth                | mfa_factors                | factor_type                 | USER-DEFINED                | NO          | null                                               |
| auth                | mfa_factors                | status                      | USER-DEFINED                | NO          | null                                               |
| auth                | mfa_factors                | created_at                  | timestamp with time zone    | NO          | null                                               |
| auth                | mfa_factors                | updated_at                  | timestamp with time zone    | NO          | null                                               |
| auth                | mfa_factors                | last_challenged_at          | timestamp with time zone    | YES         | null                                               |
| auth                | mfa_factors                | web_authn_credential        | jsonb                       | YES         | null                                               |
| auth                | mfa_factors                | web_authn_aaguid            | uuid                        | YES         | null                                               |
| auth                | mfa_challenges             | id                          | uuid                        | NO          | null                                               |
| auth                | mfa_challenges             | factor_id                   | uuid                        | NO          | null                                               |
| auth                | mfa_challenges             | created_at                  | timestamp with time zone    | NO          | null                                               |
| auth                | mfa_challenges             | verified_at                 | timestamp with time zone    | YES         | null                                               |
| auth                | mfa_challenges             | ip_address                  | inet                        | NO          | null                                               |
| auth                | mfa_challenges             | web_authn_session_data      | jsonb                       | YES         | null                                               |
| auth                | audit_log_entries          | instance_id                 | uuid                        | YES         | null                                               |
| auth                | audit_log_entries          | id                          | uuid                        | NO          | null                                               |
| auth                | audit_log_entries          | payload                     | json                        | YES         | null                                               |
| auth                | audit_log_entries          | created_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | identities                 | user_id                     | uuid                        | NO          | null                                               |
| auth                | identities                 | identity_data               | jsonb                       | NO          | null                                               |
| auth                | identities                 | last_sign_in_at             | timestamp with time zone    | YES         | null                                               |
| auth                | identities                 | created_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | identities                 | updated_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | identities                 | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| auth                | sessions                   | id                          | uuid                        | NO          | null                                               |
| auth                | sessions                   | user_id                     | uuid                        | NO          | null                                               |
| auth                | sessions                   | created_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | sessions                   | updated_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | sessions                   | factor_id                   | uuid                        | YES         | null                                               |
| auth                | sessions                   | aal                         | USER-DEFINED                | YES         | null                                               |
| auth                | sessions                   | not_after                   | timestamp with time zone    | YES         | null                                               |
| auth                | sessions                   | refreshed_at                | timestamp without time zone | YES         | null                                               |
| auth                | sessions                   | ip                          | inet                        | YES         | null                                               |
| auth                | sso_providers              | id                          | uuid                        | NO          | null                                               |
| auth                | sso_providers              | created_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | sso_providers              | updated_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | sso_domains                | id                          | uuid                        | NO          | null                                               |
| auth                | sso_domains                | sso_provider_id             | uuid                        | NO          | null                                               |
| auth                | sso_domains                | created_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | sso_domains                | updated_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | mfa_amr_claims             | session_id                  | uuid                        | NO          | null                                               |
| auth                | mfa_amr_claims             | created_at                  | timestamp with time zone    | NO          | null                                               |
| auth                | mfa_amr_claims             | updated_at                  | timestamp with time zone    | NO          | null                                               |
| auth                | mfa_amr_claims             | id                          | uuid                        | NO          | null                                               |
| auth                | saml_providers             | id                          | uuid                        | NO          | null                                               |
| auth                | saml_providers             | sso_provider_id             | uuid                        | NO          | null                                               |
| auth                | saml_providers             | attribute_mapping           | jsonb                       | YES         | null                                               |
| auth                | saml_providers             | created_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | saml_providers             | updated_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | saml_relay_states          | id                          | uuid                        | NO          | null                                               |
| auth                | saml_relay_states          | sso_provider_id             | uuid                        | NO          | null                                               |
| auth                | saml_relay_states          | created_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | saml_relay_states          | updated_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | saml_relay_states          | flow_state_id               | uuid                        | YES         | null                                               |
| auth                | flow_state                 | id                          | uuid                        | NO          | null                                               |
| auth                | flow_state                 | user_id                     | uuid                        | YES         | null                                               |
| auth                | flow_state                 | code_challenge_method       | USER-DEFINED                | NO          | null                                               |
| auth                | flow_state                 | created_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | flow_state                 | updated_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | flow_state                 | auth_code_issued_at         | timestamp with time zone    | YES         | null                                               |
| auth                | one_time_tokens            | id                          | uuid                        | NO          | null                                               |
| auth                | one_time_tokens            | user_id                     | uuid                        | NO          | null                                               |
| auth                | one_time_tokens            | token_type                  | USER-DEFINED                | NO          | null                                               |
| auth                | one_time_tokens            | created_at                  | timestamp without time zone | NO          | now()                                              |
| auth                | one_time_tokens            | updated_at                  | timestamp without time zone | NO          | now()                                              |
| realtime            | schema_migrations          | version                     | bigint                      | NO          | null                                               |
| realtime            | schema_migrations          | inserted_at                 | timestamp without time zone | YES         | null                                               |
| storage             | s3_multipart_uploads_parts | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| storage             | s3_multipart_uploads_parts | size                        | bigint                      | NO          | 0                                                  |
| storage             | s3_multipart_uploads_parts | part_number                 | integer                     | NO          | null                                               |
| storage             | s3_multipart_uploads_parts | created_at                  | timestamp with time zone    | NO          | now()                                              |
| storage             | s3_multipart_uploads       | in_progress_size            | bigint                      | NO          | 0                                                  |
| storage             | s3_multipart_uploads       | created_at                  | timestamp with time zone    | NO          | now()                                              |
| storage             | s3_multipart_uploads       | user_metadata               | jsonb                       | YES         | null                                               |
| realtime            | subscription               | id                          | bigint                      | NO          | null                                               |
| realtime            | subscription               | subscription_id             | uuid                        | NO          | null                                               |
| realtime            | subscription               | entity                      | regclass                    | NO          | null                                               |
| realtime            | subscription               | filters                     | ARRAY                       | NO          | '{}'::realtime.user_defined_filter[]               |
| realtime            | subscription               | claims                      | jsonb                       | NO          | null                                               |
| realtime            | subscription               | claims_role                 | regrole                     | NO          | null                                               |
| realtime            | subscription               | created_at                  | timestamp without time zone | NO          | timezone('utc'::text, now())                       |
| realtime            | messages                   | payload                     | jsonb                       | YES         | null                                               |
| realtime            | messages                   | private                     | boolean                     | YES         | false                                              |
| realtime            | messages                   | updated_at                  | timestamp without time zone | NO          | now()                                              |
| realtime            | messages                   | inserted_at                 | timestamp without time zone | NO          | now()                                              |
| realtime            | messages                   | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| public              | goals                      | goal_type                   | USER-DEFINED                | NO          | null                                               |
| public              | goals                      | current_progress            | numeric                     | YES         | null                                               |
| public              | goals                      | progress_target             | numeric                     | YES         | null                                               |
| public              | goals                      | progress_percent            | numeric                     | YES         | null                                               |
| public              | goals                      | progress_status             | USER-DEFINED                | NO          | null                                               |
| public              | goals                      | due_date                    | date                        | NO          | null                                               |
| public              | goals                      | goal_achieved               | boolean                     | YES         | false                                              |
| public              | goals                      | last_updated                | timestamp with time zone    | NO          | null                                               |
| public              | goals                      | created_at                  | timestamp with time zone    | YES         | now()                                              |
| public              | goals                      | updated_at                  | timestamp with time zone    | YES         | now()                                              |
| public              | goals                      | team_id                     | uuid                        | YES         | null                                               |
| extensions          | pg_stat_statements         | userid                      | oid                         | YES         | null                                               |
| extensions          | pg_stat_statements         | dbid                        | oid                         | YES         | null                                               |
| extensions          | pg_stat_statements         | toplevel                    | boolean                     | YES         | null                                               |
| extensions          | pg_stat_statements         | queryid                     | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | plans                       | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | total_plan_time             | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | min_plan_time               | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | max_plan_time               | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | mean_plan_time              | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | stddev_plan_time            | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | calls                       | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | total_exec_time             | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | min_exec_time               | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | max_exec_time               | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | mean_exec_time              | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | stddev_exec_time            | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | rows                        | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | shared_blks_hit             | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | shared_blks_read            | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | shared_blks_dirtied         | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | shared_blks_written         | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | local_blks_hit              | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | local_blks_read             | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | local_blks_dirtied          | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | local_blks_written          | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | temp_blks_read              | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | temp_blks_written           | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | blk_read_time               | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | blk_write_time              | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | temp_blk_read_time          | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | temp_blk_write_time         | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | wal_records                 | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | wal_fpi                     | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | wal_bytes                   | numeric                     | YES         | null                                               |
| extensions          | pg_stat_statements         | jit_functions               | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | jit_generation_time         | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | jit_inlining_count          | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | jit_inlining_time           | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | jit_optimization_count      | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | jit_optimization_time       | double precision            | YES         | null                                               |
| extensions          | pg_stat_statements         | jit_emission_count          | bigint                      | YES         | null                                               |
| extensions          | pg_stat_statements         | jit_emission_time           | double precision            | YES         | null                                               |
| public              | teams                      | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| public              | teams                      | created_at                  | timestamp with time zone    | YES         | now()                                              |
| public              | teams                      | updated_at                  | timestamp with time zone    | YES         | now()                                              |
| public              | teams                      | status                      | USER-DEFINED                | NO          | 'Inactive'::team_status                            |
| public              | teams                      | created_by                  | uuid                        | YES         | null                                               |
| public              | teams                      | progress                    | integer                     | NO          | 0                                                  |
| public              | teams                      | end_date                    | timestamp with time zone    | YES         | (now() + '30 days'::interval)                      |
| auth                | users                      | instance_id                 | uuid                        | YES         | null                                               |
| auth                | users                      | id                          | uuid                        | NO          | null                                               |
| auth                | users                      | email_confirmed_at          | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | invited_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | confirmation_sent_at        | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | recovery_sent_at            | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | email_change_sent_at        | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | last_sign_in_at             | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | raw_app_meta_data           | jsonb                       | YES         | null                                               |
| auth                | users                      | raw_user_meta_data          | jsonb                       | YES         | null                                               |
| auth                | users                      | is_super_admin              | boolean                     | YES         | null                                               |
| auth                | users                      | created_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | updated_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | phone_confirmed_at          | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | phone_change_sent_at        | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | confirmed_at                | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | email_change_confirm_status | smallint                    | YES         | 0                                                  |
| auth                | users                      | banned_until                | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | reauthentication_sent_at    | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | is_sso_user                 | boolean                     | NO          | false                                              |
| auth                | users                      | deleted_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | users                      | is_anonymous                | boolean                     | NO          | false                                              |
| auth                | instances                  | id                          | uuid                        | NO          | null                                               |
| auth                | instances                  | uuid                        | uuid                        | YES         | null                                               |
| auth                | instances                  | created_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | instances                  | updated_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | refresh_tokens             | instance_id                 | uuid                        | YES         | null                                               |
| auth                | refresh_tokens             | id                          | bigint                      | NO          | nextval('auth.refresh_tokens_id_seq'::regclass)    |
| auth                | refresh_tokens             | revoked                     | boolean                     | YES         | null                                               |
| auth                | refresh_tokens             | created_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | refresh_tokens             | updated_at                  | timestamp with time zone    | YES         | null                                               |
| auth                | refresh_tokens             | session_id                  | uuid                        | YES         | null                                               |
| storage             | buckets                    | owner                       | uuid                        | YES         | null                                               |
| storage             | buckets                    | created_at                  | timestamp with time zone    | YES         | now()                                              |
| storage             | buckets                    | updated_at                  | timestamp with time zone    | YES         | now()                                              |
| storage             | buckets                    | public                      | boolean                     | YES         | false                                              |
| storage             | buckets                    | avif_autodetection          | boolean                     | YES         | false                                              |
| storage             | buckets                    | file_size_limit             | bigint                      | YES         | null                                               |
| storage             | objects                    | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| storage             | objects                    | owner                       | uuid                        | YES         | null                                               |
| storage             | objects                    | created_at                  | timestamp with time zone    | YES         | now()                                              |
| storage             | objects                    | updated_at                  | timestamp with time zone    | YES         | now()                                              |
| storage             | objects                    | last_accessed_at            | timestamp with time zone    | YES         | now()                                              |
| storage             | objects                    | metadata                    | jsonb                       | YES         | null                                               |
| storage             | objects                    | user_metadata               | jsonb                       | YES         | null                                               |
| storage             | migrations                 | id                          | integer                     | NO          | null                                               |
| storage             | migrations                 | executed_at                 | timestamp without time zone | YES         | CURRENT_TIMESTAMP                                  |
| pgsodium            | mask_columns               | attrelid                    | oid                         | YES         | null                                               |
| pgsodium            | valid_key                  | id                          | uuid                        | YES         | null                                               |
| pgsodium            | valid_key                  | status                      | USER-DEFINED                | YES         | null                                               |
| pgsodium            | valid_key                  | key_type                    | USER-DEFINED                | YES         | null                                               |
| pgsodium            | valid_key                  | key_id                      | bigint                      | YES         | null                                               |
| pgsodium            | valid_key                  | key_context                 | bytea                       | YES         | null                                               |
| pgsodium            | valid_key                  | created                     | timestamp with time zone    | YES         | null                                               |
| pgsodium            | valid_key                  | expires                     | timestamp with time zone    | YES         | null                                               |
| pgsodium            | decrypted_key              | id                          | uuid                        | YES         | null                                               |
| pgsodium            | decrypted_key              | status                      | USER-DEFINED                | YES         | null                                               |
| pgsodium            | decrypted_key              | created                     | timestamp with time zone    | YES         | null                                               |
| pgsodium            | decrypted_key              | expires                     | timestamp with time zone    | YES         | null                                               |
| pgsodium            | decrypted_key              | key_type                    | USER-DEFINED                | YES         | null                                               |
| pgsodium            | decrypted_key              | key_id                      | bigint                      | YES         | null                                               |
| pgsodium            | decrypted_key              | key_context                 | bytea                       | YES         | null                                               |
| pgsodium            | decrypted_key              | raw_key                     | bytea                       | YES         | null                                               |
| pgsodium            | decrypted_key              | decrypted_raw_key           | bytea                       | YES         | null                                               |
| pgsodium            | decrypted_key              | raw_key_nonce               | bytea                       | YES         | null                                               |
| pgsodium            | decrypted_key              | parent_key                  | uuid                        | YES         | null                                               |
| pgsodium            | key                        | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| pgsodium            | key                        | status                      | USER-DEFINED                | YES         | 'valid'::pgsodium.key_status                       |
| pgsodium            | key                        | created                     | timestamp with time zone    | NO          | CURRENT_TIMESTAMP                                  |
| pgsodium            | key                        | expires                     | timestamp with time zone    | YES         | null                                               |
| pgsodium            | key                        | key_type                    | USER-DEFINED                | YES         | null                                               |
| pgsodium            | key                        | key_id                      | bigint                      | YES         | nextval('pgsodium.key_key_id_seq'::regclass)       |
| pgsodium            | key                        | key_context                 | bytea                       | YES         | '\x7067736f6469756d'::bytea                        |
| pgsodium            | key                        | raw_key                     | bytea                       | YES         | null                                               |
| pgsodium            | key                        | raw_key_nonce               | bytea                       | YES         | null                                               |
| pgsodium            | key                        | parent_key                  | uuid                        | YES         | null                                               |
| pgsodium            | masking_rule               | attrelid                    | oid                         | YES         | null                                               |
| pgsodium            | masking_rule               | attnum                      | integer                     | YES         | null                                               |
| pgsodium            | masking_rule               | relnamespace                | regnamespace                | YES         | null                                               |
| pgsodium            | masking_rule               | priority                    | integer                     | YES         | null                                               |
| pgsodium            | masking_rule               | security_invoker            | boolean                     | YES         | null                                               |
| vault               | secrets                    | id                          | uuid                        | NO          | gen_random_uuid()                                  |
| vault               | secrets                    | key_id                      | uuid                        | YES         | (pgsodium.create_key()).id                         |
| vault               | secrets                    | nonce                       | bytea                       | YES         | pgsodium.crypto_aead_det_noncegen()                |
| vault               | secrets                    | created_at                  | timestamp with time zone    | NO          | CURRENT_TIMESTAMP                                  |
| vault               | secrets                    | updated_at                  | timestamp with time zone    | NO          | CURRENT_TIMESTAMP                                  |
| vault               | decrypted_secrets          | id                          | uuid                        | YES         | null                                               |
| vault               | decrypted_secrets          | key_id                      | uuid                        | YES         | null                                               |
| vault               | decrypted_secrets          | nonce                       | bytea                       | YES         | null                                               |
| vault               | decrypted_secrets          | created_at                  | timestamp with time zone    | YES         | null                                               |
| vault               | decrypted_secrets          | updated_at                  | timestamp with time zone    | YES         | null                                               |
| public              | kanban_cards               | id                          | uuid                        | NO          | uuid_generate_v4()                                 |
| public              | kanban_cards               | team_id                     | uuid                        | YES         | null                                               |
| public              | kanban_cards               | column_id                   | uuid                        | YES         | null                                               |
| public              | kanban_cards               | order_index                 | integer                     | NO          | null                                               |
| public              | kanban_cards               | created_at                  | timestamp with time zone    | YES         | CURRENT_TIMESTAMP                                  |
| public              | kanban_cards               | updated_at                  | timestamp with time zone    | YES         | CURRENT_TIMESTAMP                                  |
| public              | kanban_cards               | due_date                    | timestamp with time zone    | YES         | null                                               |
| public              | kanban_cards               | archived_at                 | timestamp with time zone    | YES         | null                                               |
| public              | kanban_cards               | deleted_at                  | timestamp with time zone    | YES         | null                                               |
| public              | kanban_cards               | assignee_id                 | uuid                        | YES         | null                                               |
| public              | kanban_cards               | position_updated_at         | timestamp with time zone    | NO          | now()                                              |
| public              | kanban_comments            | id                          | uuid                        | NO          | uuid_generate_v4()                                 |
| public              | kanban_comments            | card_id                     | uuid                        | YES         | null                                               |
| public              | kanban_comments            | user_id                     | uuid                        | NO          | null                                               |
| public              | kanban_comments            | created_at                  | timestamp with time zone    | YES         | CURRENT_TIMESTAMP                                  |
| public              | kanban_comments            | updated_at                  | timestamp with time zone    | YES         | CURRENT_TIMESTAMP                                  |
| public              | kanban_comments            | is_markdown                 | boolean                     | YES         | false                                              |
| public              | kanban_comments            | edited_at                   | timestamp with time zone    | YES         | null                                               |
| public              | kanban_comments            | deleted_at                  | timestamp with time zone    | YES         | null                                               |
| cron                | job                        | jobname                     | text                        | YES         | null                                               |
| public              | goals                      | title                       | text                        | NO          | null                                               |
| public              | goals                      | full_name                   | text                        | NO          | null                                               |
| public              | goals                      | goal_cycle                  | text                        | NO          | null                                               |
| storage             | objects                    | version                     | text                        | YES         | null                                               |
| storage             | objects                    | owner_id                    | text                        | YES         | null                                               |
| supabase_migrations | schema_migrations          | name                        | text                        | YES         | null                                               |
| auth                | saml_relay_states          | request_id                  | text                        | NO          | null                                               |
| storage             | migrations                 | name                        | character varying           | NO          | null                                               |
| storage             | migrations                 | hash                        | character varying           | NO          | null                                               |
| cron                | job                        | schedule                    | text                        | NO          | null                                               |
| cron                | job                        | command                     | text                        | NO          | null                                               |
| pgsodium            | mask_columns               | format_type                 | text                        | YES         | null                                               |
| cron                | job                        | nodename                    | text                        | NO          | 'localhost'::text                                  |
| pgsodium            | valid_key                  | name                        | text                        | YES         | null                                               |
| auth                | saml_relay_states          | for_email                   | text                        | YES         | null                                               |
| cron                | job                        | database                    | text                        | NO          | current_database()                                 |
| cron                | job                        | username                    | text                        | NO          | CURRENT_USER                                       |
| auth                | saml_relay_states          | redirect_to                 | text                        | YES         | null                                               |
| public              | goals                      | parent_goal_title           | text                        | YES         | null                                               |
| pgsodium            | valid_key                  | associated_data             | text                        | YES         | null                                               |
| storage             | s3_multipart_uploads       | version                     | text                        | NO          | null                                               |
| public              | team_weekly_updates        | week_number                 | text                        | YES         | null                                               |
| storage             | s3_multipart_uploads_parts | upload_id                   | text                        | NO          | null                                               |
| public              | goals                      | tags                        | ARRAY                       | YES         | null                                               |
| public              | team_weekly_updates        | team_update_summary         | text                        | YES         | null                                               |
| storage             | s3_multipart_uploads       | owner_id                    | text                        | YES         | null                                               |
| public              | profiles                   | full_name                   | text                        | YES         | null                                               |
| public              | profiles                   | email                       | text                        | YES         | null                                               |
| pgsodium            | decrypted_key              | name                        | text                        | YES         | null                                               |
| pgsodium            | decrypted_key              | associated_data             | text                        | YES         | null                                               |
| public              | profiles                   | avatar_url                  | text                        | YES         | null                                               |
| public              | kanban_columns             | title                       | text                        | NO          | null                                               |
| public              | profiles                   | role                        | text                        | YES         | 'user'::text                                       |
| auth                | sessions                   | user_agent                  | text                        | YES         | null                                               |
| extensions          | pg_stat_statements         | query                       | text                        | YES         | null                                               |
| pgsodium            | decrypted_key              | comment                     | text                        | YES         | null                                               |
| realtime            | messages                   | topic                       | text                        | NO          | null                                               |
| realtime            | messages_2025_02_21        | topic                       | text                        | NO          | null                                               |
| realtime            | messages_2025_02_21        | extension                   | text                        | NO          | null                                               |
| auth                | sessions                   | tag                         | text                        | YES         | null                                               |
| realtime            | messages_2025_02_21        | event                       | text                        | YES         | null                                               |
| auth                | flow_state                 | auth_code                   | text                        | NO          | null                                               |
| auth                | mfa_factors                | friendly_name               | text                        | YES         | null                                               |
| pgsodium            | key                        | name                        | text                        | YES         | null                                               |
| pgsodium            | key                        | associated_data             | text                        | YES         | 'associated'::text                                 |
| auth                | sso_providers              | resource_id                 | text                        | YES         | null                                               |
| storage             | s3_multipart_uploads_parts | bucket_id                   | text                        | NO          | null                                               |
| realtime            | messages_2025_02_23        | topic                       | text                        | NO          | null                                               |
| pgsodium            | key                        | comment                     | text                        | YES         | null                                               |
| pgsodium            | key                        | user_data                   | text                        | YES         | null                                               |
| realtime            | messages_2025_02_23        | extension                   | text                        | NO          | null                                               |
| auth                | flow_state                 | code_challenge              | text                        | NO          | null                                               |
| realtime            | messages_2025_02_23        | event                       | text                        | YES         | null                                               |
| pgsodium            | masking_rule               | format_type                 | text                        | YES         | null                                               |
| auth                | flow_state                 | provider_type               | text                        | NO          | null                                               |
| auth                | mfa_factors                | secret                      | text                        | YES         | null                                               |
| auth                | mfa_factors                | phone                       | text                        | YES         | null                                               |
| vault               | secrets                    | name                        | text                        | YES         | null                                               |
| vault               | secrets                    | description                 | text                        | NO          | ''::text                                           |
| vault               | secrets                    | secret                      | text                        | NO          | null                                               |
| auth                | flow_state                 | provider_access_token       | text                        | YES         | null                                               |
| realtime            | messages_2025_02_25        | topic                       | text                        | NO          | null                                               |
| realtime            | messages_2025_02_25        | extension                   | text                        | NO          | null                                               |
| auth                | sso_domains                | domain                      | text                        | NO          | null                                               |
| realtime            | messages_2025_02_25        | event                       | text                        | YES         | null                                               |
| vault               | decrypted_secrets          | name                        | text                        | YES         | null                                               |
| public              | teams                      | name                        | text                        | NO          | null                                               |
| public              | teams                      | objective                   | text                        | YES         | null                                               |
| vault               | decrypted_secrets          | description                 | text                        | YES         | null                                               |
| vault               | decrypted_secrets          | secret                      | text                        | YES         | null                                               |
| public              | teams                      | avatar_url                  | text                        | YES         | null                                               |
| auth                | flow_state                 | provider_refresh_token      | text                        | YES         | null                                               |
| storage             | s3_multipart_uploads_parts | etag                        | text                        | NO          | null                                               |
| auth                | flow_state                 | authentication_method       | text                        | NO          | null                                               |
| storage             | s3_multipart_uploads_parts | owner_id                    | text                        | YES         | null                                               |
| auth                | users                      | aud                         | character varying           | YES         | null                                               |
| auth                | users                      | role                        | character varying           | YES         | null                                               |
| auth                | users                      | email                       | character varying           | YES         | null                                               |
| auth                | users                      | encrypted_password          | character varying           | YES         | null                                               |
| net                 | http_request_queue         | method                      | text                        | NO          | null                                               |
| net                 | http_request_queue         | url                         | text                        | NO          | null                                               |
| auth                | users                      | confirmation_token          | character varying           | YES         | null                                               |
| public              | kanban_cards               | title                       | text                        | NO          | null                                               |
| auth                | users                      | recovery_token              | character varying           | YES         | null                                               |
| public              | kanban_cards               | description                 | text                        | YES         | null                                               |
| auth                | users                      | email_change_token_new      | character varying           | YES         | null                                               |
| auth                | users                      | email_change                | character varying           | YES         | null                                               |
| auth                | mfa_amr_claims             | authentication_method       | text                        | NO          | null                                               |
| auth                | mfa_challenges             | otp_code                    | text                        | YES         | null                                               |
| storage             | s3_multipart_uploads_parts | version                     | text                        | NO          | null                                               |
| realtime            | messages                   | extension                   | text                        | NO          | null                                               |
| storage             | s3_multipart_uploads       | id                          | text                        | NO          | null                                               |
| net                 | \_http_response            | content_type                | text                        | YES         | null                                               |
| public              | kanban_cards               | priority                    | text                        | NO          | 'P1'::text                                         |
| auth                | users                      | phone                       | text                        | YES         | NULL::character varying                            |
| auth                | saml_providers             | entity_id                   | text                        | NO          | null                                               |
| auth                | users                      | phone_change                | text                        | YES         | ''::character varying                              |
| auth                | users                      | phone_change_token          | character varying           | YES         | ''::character varying                              |
| net                 | \_http_response            | content                     | text                        | YES         | null                                               |
| auth                | saml_providers             | metadata_xml                | text                        | NO          | null                                               |
| auth                | users                      | email_change_token_current  | character varying           | YES         | ''::character varying                              |
| net                 | \_http_response            | error_msg                   | text                        | YES         | null                                               |
| auth                | audit_log_entries          | ip_address                  | character varying           | NO          | ''::character varying                              |
| auth                | users                      | reauthentication_token      | character varying           | YES         | ''::character varying                              |
| public              | kanban_comments            | content                     | text                        | NO          | null                                               |
| realtime            | messages_2025_02_22        | topic                       | text                        | NO          | null                                               |
| realtime            | messages_2025_02_22        | extension                   | text                        | NO          | null                                               |
| auth                | identities                 | provider_id                 | text                        | NO          | null                                               |
| auth                | schema_migrations          | version                     | character varying           | NO          | null                                               |
| realtime            | messages_2025_02_22        | event                       | text                        | YES         | null                                               |
| public              | kanban_comments            | urls                        | ARRAY                       | YES         | null                                               |
| auth                | instances                  | raw_base_config             | text                        | YES         | null                                               |
| auth                | saml_providers             | metadata_url                | text                        | YES         | null                                               |
| auth                | one_time_tokens            | token_hash                  | text                        | NO          | null                                               |
| auth                | identities                 | provider                    | text                        | NO          | null                                               |
| auth                | one_time_tokens            | relates_to                  | text                        | NO          | null                                               |
| auth                | refresh_tokens             | token                       | character varying           | YES         | null                                               |
| auth                | refresh_tokens             | user_id                     | character varying           | YES         | null                                               |
| public              | kanban_subtasks            | title                       | text                        | NO          | null                                               |
| realtime            | messages_2025_02_24        | topic                       | text                        | NO          | null                                               |
| realtime            | messages_2025_02_24        | extension                   | text                        | NO          | null                                               |
| auth                | refresh_tokens             | parent                      | character varying           | YES         | null                                               |
| realtime            | messages                   | event                       | text                        | YES         | null                                               |
| storage             | buckets                    | id                          | text                        | NO          | null                                               |
| storage             | buckets                    | name                        | text                        | NO          | null                                               |
| realtime            | messages_2025_02_24        | event                       | text                        | YES         | null                                               |
| auth                | saml_providers             | name_id_format              | text                        | YES         | null                                               |
| auth                | identities                 | email                       | text                        | YES         | null                                               |
| storage             | s3_multipart_uploads       | upload_signature            | text                        | NO          | null                                               |
| storage             | s3_multipart_uploads       | bucket_id                   | text                        | NO          | null                                               |
| supabase_migrations | schema_migrations          | version                     | text                        | NO          | null                                               |
| storage             | buckets                    | allowed_mime_types          | ARRAY                       | YES         | null                                               |
| storage             | buckets                    | owner_id                    | text                        | YES         | null                                               |
| cron                | job_run_details            | database                    | text                        | YES         | null                                               |
| storage             | objects                    | bucket_id                   | text                        | YES         | null                                               |
| storage             | objects                    | name                        | text                        | YES         | null                                               |
| cron                | job_run_details            | username                    | text                        | YES         | null                                               |
| cron                | job_run_details            | command                     | text                        | YES         | null                                               |
| cron                | job_run_details            | status                      | text                        | YES         | null                                               |
| cron                | job_run_details            | return_message              | text                        | YES         | null                                               |
| supabase_migrations | schema_migrations          | statements                  | ARRAY                       | YES         | null                                               |
| storage             | objects                    | path_tokens                 | ARRAY                       | YES         | null                                               |
| storage             | s3_multipart_uploads       | key                         | text                        | NO          | null                                               |
| vault               | decrypted_secrets          | decrypted_secret            | text                        | YES         | null                                               |
| pgsodium            | mask_columns               | nonce_column                | text                        | YES         | null                                               |
| pgsodium            | mask_columns               | key_id                      | text                        | YES         | null                                               |
| storage             | s3_multipart_uploads_parts | key                         | text                        | NO          | null                                               |
| pgsodium            | masking_rule               | relname                     | name                        | YES         | null                                               |
| pgsodium            | masking_rule               | attname                     | name                        | YES         | null                                               |
| pgsodium            | mask_columns               | key_id_column               | text                        | YES         | null                                               |
| pgsodium            | masking_rule               | col_description             | text                        | YES         | null                                               |
| pgsodium            | masking_rule               | key_id_column               | text                        | YES         | null                                               |
| pgsodium            | masking_rule               | key_id                      | text                        | YES         | null                                               |
| pgsodium            | masking_rule               | associated_columns          | text                        | YES         | null                                               |
| pgsodium            | masking_rule               | nonce_column                | text                        | YES         | null                                               |
| pgsodium            | masking_rule               | view_name                   | text                        | YES         | null                                               |
| pgsodium            | mask_columns               | associated_columns          | text                        | YES         | null                                               |
| pgsodium            | mask_columns               | attname                     | name                        | YES         | null                                               |
