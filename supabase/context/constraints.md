| table_schema        | table_name                 | column_name           | constraint_type |
| ------------------- | -------------------------- | --------------------- | --------------- |
| storage             | buckets                    | id                    | PRIMARY KEY     |
| storage             | migrations                 | name                  | UNIQUE          |
| storage             | migrations                 | id                    | PRIMARY KEY     |
| storage             | objects                    | bucket_id             | FOREIGN KEY     |
| storage             | objects                    | id                    | PRIMARY KEY     |
| storage             | s3_multipart_uploads       | bucket_id             | FOREIGN KEY     |
| storage             | s3_multipart_uploads       | id                    | PRIMARY KEY     |
| storage             | s3_multipart_uploads_parts | bucket_id             | FOREIGN KEY     |
| storage             | s3_multipart_uploads_parts | id                    | PRIMARY KEY     |
| storage             | s3_multipart_uploads_parts | upload_id             | FOREIGN KEY     |
| auth                | refresh_tokens             | id                    | PRIMARY KEY     |
| auth                | refresh_tokens             | session_id            | FOREIGN KEY     |
| auth                | refresh_tokens             | token                 | UNIQUE          |
| auth                | instances                  | id                    | PRIMARY KEY     |
| auth                | schema_migrations          | version               | PRIMARY KEY     |
| auth                | users                      | null                  | CHECK           |
| auth                | users                      | phone                 | UNIQUE          |
| auth                | users                      | id                    | PRIMARY KEY     |
| auth                | audit_log_entries          | id                    | PRIMARY KEY     |
| auth                | mfa_factors                | last_challenged_at    | UNIQUE          |
| auth                | mfa_factors                | id                    | PRIMARY KEY     |
| auth                | mfa_factors                | user_id               | FOREIGN KEY     |
| auth                | mfa_challenges             | factor_id             | FOREIGN KEY     |
| auth                | mfa_challenges             | id                    | PRIMARY KEY     |
| auth                | identities                 | id                    | PRIMARY KEY     |
| auth                | identities                 | provider_id           | UNIQUE          |
| auth                | identities                 | provider              | UNIQUE          |
| auth                | identities                 | user_id               | FOREIGN KEY     |
| auth                | sessions                   | id                    | PRIMARY KEY     |
| auth                | sessions                   | user_id               | FOREIGN KEY     |
| auth                | mfa_amr_claims             | id                    | PRIMARY KEY     |
| auth                | mfa_amr_claims             | session_id            | UNIQUE          |
| auth                | mfa_amr_claims             | authentication_method | UNIQUE          |
| auth                | mfa_amr_claims             | session_id            | FOREIGN KEY     |
| auth                | sso_providers              | null                  | CHECK           |
| auth                | sso_providers              | id                    | PRIMARY KEY     |
| auth                | sso_domains                | null                  | CHECK           |
| auth                | sso_domains                | id                    | PRIMARY KEY     |
| auth                | sso_domains                | sso_provider_id       | FOREIGN KEY     |
| auth                | saml_providers             | null                  | CHECK           |
| auth                | saml_providers             | null                  | CHECK           |
| auth                | saml_providers             | null                  | CHECK           |
| auth                | saml_providers             | entity_id             | UNIQUE          |
| auth                | saml_providers             | id                    | PRIMARY KEY     |
| auth                | saml_providers             | sso_provider_id       | FOREIGN KEY     |
| auth                | saml_relay_states          | null                  | CHECK           |
| auth                | saml_relay_states          | flow_state_id         | FOREIGN KEY     |
| auth                | saml_relay_states          | id                    | PRIMARY KEY     |
| auth                | saml_relay_states          | sso_provider_id       | FOREIGN KEY     |
| auth                | flow_state                 | id                    | PRIMARY KEY     |
| auth                | one_time_tokens            | id                    | PRIMARY KEY     |
| auth                | one_time_tokens            | null                  | CHECK           |
| auth                | one_time_tokens            | user_id               | FOREIGN KEY     |
| realtime            | messages_2025_02_21        | id                    | PRIMARY KEY     |
| realtime            | messages_2025_02_21        | inserted_at           | PRIMARY KEY     |
| realtime            | messages_2025_02_23        | id                    | PRIMARY KEY     |
| realtime            | messages_2025_02_23        | inserted_at           | PRIMARY KEY     |
| realtime            | messages_2025_02_25        | id                    | PRIMARY KEY     |
| realtime            | messages_2025_02_25        | inserted_at           | PRIMARY KEY     |
| realtime            | messages_2025_02_22        | id                    | PRIMARY KEY     |
| realtime            | messages_2025_02_22        | inserted_at           | PRIMARY KEY     |
| realtime            | messages_2025_02_24        | id                    | PRIMARY KEY     |
| realtime            | messages_2025_02_24        | inserted_at           | PRIMARY KEY     |
| realtime            | schema_migrations          | version               | PRIMARY KEY     |
| realtime            | subscription               | id                    | PRIMARY KEY     |
| realtime            | messages                   | id                    | PRIMARY KEY     |
| realtime            | messages                   | inserted_at           | PRIMARY KEY     |
| pgsodium            | key                        | null                  | CHECK           |
| pgsodium            | key                        | parent_key            | FOREIGN KEY     |
| pgsodium            | key                        | id                    | PRIMARY KEY     |
| pgsodium            | key                        | name                  | UNIQUE          |
| pgsodium            | key                        | null                  | CHECK           |
| vault               | secrets                    | key_id                | FOREIGN KEY     |
| vault               | secrets                    | id                    | PRIMARY KEY     |
| public              | goals                      | id                    | PRIMARY KEY     |
| public              | goals                      | team_id               | FOREIGN KEY     |
| public              | teams                      | created_by            | FOREIGN KEY     |
| public              | teams                      | name                  | UNIQUE          |
| public              | teams                      | id                    | PRIMARY KEY     |
| public              | teams                      | null                  | CHECK           |
| public              | team_members               | id                    | PRIMARY KEY     |
| public              | team_members               | team_id               | FOREIGN KEY     |
| public              | team_members               | user_id               | FOREIGN KEY     |
| public              | team_members               | team_id               | UNIQUE          |
| public              | team_members               | user_id               | UNIQUE          |
| public              | kanban_columns             | id                    | PRIMARY KEY     |
| public              | kanban_columns             | team_id               | FOREIGN KEY     |
| public              | kanban_cards               | assignee_id           | FOREIGN KEY     |
| public              | kanban_cards               | assignee_id           | FOREIGN KEY     |
| public              | kanban_cards               | column_id             | FOREIGN KEY     |
| public              | kanban_cards               | id                    | PRIMARY KEY     |
| public              | kanban_cards               | null                  | CHECK           |
| public              | kanban_cards               | team_id               | FOREIGN KEY     |
| public              | kanban_comments            | user_id               | FOREIGN KEY     |
| public              | kanban_comments            | card_id               | FOREIGN KEY     |
| public              | kanban_comments            | id                    | PRIMARY KEY     |
| public              | kanban_comments            | user_id               | FOREIGN KEY     |
| public              | kanban_subtasks            | assignee_id           | FOREIGN KEY     |
| public              | kanban_subtasks            | assignee_id           | FOREIGN KEY     |
| public              | kanban_subtasks            | card_id               | FOREIGN KEY     |
| public              | kanban_subtasks            | id                    | PRIMARY KEY     |
| public              | profiles                   | id                    | FOREIGN KEY     |
| public              | profiles                   | id                    | PRIMARY KEY     |
| public              | profiles                   | null                  | CHECK           |
| supabase_migrations | schema_migrations          | version               | PRIMARY KEY     |
| storage             | buckets                    | null                  | CHECK           |
| storage             | buckets                    | null                  | CHECK           |
| storage             | migrations                 | null                  | CHECK           |
| storage             | migrations                 | null                  | CHECK           |
| storage             | migrations                 | null                  | CHECK           |
| storage             | objects                    | null                  | CHECK           |
| storage             | s3_multipart_uploads       | null                  | CHECK           |
| storage             | s3_multipart_uploads       | null                  | CHECK           |
| storage             | s3_multipart_uploads       | null                  | CHECK           |
| storage             | s3_multipart_uploads       | null                  | CHECK           |
| storage             | s3_multipart_uploads       | null                  | CHECK           |
| storage             | s3_multipart_uploads       | null                  | CHECK           |
| storage             | s3_multipart_uploads       | null                  | CHECK           |
| storage             | s3_multipart_uploads_parts | null                  | CHECK           |
| storage             | s3_multipart_uploads_parts | null                  | CHECK           |
| storage             | s3_multipart_uploads_parts | null                  | CHECK           |
| storage             | s3_multipart_uploads_parts | null                  | CHECK           |
| storage             | s3_multipart_uploads_parts | null                  | CHECK           |
| storage             | s3_multipart_uploads_parts | null                  | CHECK           |
| storage             | s3_multipart_uploads_parts | null                  | CHECK           |
| storage             | s3_multipart_uploads_parts | null                  | CHECK           |
| storage             | s3_multipart_uploads_parts | null                  | CHECK           |
| auth                | refresh_tokens             | null                  | CHECK           |
| auth                | instances                  | null                  | CHECK           |
| auth                | schema_migrations          | null                  | CHECK           |
| auth                | users                      | null                  | CHECK           |
| auth                | users                      | null                  | CHECK           |
| auth                | users                      | null                  | CHECK           |
| auth                | audit_log_entries          | null                  | CHECK           |
| auth                | audit_log_entries          | null                  | CHECK           |
| auth                | mfa_factors                | null                  | CHECK           |
| auth                | mfa_factors                | null                  | CHECK           |
| auth                | mfa_factors                | null                  | CHECK           |
| auth                | mfa_factors                | null                  | CHECK           |
| auth                | mfa_factors                | null                  | CHECK           |
| auth                | mfa_factors                | null                  | CHECK           |
| auth                | mfa_challenges             | null                  | CHECK           |
| auth                | mfa_challenges             | null                  | CHECK           |
| auth                | mfa_challenges             | null                  | CHECK           |
| auth                | mfa_challenges             | null                  | CHECK           |
| auth                | identities                 | null                  | CHECK           |
| auth                | identities                 | null                  | CHECK           |
| auth                | identities                 | null                  | CHECK           |
| auth                | identities                 | null                  | CHECK           |
| auth                | identities                 | null                  | CHECK           |
| auth                | sessions                   | null                  | CHECK           |
| auth                | sessions                   | null                  | CHECK           |
| auth                | mfa_amr_claims             | null                  | CHECK           |
| auth                | mfa_amr_claims             | null                  | CHECK           |
| auth                | mfa_amr_claims             | null                  | CHECK           |
| auth                | mfa_amr_claims             | null                  | CHECK           |
| auth                | mfa_amr_claims             | null                  | CHECK           |
| auth                | sso_providers              | null                  | CHECK           |
| auth                | sso_domains                | null                  | CHECK           |
| auth                | sso_domains                | null                  | CHECK           |
| auth                | sso_domains                | null                  | CHECK           |
| auth                | saml_providers             | null                  | CHECK           |
| auth                | saml_providers             | null                  | CHECK           |
| auth                | saml_providers             | null                  | CHECK           |
| auth                | saml_providers             | null                  | CHECK           |
| auth                | saml_relay_states          | null                  | CHECK           |
| auth                | saml_relay_states          | null                  | CHECK           |
| auth                | saml_relay_states          | null                  | CHECK           |
| auth                | flow_state                 | null                  | CHECK           |
| auth                | flow_state                 | null                  | CHECK           |
| auth                | flow_state                 | null                  | CHECK           |
| auth                | flow_state                 | null                  | CHECK           |
| auth                | flow_state                 | null                  | CHECK           |
| auth                | flow_state                 | null                  | CHECK           |
| auth                | one_time_tokens            | null                  | CHECK           |
| auth                | one_time_tokens            | null                  | CHECK           |
| auth                | one_time_tokens            | null                  | CHECK           |
| auth                | one_time_tokens            | null                  | CHECK           |
| auth                | one_time_tokens            | null                  | CHECK           |
| auth                | one_time_tokens            | null                  | CHECK           |
| auth                | one_time_tokens            | null                  | CHECK           |
| realtime            | messages_2025_02_21        | null                  | CHECK           |
| realtime            | messages_2025_02_21        | null                  | CHECK           |
| realtime            | messages_2025_02_21        | null                  | CHECK           |
| realtime            | messages_2025_02_21        | null                  | CHECK           |
| realtime            | messages_2025_02_21        | null                  | CHECK           |
| realtime            | messages_2025_02_23        | null                  | CHECK           |
| realtime            | messages_2025_02_23        | null                  | CHECK           |
| realtime            | messages_2025_02_23        | null                  | CHECK           |
| realtime            | messages_2025_02_23        | null                  | CHECK           |
| realtime            | messages_2025_02_23        | null                  | CHECK           |
| realtime            | messages_2025_02_25        | null                  | CHECK           |
| realtime            | messages_2025_02_25        | null                  | CHECK           |
| realtime            | messages_2025_02_25        | null                  | CHECK           |
| realtime            | messages_2025_02_25        | null                  | CHECK           |
| realtime            | messages_2025_02_25        | null                  | CHECK           |
| realtime            | messages_2025_02_22        | null                  | CHECK           |
| realtime            | messages_2025_02_22        | null                  | CHECK           |
| realtime            | messages_2025_02_22        | null                  | CHECK           |
| realtime            | messages_2025_02_22        | null                  | CHECK           |
| realtime            | messages_2025_02_22        | null                  | CHECK           |
| realtime            | messages_2025_02_24        | null                  | CHECK           |
| realtime            | messages_2025_02_24        | null                  | CHECK           |
| realtime            | messages_2025_02_24        | null                  | CHECK           |
| realtime            | messages_2025_02_24        | null                  | CHECK           |
| realtime            | messages_2025_02_24        | null                  | CHECK           |
| realtime            | schema_migrations          | null                  | CHECK           |
| realtime            | subscription               | null                  | CHECK           |
| realtime            | subscription               | null                  | CHECK           |
| realtime            | subscription               | null                  | CHECK           |
| realtime            | subscription               | null                  | CHECK           |
| realtime            | subscription               | null                  | CHECK           |
| realtime            | subscription               | null                  | CHECK           |
| realtime            | subscription               | null                  | CHECK           |
| realtime            | messages                   | null                  | CHECK           |
| realtime            | messages                   | null                  | CHECK           |
| realtime            | messages                   | null                  | CHECK           |
| realtime            | messages                   | null                  | CHECK           |
| realtime            | messages                   | null                  | CHECK           |
| pgsodium            | key                        | null                  | CHECK           |
| pgsodium            | key                        | null                  | CHECK           |
| vault               | secrets                    | null                  | CHECK           |
| vault               | secrets                    | null                  | CHECK           |
| vault               | secrets                    | null                  | CHECK           |
| vault               | secrets                    | null                  | CHECK           |
| vault               | secrets                    | null                  | CHECK           |
| public              | goals                      | null                  | CHECK           |
| public              | goals                      | null                  | CHECK           |
| public              | goals                      | null                  | CHECK           |
| public              | goals                      | null                  | CHECK           |
| public              | goals                      | null                  | CHECK           |
| public              | goals                      | null                  | CHECK           |
| public              | goals                      | null                  | CHECK           |
| public              | goals                      | null                  | CHECK           |
| public              | teams                      | null                  | CHECK           |
| public              | teams                      | null                  | CHECK           |
| public              | teams                      | null                  | CHECK           |
| public              | teams                      | null                  | CHECK           |
| public              | team_members               | null                  | CHECK           |
| public              | team_members               | null                  | CHECK           |
| public              | kanban_columns             | null                  | CHECK           |
| public              | kanban_columns             | null                  | CHECK           |
| public              | kanban_columns             | null                  | CHECK           |
| public              | kanban_cards               | null                  | CHECK           |
| public              | kanban_cards               | null                  | CHECK           |
| public              | kanban_cards               | null                  | CHECK           |
| public              | kanban_cards               | null                  | CHECK           |
| public              | kanban_cards               | null                  | CHECK           |
| public              | kanban_comments            | null                  | CHECK           |
| public              | kanban_comments            | null                  | CHECK           |
| public              | kanban_comments            | null                  | CHECK           |
| public              | kanban_subtasks            | null                  | CHECK           |
| public              | kanban_subtasks            | null                  | CHECK           |
| public              | kanban_subtasks            | null                  | CHECK           |
| public              | profiles                   | null                  | CHECK           |
| supabase_migrations | schema_migrations          | null                  | CHECK           |