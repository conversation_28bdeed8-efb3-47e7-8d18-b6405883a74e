import { create } from 'zustand';
import { supabase } from '@/lib/supabase';
import type { TeamKanbanCard } from '@/features/teams/types/kanban';
import type { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { PriorityLevel } from '@/features/teams/types/kanban';

interface CardsByColumn {
    [columnId: string]: TeamKanbanCard[];
}

interface CardSubscriptions {
    [columnId: string]: RealtimeChannel;
}

interface KanbanCardState {
    cards: CardsByColumn;
    cardDetails: { [cardId: string]: TeamKanbanCard | null };
    loading: { [columnId: string]: boolean };
    error: string | null;
    hasMore: { [columnId: string]: boolean };
    page: { [columnId: string]: number };
    subscriptions: CardSubscriptions;

    // Card Loading
    loadColumnCards: (columnId: string) => Promise<void>;
    loadMoreCards: (columnId: string) => Promise<void>;

    // Card Operations
    getCard: (cardId: string) => TeamKanbanCard | undefined;
    getColumnCards: (columnId: string) => TeamKanbanCard[];
    setCard: (cardId: string, card: TeamKanbanCard) => void;
    setCards: (columnId: string, cards: TeamKanbanCard[]) => void;
    addCard: (columnId: string, teamId: string, title: string, description?: string) => Promise<void>;
    updateCard: (cardId: string, updates: Partial<TeamKanbanCard>) => Promise<void>;
    deleteCard: (cardId: string) => Promise<void>;
    moveCard: (cardId: string, newColumnId: string, newIndex: number) => Promise<void>;
    reorderCards: (columnId: string, cardIds: string[]) => Promise<void>;

    // Realtime
    handleRealtimeUpdate: (payload: RealtimePostgresChangesPayload<TeamKanbanCard>) => void;
    subscribeToColumn: (columnId: string) => void;
    unsubscribeFromColumn: (columnId: string) => void;
}

const CARDS_PER_PAGE = 10;

export const useKanbanCardStore = create<KanbanCardState>((set, get) => ({
    cards: {},
    cardDetails: {},
    loading: {},
    error: null,
    hasMore: {},
    page: {},
    subscriptions: {},

    loadColumnCards: async (columnId: string) => {
        try {
            set(state => ({
                loading: { ...state.loading, [columnId]: true },
                error: null
            }));

            const { data, error } = await supabase
                .from('kanban_cards')
                .select('*')
                .eq('column_id', columnId)
                .order('order_index')
                .limit(CARDS_PER_PAGE);

            if (error) throw error;

            set(state => ({
                cards: {
                    ...state.cards,
                    [columnId]: data || []
                },
                loading: {
                    ...state.loading,
                    [columnId]: false
                },
                hasMore: {
                    ...state.hasMore,
                    [columnId]: (data?.length || 0) === CARDS_PER_PAGE
                },
                page: {
                    ...state.page,
                    [columnId]: 1
                }
            }));
        } catch (error) {
            set(state => ({
                error: error instanceof Error ? error.message : 'Failed to load cards',
                loading: { ...state.loading, [columnId]: false }
            }));
        }
    },

    loadMoreCards: async (columnId: string) => {
        const state = get();
        if (!state.hasMore[columnId] || state.loading[columnId]) return;

        try {
            const currentPage = state.page[columnId] || 1;
            const offset = currentPage * CARDS_PER_PAGE;

            const { data, error } = await supabase
                .from('kanban_cards')
                .select('*')
                .eq('column_id', columnId)
                .order('order_index')
                .range(offset, offset + CARDS_PER_PAGE - 1);

            if (error) throw error;

            set(state => ({
                cards: {
                    ...state.cards,
                    [columnId]: [...(state.cards[columnId] || []), ...(data || [])]
                },
                hasMore: {
                    ...state.hasMore,
                    [columnId]: (data?.length || 0) === CARDS_PER_PAGE
                },
                page: {
                    ...state.page,
                    [columnId]: currentPage + 1
                }
            }));
        } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to load more cards' });
        }
    },

    getCard: (cardId: string) => {
        const state = get();
        return state.cardDetails[cardId] || Object.values(state.cards)
            .flat()
            .find(card => card.id === cardId);
    },

    getColumnCards: (columnId: string) => {
        return get().cards[columnId] || [];
    },

    setCard: (cardId: string, card: TeamKanbanCard) => {
        set(state => ({
            cardDetails: { ...state.cardDetails, [cardId]: card }
        }));
    },

    setCards: (columnId: string, cards: TeamKanbanCard[]) => {
        set(state => ({
            cards: { ...state.cards, [columnId]: cards }
        }));
    },

    addCard: async (columnId: string, teamId: string, title: string, description?: string) => {
        try {
            set({ error: null });
            const currentCards = get().getColumnCards(columnId);
            const newOrderIndex = currentCards.length;

            const { data, error } = await supabase
                .from('kanban_cards')
                .insert({
                    column_id: columnId,
                    team_id: teamId,
                    title,
                    description,
                    priority: PriorityLevel.P2,
                    order_index: newOrderIndex,
                    archived_at: null
                })
                .select()
                .single();

            if (error) throw error;

            set(state => ({
                cards: {
                    ...state.cards,
                    [columnId]: [...(state.cards[columnId] || []), data]
                },
                cardDetails: {
                    ...state.cardDetails,
                    [data.id]: data
                }
            }));
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to add card'
            });
        }
    },

    updateCard: async (cardId: string, updates: Partial<TeamKanbanCard>) => {
        const originalCard = get().getCard(cardId);
        if (!originalCard) return;

        // Optimistic update
        set(state => ({
            cardDetails: {
                ...state.cardDetails,
                [cardId]: { ...originalCard, ...updates }
            }
        }));

        try {
            const { data, error } = await supabase
                .from('kanban_cards')
                .update(updates)
                .eq('id', cardId)
                .select()
                .single();

            if (error) throw error;

            if (data) {
                set(state => ({
                    cardDetails: { ...state.cardDetails, [cardId]: data }
                }));
            }
        } catch (error) {
            // Revert on failure
            set(state => ({
                cardDetails: { ...state.cardDetails, [cardId]: originalCard },
                error: error instanceof Error ? error.message : 'Failed to update card'
            }));
        }
    },

    deleteCard: async (cardId: string) => {
        try {
            set({ error: null });
            const card = get().getCard(cardId);
            if (!card) return;

            const { error } = await supabase
                .from('kanban_cards')
                .delete()
                .eq('id', cardId);

            if (error) throw error;

            set(state => ({
                cards: {
                    ...state.cards,
                    [card.column_id]: state.cards[card.column_id]?.filter(c => c.id !== cardId) || []
                },
                cardDetails: {
                    ...state.cardDetails,
                    [cardId]: null
                }
            }));
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to delete card'
            });
        }
    },

    moveCard: async (cardId: string, newColumnId: string, newIndex: number) => {
        try {
            set({ error: null });
            const card = get().getCard(cardId);
            if (!card) return;

            const oldColumnId = card.column_id;
            const targetCards = get().getColumnCards(newColumnId);

            // Update order indices for cards in the target column
            const updatedCards = [...targetCards];
            updatedCards.splice(newIndex, 0, { ...card, column_id: newColumnId });
            const updates = updatedCards.map((c, index) => ({
                id: c.id,
                order_index: index,
                ...(c.id === cardId ? { column_id: newColumnId } : {})
            }));

            const { error } = await supabase
                .from('kanban_cards')
                .upsert(updates);

            if (error) throw error;

            // Update local state
            set(state => ({
                cards: {
                    ...state.cards,
                    [oldColumnId]: state.cards[oldColumnId]?.filter(c => c.id !== cardId) || [],
                    [newColumnId]: updatedCards
                },
                cardDetails: {
                    ...state.cardDetails,
                    [cardId]: { ...card, column_id: newColumnId, order_index: newIndex }
                }
            }));
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to move card'
            });
        }
    },

    reorderCards: async (columnId: string, cardIds: string[]) => {
        try {
            set({ error: null });
            const updates = cardIds.map((id, index) => ({
                id,
                order_index: index
            }));

            const { error } = await supabase
                .from('kanban_cards')
                .upsert(updates);

            if (error) throw error;

            const reorderedCards = cardIds.map((id, index) => {
                const card = get().getCard(id);
                return card ? { ...card, order_index: index } : null;
            }).filter((card): card is TeamKanbanCard => card !== null);

            set(state => ({
                cards: {
                    ...state.cards,
                    [columnId]: reorderedCards
                }
            }));
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to reorder cards'
            });
        }
    },

    handleRealtimeUpdate: (payload: RealtimePostgresChangesPayload<TeamKanbanCard>) => {
        const { eventType, new: newRecord, old: oldRecord } = payload;

        switch (eventType) {
            case 'INSERT':
                if (newRecord) {
                    set(state => ({
                        cards: {
                            ...state.cards,
                            [newRecord.column_id]: [
                                ...(state.cards[newRecord.column_id] || []),
                                newRecord
                            ]
                        }
                    }));
                }
                break;

            case 'UPDATE':
                if (newRecord) {
                    set(state => ({
                        cards: {
                            ...state.cards,
                            [newRecord.column_id]: state.cards[newRecord.column_id]?.map(card =>
                                card.id === newRecord.id ? newRecord : card
                            ) || []
                        },
                        cardDetails: {
                            ...state.cardDetails,
                            [newRecord.id]: newRecord
                        }
                    }));
                }
                break;

            case 'DELETE':
                if (oldRecord) {
                    set(state => ({
                        cards: {
                            ...state.cards,
                            [oldRecord.column_id || '']: (state.cards[oldRecord.column_id || ''] || [])?.filter((card: TeamKanbanCard) =>
                                card.id !== oldRecord.id
                            ) || []
                        },
                        cardDetails: {
                            ...state.cardDetails,
                            [oldRecord.id || '']: null
                        }
                    }));
                }
                break;
        }
    },

    subscribeToColumn: (columnId: string) => {
        const subscription = supabase
            .channel(`cards:${columnId}`)
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'kanban_cards',
                filter: `column_id=eq.${columnId}`
            }, (payload: RealtimePostgresChangesPayload<TeamKanbanCard>) => {
                get().handleRealtimeUpdate(payload);
            })
            .subscribe();

        set(state => ({
            subscriptions: {
                ...state.subscriptions,
                [columnId]: subscription
            }
        }));
    },

    unsubscribeFromColumn: (columnId: string) => {
        const subscription = get().subscriptions[columnId];
        if (subscription) {
            subscription.unsubscribe();
            set((state) => {
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                const { [columnId]: unused, ...rest } = state.subscriptions;
                return { subscriptions: rest };
            });
        }
    }
})); 