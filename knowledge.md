# Cadence.ai Knowledge File

## Project Overview

Cadence.ai is a team collaboration and task management application with a focus on OKR (Objectives and Key Results) implementation. The application features a Kanban board system for task management and team collaboration.

## Tech Stack

- React
- TypeScript
- Tailwind CSS
- Shadcn UI (built on Radix UI)
- Supabase (Database & Authentication)
- <PERSON><PERSON><PERSON> (State Management)
- React Router
- React Hook Form
- Date-fns
- DND Kit (Drag and Drop functionality)

## Architecture

### Directory Structure

```
src/
├── app/                    # App-wide setup and configuration
│   ├── providers/         # All context providers
│   ├── routes/           # Route definitions and configurations
│   └── layout/           # Root layout components
├── assets/               # Static assets (images, fonts, etc.)
├── components/
│   ├── ui/              # Reusable UI components (shadcn)
│   └── common/          # Shared components used across features
├── features/            # Feature-based modules
│   ├── auth/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── store/
│   │   ├── types/
│   │   └── __tests__/
│   ├── teams/
│   ├── dashboard/
│   ├── team-management/
│   ├── settings/
│   └── layout/
├── hooks/               # Global custom hooks
├── lib/                # Third-party library configurations
├── services/           # API and external service integrations
├── store/              # Global state management
├── styles/             # Global styles
├── types/              # Global TypeScript types
└── utils/              # Shared utility functions
```

## Key Features

### Kanban Board

The application features a Kanban board system for task management with:

- Columns and cards
- Drag and drop functionality
- Subtasks with assignees and due dates
- Comments
- Card details

### Data Update Patterns

#### Entity Updates

When updating entities (cards, subtasks, etc.), follow these patterns:

```typescript
// 1. Optimistic Update Pattern
const handleUpdate = async (id: string, data: UpdateData) => {
  // Create optimistic update
  const optimisticItem = { ...previousItem, ...updates };
  
  // Update UI immediately
  setState(state => ({
    items: state.items.map(item =>
      item.id === id ? optimisticItem : item
    )
  }));

  try {
    // Make API call
    const updatedItem = await api.updateItem(id, updates);
    
    // Generate any necessary system comments
    await generateSystemComments(previousItem, updatedItem);
    
    // Handle view transitions if needed
    if (isStateChange) {
      updateOppositeView(updatedItem);
    }
  } catch (error) {
    // Revert optimistic update on error
    setState(state => ({
      items: state.items.map(item =>
        item.id === id ? previousItem : item
      )
    }));
    throw error;
  }
};
```

#### Key Principles

1. User Experience:
   - Immediate UI feedback through optimistic updates
   - Smooth transitions between views
   - Consistent system comments for changes
   - Graceful error handling

2. Performance:
   - Minimize server requests
   - Avoid unnecessary refreshes
   - Update only affected state
   - Use direct state updates over refetching

3. Data Integrity:
   - Maintain consistent state across views
   - Handle edge cases in state transitions
   - Proper error recovery
   - Accurate system comment generation

## State Management

### Zustand Store Structure

The application uses Zustand for state management, with a focus on the slice pattern:

```typescript
// Store slice example
const createBoardSlice = (set, get) => ({
  // State
  columns: [],
  loading: false,
  error: null,

  // Actions
  fetchBoard: async (teamId) => {
    set({ loading: true, error: null });
    try {
      const data = await fetchData(teamId);
      set({ columns: data, loading: false });
    } catch (error) {
      set({ error: error.message, loading: false });
    }
  }
});

// Selectors
export const selectColumns = (state) => state.columns;
```

## Database Integration

### Schema Structure

Key tables and relationships:

```sql
-- Teams and Members
teams
  id uuid PK
  name text
  created_at timestamp

team_members
  team_id uuid FK >- teams.id
  user_id uuid FK >- profiles.id
  role text

-- Kanban Structure
kanban_columns
  id uuid PK
  team_id uuid FK >- teams.id
  title text
  order_index int

kanban_cards
  id uuid PK
  column_id uuid FK >- kanban_columns.id
  title text
  description text
  order_index int
  assignee_id uuid FK >- profiles.id NULL
  due_date timestamp NULL
  archived_at timestamp NULL

kanban_subtasks
  id uuid PK
  card_id uuid FK >- kanban_cards.id
  title text
  is_completed boolean
  order_index int
  assignee_id uuid FK >- profiles.id NULL
  due_date timestamp NULL
```

### Foreign Key Relationships

- All assignee_id fields reference profiles.id with ON DELETE SET NULL
- All hierarchical relationships (team->column->card->subtask) use ON DELETE CASCADE
- Indexes are maintained on frequently queried fields (assignee_id, order_index)

## Common Troubleshooting

### UI State Issues

1. Missing or incorrect data after updates:
   - Check store update logic preserves all fields
   - Verify database update succeeded
   - Ensure proper error handling and rollbacks

2. Stale data after operations:
   - Verify optimistic updates are properly implemented
   - Check subscription/refresh logic
   - Validate store selector memoization

### Database Issues

1. Foreign key violations:
   - Verify NULL handling for optional relationships
   - Check cascade delete settings
   - Validate data before updates

2. Race conditions:
   - Use proper ordering for batch operations
   - Implement proper locking mechanisms
   - Handle concurrent updates gracefully

## Testing

The application includes various testing strategies:

- Unit tests for utilities and hooks
- Component tests for UI components
- Integration tests for API operations
- Test for Supabase RLS policies

## Styling

- Tailwind CSS for styling
- Shadcn UI components
- Mobile-first responsive design
- CSS variables for theming

## Key Takeaways

1. Follow established patterns for data updates
2. Preserve data integrity in store updates
3. Handle errors gracefully
4. Use optimistic updates for better UX
5. Maintain proper separation of concerns
