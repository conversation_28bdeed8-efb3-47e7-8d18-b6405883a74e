import { render, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter } from 'react-router-dom';
import { SessionContextProvider } from '@supabase/auth-helpers-react';
import { supabase } from '@/lib/supabase';
import { AppRoutes } from '@/app/routes';
import { SidebarProvider } from '@/app/context/SidebarContext';
import { AuthProvider } from '@/features/auth/components/AuthProvider';
import { Toaster } from '@/components/ui/toaster';
import { vi } from 'vitest';

// Mock the useUser hook
vi.mock('@supabase/auth-helpers-react', async () => {
    const actual = await vi.importActual('@supabase/auth-helpers-react');
    return {
        ...actual,
        useUser: () => ({
            id: '123',
            email: '<EMAIL>',
            user_metadata: { name: 'Test User' }
        })
    };
});

// Mock the auth store
vi.mock('@/features/auth/store/authStore', () => ({
    useAuthStore: () => ({
        user: {
            id: '123',
            email: '<EMAIL>',
            user_metadata: { name: 'Test User' }
        },
        loading: false,
        error: null,
        checkAuth: vi.fn()
    })
}));

const TestApp = () => {
    return (
        <SessionContextProvider supabaseClient={supabase}>
            <SidebarProvider>
                <AuthProvider>
                    <AppRoutes />
                    <Toaster />
                </AuthProvider>
            </SidebarProvider>
        </SessionContextProvider>
    );
};

const renderWithRouter = (initialEntries = ['/teams']) => {
    const user = userEvent.setup();
    return {
        user,
        ...render(
            <MemoryRouter initialEntries={initialEntries}>
                <TestApp />
            </MemoryRouter>
        )
    };
};

describe('Sidebar Integration', () => {
    beforeEach(() => {
        vi.useFakeTimers({ shouldAdvanceTime: true });
        // Mock ResizeObserver
        window.ResizeObserver = vi.fn().mockImplementation(() => ({
            observe: vi.fn(),
            unobserve: vi.fn(),
            disconnect: vi.fn(),
        }));
        // Set initial viewport width
        window.innerWidth = 1024;
        // Clear localStorage
        localStorage.clear();
        // Set initial sidebar state
        localStorage.setItem('sidebarCollapsed', 'false');
    });

    afterEach(() => {
        vi.useRealTimers();
        window.innerWidth = 1024; // Reset to desktop viewport
        vi.resetAllMocks();
        localStorage.clear();
    });

    it('should maintain consistent state across route changes', async () => {
        const { user, getByTestId } = renderWithRouter();

        // Wait for initial render and ensure sidebar is mounted
        await act(async () => {
            await vi.advanceTimersByTimeAsync(2000);
        });

        const sidebar = await waitFor(() => getByTestId('sidebar'), { timeout: 20000 });
        expect(sidebar).toBeInTheDocument();

        const toggleButton = await waitFor(() => getByTestId('sidebar-toggle'), { timeout: 20000 });
        expect(toggleButton).toBeInTheDocument();

        // Initial state check
        expect(sidebar).toHaveClass('w-64');

        // Toggle sidebar
        await act(async () => {
            await user.click(toggleButton);
            await vi.advanceTimersByTimeAsync(2000);
        });

        // Check collapsed state
        expect(sidebar).toHaveClass('w-16');

        // Navigate to settings
        const settingsLink = await waitFor(() => getByTestId('nav-item-settings'), { timeout: 20000 });
        await act(async () => {
            await user.click(settingsLink);
            await vi.advanceTimersByTimeAsync(2000);
        });

        // Verify sidebar maintains collapsed state after navigation
        expect(sidebar).toHaveClass('w-16');
    }, 30000);

    it('should handle responsive behavior correctly', async () => {
        const { getByTestId } = renderWithRouter();

        // Wait for initial render and ensure sidebar is mounted
        await act(async () => {
            await vi.advanceTimersByTimeAsync(2000);
        });

        const sidebar = await waitFor(() => getByTestId('sidebar'), { timeout: 20000 });
        expect(sidebar).toBeInTheDocument();

        // Initial desktop state
        expect(sidebar).toHaveClass('w-64');

        // Simulate mobile viewport (using 767px to be just below the 768px breakpoint)
        await act(async () => {
            window.innerWidth = 767;
            window.dispatchEvent(new Event('resize'));
            await vi.advanceTimersByTimeAsync(2000);
        });

        // Wait for the state to update and check mobile state
        await waitFor(() => {
            expect(sidebar).toHaveClass('w-16');
        }, { timeout: 5000 });

        // Simulate desktop viewport
        await act(async () => {
            window.innerWidth = 1024;
            window.dispatchEvent(new Event('resize'));
            await vi.advanceTimersByTimeAsync(2000);
        });

        // Wait for the state to update and check desktop state
        await waitFor(() => {
            expect(sidebar).toHaveClass('w-64');
        }, { timeout: 5000 });
    }, 30000);
});