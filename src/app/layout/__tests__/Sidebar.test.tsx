import { render, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import Sidebar from '../Sidebar';
import { SidebarProvider } from '../../context/SidebarContext';

const renderWithProviders = (component: React.ReactNode) => {
    return render(
        <MemoryRouter initialEntries={['/teams']}>
            <SidebarProvider>
                {component}
            </SidebarProvider>
        </MemoryRouter>
    );
};

describe('Sidebar Component', () => {
    beforeEach(() => {
        vi.useFakeTimers({ shouldAdvanceTime: true });
        localStorage.clear();
        localStorage.setItem('sidebarCollapsed', 'false');
        window.innerWidth = 1024; // Set desktop width by default
        // Mock ResizeObserver
        window.ResizeObserver = vi.fn().mockImplementation(() => ({
            observe: vi.fn(),
            unobserve: vi.fn(),
            disconnect: vi.fn(),
        }));
    });

    afterEach(() => {
        vi.useRealTimers();
        localStorage.clear();
        window.innerWidth = 1024; // Reset to desktop
        vi.clearAllTimers();
    });

    const setupSidebar = async () => {
        const utils = renderWithProviders(<Sidebar />);
        // Process initial mount effects
        await act(async () => {
            window.dispatchEvent(new Event('resize'));
            await vi.runAllTimersAsync();
        });
        return utils;
    };

    // Basic rendering and interaction tests
    it('should render with default expanded state', async () => {
        const { container } = await setupSidebar();
        await act(async () => {
            await vi.runAllTimersAsync();
        });

        const sidebar = container.querySelector('[data-testid="sidebar"]');
        expect(sidebar).toBeTruthy();
        expect(sidebar).toHaveClass('w-64');
    });

    it('should toggle collapse state when toggle button is clicked', async () => {
        const { container } = await setupSidebar();
        await act(async () => {
            await vi.runAllTimersAsync();
        });

        const toggleButton = container.querySelector('[data-testid="sidebar-toggle"]');
        const sidebar = container.querySelector('[data-testid="sidebar"]');
        expect(toggleButton).not.toBeNull();
        expect(sidebar).not.toBeNull();

        // Test collapse
        if (toggleButton) {
            await act(async () => {
                if (toggleButton) await userEvent.click(toggleButton);
                await vi.runAllTimersAsync();
            });
        }

        expect(sidebar).toHaveClass('w-16');
        expect(localStorage.getItem('sidebarCollapsed')).toBe('true');

        // Test expand
        if (toggleButton) {
            await act(async () => {
                if (toggleButton) await userEvent.click(toggleButton);
                await vi.runAllTimersAsync();
            });
        }

        expect(sidebar).toHaveClass('w-64');
        expect(localStorage.getItem('sidebarCollapsed')).toBe('false');
    });

    // Title Display Tests
    describe('Sidebar Title', () => {
        it('should display "cadence.ai" when expanded', async () => {
            localStorage.setItem('sidebarCollapsed', 'false');
            const { container } = await setupSidebar();
            await act(async () => {
                await vi.runAllTimersAsync();
            });

            const title = container.querySelector('h1');
            expect(title).toBeTruthy();
            expect(title).toHaveTextContent('cadence.ai');
            expect(title).toHaveClass('w-auto');
        });

        it('should display "c." when collapsed', async () => {
            const { container } = await setupSidebar();
            await act(async () => {
                await vi.runAllTimersAsync();
            });

            const toggleButton = container.querySelector('[data-testid="sidebar-toggle"]');
            const title = container.querySelector('h1');
            expect(toggleButton).not.toBeNull();
            expect(title).not.toBeNull();
            expect(title).toHaveTextContent('cadence.ai');

            await act(async () => {
                if (toggleButton) await userEvent.click(toggleButton);
                await vi.runAllTimersAsync();
            });

            expect(title).toHaveTextContent('c.');
            expect(title).toHaveClass('w-[2.2ch]');
            expect(title).not.toHaveClass('opacity-0');
        });

        it('should maintain title visibility during transition', async () => {
            const { container } = await setupSidebar();
            await act(async () => {
                await vi.runAllTimersAsync();
            });

            const toggleButton = container.querySelector('[data-testid="sidebar-toggle"]');
            const title = container.querySelector('h1');
            expect(toggleButton).not.toBeNull();
            expect(title).not.toBeNull();
            expect(title).toHaveTextContent('cadence.ai');
            expect(title).toBeVisible();

            await act(async () => {
                if (toggleButton) await userEvent.click(toggleButton);
                await vi.advanceTimersByTimeAsync(250);
            });

            expect(title).toBeVisible();
            expect(title).toHaveClass('transition-all');

            await act(async () => {
                await vi.advanceTimersByTimeAsync(250);
            });

            expect(title).toHaveTextContent('c.');
            expect(title).toBeVisible();
        });
    });

    // Responsive Behavior Tests
    describe('Sidebar Title Responsive Behavior', () => {
        it('should show "c." when auto-collapsed on mobile', async () => {
            const { container } = await setupSidebar();
            await act(async () => {
                await vi.runAllTimersAsync();
            });

            const title = container.querySelector('h1');
            expect(title).toBeTruthy();
            expect(title).toHaveTextContent('cadence.ai');

            await act(async () => {
                window.innerWidth = 767;
                window.dispatchEvent(new Event('resize'));
                await vi.runAllTimersAsync();
            });

            expect(title).toHaveTextContent('c.');
            expect(title).toHaveClass('w-[2.2ch]');
            expect(title).not.toHaveClass('opacity-0');
        });

        it('should restore previous title state when returning to desktop', async () => {
            const { container } = await setupSidebar();
            await act(async () => {
                await vi.runAllTimersAsync();
            });

            const title = container.querySelector('h1');
            expect(title).toBeTruthy();
            expect(title).toHaveTextContent('cadence.ai');

            await act(async () => {
                window.innerWidth = 767;
                window.dispatchEvent(new Event('resize'));
                await vi.runAllTimersAsync();
            });

            expect(title).toHaveTextContent('c.');

            await act(async () => {
                window.innerWidth = 1024;
                window.dispatchEvent(new Event('resize'));
                await vi.runAllTimersAsync();
            });

            expect(title).toHaveTextContent('cadence.ai');
        });
    });

    // State Persistence Tests
    describe('Sidebar Title State Persistence', () => {
        beforeEach(() => {
            localStorage.clear();
        });

        it('should maintain collapsed title state after page reload', async () => {
            const { container, unmount } = await setupSidebar();
            await act(async () => {
                await vi.runAllTimersAsync();
            });

            const toggleButton = container.querySelector('[data-testid="sidebar-toggle"]');
            expect(toggleButton).not.toBeNull();

            await act(async () => {
                if (toggleButton) await userEvent.click(toggleButton);
                await vi.runAllTimersAsync();
            });

            unmount();

            const { container: newContainer } = await setupSidebar();
            await act(async () => {
                await vi.runAllTimersAsync();
            });

            const titleAfterReload = newContainer.querySelector('h1');
            expect(titleAfterReload).toBeTruthy();
            expect(titleAfterReload).toHaveTextContent('c.');
            expect(titleAfterReload).not.toHaveClass('opacity-0');
        });
    });

    // Styling and Accessibility Tests
    it('should have consistent styling across all instances', async () => {
        const { container } = await setupSidebar();
        await act(async () => {
            await vi.runAllTimersAsync();
        });

        const sidebar = container.querySelector('[data-testid="sidebar"]');
        expect(sidebar).toBeTruthy();
        expect(sidebar).toHaveClass('bg-background', 'border-r', 'border-border');
    });

    it('should highlight active navigation item', async () => {
        const { container } = await setupSidebar();
        await act(async () => {
            await vi.runAllTimersAsync();
        });

        const navItem = container.querySelector('[data-testid="nav-item-teams"]');
        expect(navItem).toBeTruthy();
        expect(navItem).toHaveClass('bg-accent/50', 'text-primary');
    });

    it('should be keyboard navigable', async () => {
        const { container } = await setupSidebar();
        await act(async () => {
            await vi.runAllTimersAsync();
        });

        const teamsNavItem = container.querySelector<HTMLElement>('[data-testid="nav-item-teams"]');
        const healthMetricsNavItem = container.querySelector<HTMLElement>('[data-testid="nav-item-health-metrics"]');
        const teamUpdatesNavItem = container.querySelector<HTMLElement>('[data-testid="nav-item-team-updates"]');
        const settingsNavItem = container.querySelector<HTMLElement>('[data-testid="nav-item-settings"]');
        expect(teamsNavItem).toBeTruthy();
        expect(healthMetricsNavItem).toBeTruthy();
        expect(teamUpdatesNavItem).toBeTruthy();
        expect(settingsNavItem).toBeTruthy();

        teamsNavItem?.focus();
        expect(document.activeElement).toBe(teamsNavItem);

        await userEvent.tab();
        expect(document.activeElement).toBe(healthMetricsNavItem);

        await userEvent.tab();
        expect(document.activeElement).toBe(teamUpdatesNavItem);

        await userEvent.tab();
        expect(document.activeElement).toBe(settingsNavItem);
    });

    it('should have proper ARIA attributes', async () => {
        const { container } = await setupSidebar();
        await act(async () => {
            await vi.runAllTimersAsync();
        });

        const navigation = container.querySelector('nav');
        expect(navigation).toBeTruthy();
        expect(navigation).toHaveAttribute('aria-label', 'Main Navigation');
    });

    // Title Visibility Tests
    describe('Sidebar Title Visibility', () => {
        it('should maintain title visibility in mobile view', async () => {
            const { container } = await setupSidebar();
            await act(async () => {
                await vi.runAllTimersAsync();
            });

            const title = container.querySelector('h1');
            expect(title).toBeTruthy();
            expect(title).toHaveTextContent('cadence.ai');

            await act(async () => {
                window.innerWidth = 767;
                window.dispatchEvent(new Event('resize'));
                await vi.runAllTimersAsync();
            });

            expect(title).toHaveTextContent('c.');
            expect(title).toBeVisible();
            expect(title).toHaveClass('w-[2.2ch]');
            expect(title).not.toHaveClass('opacity-0');
        });
    });
});