import React from 'react';
import { NavLink } from 'react-router-dom';
import { Users, Settings, ChevronLeft, BarChart3, Activity } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useSidebar } from '../context/SidebarContext';
import { cn } from '@/lib/utils';

const navItems = [
    { icon: Users, label: 'Teams', path: '/teams', testId: 'nav-item-teams' },
    { icon: Activity, label: 'Health Metrics', path: '/health-metrics', testId: 'nav-item-health-metrics' },
    { icon: BarChart3, label: 'Team Updates', path: '/team-updates', testId: 'nav-item-team-updates' },
    { icon: Settings, label: 'Settings', path: '/settings', testId: 'nav-item-settings' }
];

const Sidebar = React.memo(() => {
    const { isCollapsed, toggleCollapse } = useSidebar();

    return (
        <aside
            data-testid="sidebar"
            className={cn(
                'bg-background border-r border-border transition-[width] duration-500 ease-in-out relative',
                isCollapsed ? 'w-16' : 'w-64'
            )}
        >
            <div className="h-16 flex items-center justify-between px-4 border-b border-border">
                <h1 className={cn(
                    'text-xl font-bold text-primary transition-all duration-300 overflow-hidden whitespace-nowrap min-w-[2ch]',
                    isCollapsed ? 'w-[2.2ch]' : 'w-auto'
                )}>
                    {isCollapsed ? 'c.' : 'cadence.ai'}
                </h1>
                <button
                    data-testid="sidebar-toggle"
                    onClick={toggleCollapse}
                    className="p-2 hover:bg-accent rounded-md text-muted-foreground hover:text-foreground"
                    aria-label="Toggle Sidebar"
                >
                    <ChevronLeft className={cn(
                        'w-4 h-4 transition-transform duration-300',
                        isCollapsed && 'rotate-180'
                    )} />
                </button>
            </div>

            <ScrollArea data-testid="scroll-area" className="p-4 h-[calc(100vh-4rem)]">
                <nav aria-label="Main Navigation">
                    <ul className="space-y-2">
                        {navItems.map((item, index) => {
                            const Icon = item.icon;
                            return (
                                <li key={item.path}>
                                    <NavLink
                                        to={item.path}
                                        data-testid={item.testId || `nav-item-${index === 0 ? 'first' : 'second'}`}
                                        className={({ isActive }) => cn(
                                            'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                                            isActive
                                                ? 'bg-accent/50 text-primary'
                                                : 'text-muted-foreground hover:bg-accent hover:text-foreground'
                                        )}
                                    >
                                        <Icon className="w-4 h-4 min-w-[16px]" />
                                        <span className={cn(
                                            'ml-3 transition-all duration-300',
                                            isCollapsed ? 'opacity-0 w-0' : 'opacity-100 w-auto'
                                        )}>
                                            {item.label}
                                        </span>
                                    </NavLink>
                                </li>
                            );
                        })}
                    </ul>
                </nav>
            </ScrollArea>
        </aside>
    );
});

Sidebar.displayName = 'Sidebar';

export default Sidebar;