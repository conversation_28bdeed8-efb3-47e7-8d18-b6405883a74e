import React, { useContext, useState, useEffect } from 'react';
import { SidebarContext, SidebarContextType } from './sidebarContext.types';

export const SidebarProvider = ({ children }: { children: React.ReactNode }) => {
    const [isCollapsed, setIsCollapsed] = useState(() => {
        const stored = localStorage.getItem('sidebarCollapsed');
        return stored ? JSON.parse(stored) : false;
    });
    const [isResponsiveCollapse, setIsResponsiveCollapse] = useState(false);

    useEffect(() => {
        // Handle responsive behavior
        const handleResize = () => {
            if (window.innerWidth < 768) {
                setIsResponsiveCollapse(true);
                setIsCollapsed(true);
            } else {
                setIsResponsiveCollapse(false);
                // Only restore previous state if it was collapsed due to responsive behavior
                if (isResponsiveCollapse) {
                    const stored = localStorage.getItem('sidebarCollapsed');
                    setIsCollapsed(stored ? JSON.parse(stored) : false);
                }
            }
        };

        window.addEventListener('resize', handleResize);
        handleResize(); // Check initial size

        return () => window.removeEventListener('resize', handleResize);
    }, [isResponsiveCollapse]);

    useEffect(() => {
        // Only persist manual collapse state
        if (!isResponsiveCollapse) {
            localStorage.setItem('sidebarCollapsed', JSON.stringify(isCollapsed));
        }
    }, [isCollapsed, isResponsiveCollapse]);

    const toggleCollapse = () => {
        if (!isResponsiveCollapse) {
            setIsCollapsed((prev: boolean) => !prev);
        }
    };

    return (
        <SidebarContext.Provider value={{ isCollapsed, toggleCollapse }}>
            {children}
        </SidebarContext.Provider>
    );
};

export const useSidebar = (): SidebarContextType => {
    const context = useContext(SidebarContext);
    if (context === undefined) {
        throw new Error('useSidebar must be used within a SidebarProvider');
    }
    return context;
}; 