import { useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { useAuthStore } from '@/store/auth.store'
import { useUserStore } from '@/store/user.store'

export function AuthProvider({ children }: { children: React.ReactNode }) {
    const { setUser, setSession, setLoading } = useAuthStore()
    const { getProfile } = useUserStore()

    useEffect(() => {
        // Get initial session
        supabase.auth.getSession().then(({ data: { session } }) => {
            setSession(session)
            setUser(session?.user ?? null)
            if (session?.user) {
                getProfile(session.user.id).catch(console.error)
            }
            setLoading(false)
        })

        // Listen for auth changes
        const {
            data: { subscription },
        } = supabase.auth.onAuthStateChange(async (event, session) => {
            setSession(session)
            setUser(session?.user ?? null)

            if (event === 'SIGNED_IN' && session?.user) {
                // Pre-fetch user profile on sign in
                await getProfile(session.user.id)
            }

            if (event === 'SIGNED_OUT') {
                useUserStore.getState().clearCache()
            }
        })

        return () => {
            subscription.unsubscribe()
        }
    }, [getProfile, setLoading, setSession, setUser])

    return children
}

export default AuthProvider 