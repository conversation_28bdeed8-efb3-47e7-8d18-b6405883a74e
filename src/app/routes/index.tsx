import { Routes, Route, Navigate } from 'react-router-dom';
import MainLayout from '@/app/layout/MainLayout';
import { Teams, TeamDetail } from '@/features/teams';
import { Settings } from '@/features/settings';
import { TeamManagement } from '@/features/team-management';
import { ResetPassword, AuthCallback } from '@/features/auth';
import { TeamUpdates } from '@/features/teamUpdates';
import { HealthMetricsRoutes } from '@/features/health-metrics/routes';
import { AllHealthMetricsPage } from '@/features/health-metrics';

export const AppRoutes = () => {
    return (
        <Routes>
            {/* Public routes */}
            <Route path="/" element={<Navigate to="/teams" replace />} />
            <Route path="/auth/reset-password" element={<ResetPassword />} />
            <Route path="/auth/callback" element={<AuthCallback />} />

            {/* Protected routes */}
            <Route element={<MainLayout />}>
                <Route path="/teams" element={<Teams />} />
                <Route path="/team/:teamId" element={<TeamDetail />} />
                <Route path="/team-updates" element={<TeamUpdates />} />
                <Route path="/settings" element={<Settings />} />
                <Route path="/team-management" element={<TeamManagement />} />
                <Route path="/health-metrics" element={<AllHealthMetricsPage />} />
                <Route path="/teams/:teamId/health-metrics/*" element={<HealthMetricsRoutes />} />
            </Route>

            {/* Catch all route - redirect to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
    );
};