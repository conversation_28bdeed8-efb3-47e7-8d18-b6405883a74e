import { BrowserRouter } from 'react-router-dom';
import { SessionContextProvider } from '@supabase/auth-helpers-react';
import { supabase } from '@/lib/supabase';
import { AppRoutes } from './routes/index';
import { SidebarProvider } from './context/SidebarContext';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider } from '@/features/auth/components/AuthProvider';

export const App = () => {
    return (
        <SessionContextProvider supabaseClient={supabase}>
            <SidebarProvider>
                <BrowserRouter>
                    <AuthProvider>
                        <AppRoutes />
                        <Toaster />
                    </AuthProvider>
                </BrowserRouter>
            </SidebarProvider>
        </SessionContextProvider>
    );
}; 