import { supabase } from '@/lib/supabase';

export interface TeamWeeklyUpdate {
  id: number;
  team_id: string;
  team_update_summary: string;
  start_date: string;
  end_date: string;
  week_number?: string;
  team_updates?: TeamActivityData;
}

export interface TeamActivityData {
  period: {
    start: string;
    end: string;
  };
  completed_tasks: TeamTaskCard[];
  card_movements: TeamTaskCard[];
  new_cards: TeamTaskCard[];
  subtask_updates: TeamSubtaskUpdate[];
  comments: TeamComment[];
  health_metrics?: TeamHealthMetric[];
}

export interface TeamHealthMetric {
  id: string;
  name: string;
  value: number;
  status: 'green' | 'yellow' | 'red';
  unit?: string;
  updated_at: string;
}

export interface TeamTaskCard {
  card_id: string;
  card_title: string;
  completed_at?: string;
  completed_by?: string;
  archived_at?: string;
  moved_at?: string;
  moved_by?: string;
  current_column?: string;
  created_at?: string;
  created_by?: string;
  column_title?: string;
}

export interface TeamSubtaskUpdate {
  subtask_id: string;
  subtask_title: string;
  card_id: string;
  card_title: string;
  updated_at: string;
  is_completed: boolean;
  updated_by: string;
}

export interface TeamComment {
  comment_id: string;
  card_id: string;
  card_title: string;
  content: string;
  created_at: string;
  is_system: boolean;
  commented_by: string;
}

export interface Team {
  id: string;
  name: string;
  avatar_url?: string;
}

interface TeamResponse {
  team_id: string;
  teams: {
    id: string;
    name: string;
    avatar_url?: string;
  };
}

// Use a more generic approach to handle the teams data shape
export const teamUpdatesApi = {
  // Get all teams for a user (regular users see their teams, admins see all)
  getTeams: async (): Promise<Team[]> => {
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error('Error fetching user:', userError);
      return [];
    }

    const userId = userData?.user?.id;

    if (!userId) {
      return [];
    }

    // Check if user is admin
    const { data: profileData } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    const isAdmin = profileData?.role === 'admin';

    if (isAdmin) {
      // Admins can see all teams
      const { data: teams, error } = await supabase
        .from('teams')
        .select('id, name, avatar_url');

      if (error) {
        console.error('Error fetching teams:', error);
        return [];
      }

      return teams || [];
    } else {
      // Regular users see only their teams
      const { data, error } = await supabase
        .from('team_members')
        .select(`
          team_id,
          teams:team_id (id, name, avatar_url)
        `)
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching team members:', error);
        return [];
      }

      // Transform the data safely
      const teamList: Team[] = [];

      if (data) {
        // Type assertion for the response data
        const teamMembers = data as unknown as TeamResponse[];

        for (const teamMember of teamMembers) {
          if (teamMember.teams) {
            teamList.push({
              id: teamMember.teams.id,
              name: teamMember.teams.name,
              avatar_url: teamMember.teams.avatar_url
            });
          }
        }
      }

      return teamList;
    }
  },

  // Get weekly updates for a specific team and date range
  getTeamWeeklyUpdate: async (teamId: string, startDate: string, endDate: string): Promise<TeamWeeklyUpdate | null> => {
    // Get the latest update by ordering by created_at in descending order and limiting to 1
    const { data, error } = await supabase
      .from('team_weekly_updates')
      .select('id, team_id, team_update_summary, start_date, end_date, week_number, team_updates')
      .eq('team_id', teamId)
      .gte('start_date', startDate)
      .lte('end_date', endDate)
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error('Error fetching team weekly update:', error);
      return null;
    }

    return data || null;
  }
};