import { useEffect, useState } from 'react'
import { useAuthStore } from '@/features/auth'

export function useUser(userId?: string) {
    const { user: currentUser, userProfile, fetchUserProfile, loading, error } = useAuthStore()
    const [isInitialLoad, setIsInitialLoad] = useState(true)

    const targetUserId = userId || currentUser?.id

    useEffect(() => {
        async function loadProfile() {
            if (targetUserId && (isInitialLoad || !userProfile)) {
                await fetchUserProfile()
                setIsInitialLoad(false)
            }
        }

        loadProfile()
    }, [targetUserId, isInitialLoad, userProfile, fetchUserProfile])

    return {
        user: userProfile,
        isLoading: loading && isInitialLoad,
        error,
        isAuthenticated: !!currentUser,
    }
}

export default useUser 