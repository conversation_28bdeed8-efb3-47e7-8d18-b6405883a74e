import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { TeamSidebar } from './TeamSidebar/index';
import { TeamMembersTable } from './TeamMembersTable/index';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, ArrowLeft } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";
import { useTeamManagerStore } from '../store/teamManagerStore';
import { Team } from '@/features/teams/types/team';
import { AddTeamMembersDialog } from './AddTeamMembersDialog';

export const TeamManagement = () => {
    const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
    const { loading, error, fetchTeams, fetchMembers } = useTeamManagerStore();
    const { toast } = useToast();
    const navigate = useNavigate();
    const [isAddMembersDialogOpen, setIsAddMembersDialogOpen] = useState(false);

    useEffect(() => {
        const loadData = async () => {
            try {
                await Promise.all([
                    fetchTeams(),
                    fetchMembers()
                ]);
            } catch (err) {
                const error = err as Error;
                toast({
                    title: 'Error',
                    description: error.message || 'Failed to load data. Please try again.',
                    variant: 'destructive',
                });
            }
        };

        loadData();
    }, [fetchTeams, fetchMembers, toast]);

    if (error) {
        return (
            <div className="flex items-center justify-center h-full p-8">
                <Card className="p-6">
                    <CardContent>
                        <p className="text-destructive">Error loading team data: {error}</p>
                        <button
                            onClick={() => fetchTeams()}
                            className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
                        >
                            Retry
                        </button>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="space-y-6 p-8">
            <div className="flex items-center space-x-4">
                <button
                    onClick={() => navigate(-1)}
                    className="p-2 hover:bg-accent rounded-full transition-colors"
                    aria-label="Go back"
                >
                    <ArrowLeft className="h-6 w-6" />
                </button>
                <div>
                    <h2 className="text-2xl font-semibold tracking-tight">Team Management</h2>
                    <p className="text-muted-foreground">
                        Manage and organize your team
                    </p>
                </div>
            </div>

            {loading ? (
                <div className="flex items-center justify-center h-32">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
            ) : (
                <div className="flex h-[calc(100vh-12rem)] bg-white rounded-lg shadow overflow-hidden">
                    <TeamSidebar onTeamSelect={setSelectedTeam} />
                    <div className="flex-1 overflow-y-auto">
                        <TeamMembersTable
                            team={selectedTeam}
                            onAddMemberClick={() => setIsAddMembersDialogOpen(true)}
                        />
                    </div>
                </div>
            )}

            <AddTeamMembersDialog
                team={selectedTeam}
                open={isAddMembersDialogOpen}
                onOpenChange={setIsAddMembersDialogOpen}
            />
        </div>
    );
}; 