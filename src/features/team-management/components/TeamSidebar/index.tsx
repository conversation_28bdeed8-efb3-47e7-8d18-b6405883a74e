import React from 'react';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useTeamManagerStore } from '../../store/teamManagerStore';
import { Team } from '@/features/teams/types/team';
import { MoreHorizontal, Plus } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

export const TeamSidebar = ({ onTeamSelect }: { onTeamSelect: (team: Team) => void }) => {
    const { teams, createTeam, updateTeam, currentUserRole } = useTeamManagerStore();
    const { toast } = useToast();
    const [isUpdatingStatus, setIsUpdatingStatus] = React.useState(false);
    const [searchQuery, setSearchQuery] = React.useState('');
    const [showNewTeamForm, setShowNewTeamForm] = React.useState(false);
    const [newTeamName, setNewTeamName] = React.useState('');
    const [newTeamObjective, setNewTeamObjective] = React.useState('');
    const [selectedTeamId, setSelectedTeamId] = React.useState<string | null>(null);
    const [editingTeam, setEditingTeam] = React.useState<Team | null>(null);

    // Helper function to check if user has admin privileges
    const isAdmin = currentUserRole === 'admin';

    const handleCreateTeam = async (name: string) => {
        try {
            await createTeam({ name, objective: newTeamObjective });
            toast({
                title: "Success",
                description: "New team has been created successfully.",
            });
            setShowNewTeamForm(false);
            setNewTeamName('');
            setNewTeamObjective('');
        } catch {
            toast({
                title: "Error",
                description: "Failed to create team.",
                variant: "destructive",
            });
        }
    };

    const handleUpdateTeam = async (id: string, name: string) => {
        try {
            await updateTeam(id, {
                name,
                objective: newTeamObjective
            });
            toast({
                title: "Success",
                description: "Team has been updated successfully.",
            });
            setShowNewTeamForm(false);
            setNewTeamName('');
            setNewTeamObjective('');
            setEditingTeam(null);
        } catch {
            toast({
                title: "Error",
                description: "Failed to update team.",
                variant: "destructive",
            });
        }
    };

    const handleTeamAction = async (name: string) => {
        try {
            if (editingTeam) {
                await handleUpdateTeam(editingTeam.id, name);
            } else {
                await handleCreateTeam(name);
            }
        } catch {
            toast({
                title: "Error",
                description: "Failed to perform team action.",
                variant: "destructive",
            });
        }
    };

    const handleCancelForm = () => {
        setShowNewTeamForm(false);
        setNewTeamName('');
        setNewTeamObjective('');
        setEditingTeam(null);
    };

    const handleCreateTeamClick = () => {
        setShowNewTeamForm(true);
    };

    const filteredTeams = teams
        .filter(team =>
            team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            team.objective?.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .sort((a, b) => {
            // Sort by status: 'Active' teams come before 'Inactive' teams
            if (a.status === 'Active' && b.status === 'Inactive') return -1;
            if (a.status === 'Inactive' && b.status === 'Active') return 1;
            return 0;
        });

    const handleTeamClick = (team: Team) => {
        setSelectedTeamId(team.id);
        onTeamSelect(team);
    };

    return (
        <div className="w-80 border-r h-full bg-background p-4 flex flex-col">
            <div className="space-y-4">
                <div className="flex items-center space-x-2">
                    <Input
                        placeholder="Search teams..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="flex-1"
                    />
                    {isAdmin && (
                        <Button
                            size="icon"
                            variant="outline"
                            onClick={handleCreateTeamClick}
                        >
                            <Plus className="h-4 w-4" />
                        </Button>
                    )}
                </div>

                {showNewTeamForm && (
                    <div className="space-y-4 p-4 border rounded-lg">
                        <h3 className="font-medium">{editingTeam ? 'Edit Team' : 'Create New Team'}</h3>
                        <Input
                            placeholder="Team name"
                            value={newTeamName}
                            onChange={(e) => setNewTeamName(e.target.value)}
                        />
                        <Input
                            placeholder="Objective"
                            value={newTeamObjective}
                            onChange={(e) => setNewTeamObjective(e.target.value)}
                        />
                        <div className="flex justify-end space-x-2">
                            <Button
                                variant="outline"
                                onClick={handleCancelForm}
                            >
                                Cancel
                            </Button>
                            <Button onClick={() => handleTeamAction(newTeamName)}>
                                {editingTeam ? 'Update' : 'Create'}
                            </Button>
                        </div>
                    </div>
                )}

                <div className="space-y-2 overflow-y-auto max-h-[calc(100vh-200px)]">
                    {filteredTeams.map((team) => (
                        <div
                            key={team.id}
                            className={`p-3 rounded-lg cursor-pointer hover:bg-accent ${selectedTeamId === team.id ? 'bg-accent' : ''
                                } ${team.status === 'Inactive' ? 'opacity-75 bg-muted' : ''}`}
                            onClick={() => handleTeamClick(team)}
                        >
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="font-medium">{team.name}</h3>
                                    {team.objective && (
                                        <p className="text-sm text-muted-foreground">
                                            {team.objective}
                                        </p>
                                    )}
                                </div>
                                {isAdmin && (
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                className="h-8 w-8 p-0"
                                                onClick={(e) => e.stopPropagation()}
                                            >
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setEditingTeam(team);
                                                    setNewTeamName(team.name);
                                                    setNewTeamObjective(team.objective || '');
                                                    setShowNewTeamForm(true);
                                                }}
                                            >
                                                Edit
                                            </DropdownMenuItem>
                                            <DropdownMenuItem
                                                onClick={async (e) => {
                                                    e.stopPropagation();
                                                    if (isUpdatingStatus) return;
                                                    try {
                                                        setIsUpdatingStatus(true);
                                                        await updateTeam(team.id, {
                                                            status: team.status === 'Active' ? 'Inactive' : 'Active'
                                                        });
                                                        toast({
                                                            title: 'Team status updated',
                                                            description: `Team is now ${team.status === 'Active' ? 'Inactive' : 'Active'}.`
                                                        });
                                                    } catch (err) {
                                                        const error = err as Error;
                                                        toast({
                                                            title: 'Error',
                                                            description: error.message || 'Failed to update team status. Please try again.',
                                                            variant: 'destructive',
                                                        });
                                                    } finally {
                                                        setIsUpdatingStatus(false);
                                                    }
                                                }}
                                            >
                                                {team.status === 'Active' ? 'Make Inactive' : 'Make Active'}
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}; 