import { useState } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { useTeamManagerStore } from '../../store/teamManagerStore';
import { Team, ProfileRole } from '@/features/teams/types/team';
import { MoreHorizontal, UserPlus } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useAuthStore } from '@/features/auth/store/authStore';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface TeamMembersTableProps {
    team: Team | null;
    onAddMemberClick: () => void;
}

export const TeamMembersTable = ({ team, onAddMemberClick }: TeamMembersTableProps) => {
    const {
        members,
        loading,
        removeMember,
        updateProfile,
        currentUserRole,
    } = useTeamManagerStore();
    const { user } = useAuthStore();
    const { toast } = useToast();
    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
    const [pendingAction, setPendingAction] = useState<{ type: string; memberId: string; role?: ProfileRole } | null>(null);

    // Filter members for the selected team
    const teamMembers = members.filter(member => member.teamId === team?.id);

    // Check if current user is a member of the selected team
    const isCurrentUserTeamMember = team && user ? teamMembers.some(member =>
        member.userId === user.id
    ) : false;

    // Determine if the Add Team Member button should be shown
    const showAddMemberButton =
        currentUserRole === 'admin' ||
        (currentUserRole === 'manager' && isCurrentUserTeamMember);

    const handleRemoveMember = async (memberId: string) => {
        try {
            await removeMember(memberId);
            toast({
                title: "Member removed",
                description: "Team member has been removed successfully.",
            });
        } catch {
            toast({
                title: "Error",
                description: "Failed to remove team member.",
                variant: "destructive",
            });
        }
    };

    const handleUpdateProfile = async (userId: string, role: ProfileRole) => {
        try {
            await updateProfile(userId, { role });
            toast({
                title: "Role updated",
                description: `Member's role has been updated to ${role} successfully.`,
            });
        } catch (error) {
            console.error('Failed to update role:', error);
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : "Failed to update member's role.",
                variant: "destructive",
            });
        }
    };

    const getRoleActions = (memberRole: ProfileRole) => {
        // If current user is a regular user, show no actions
        if (currentUserRole === 'user') return [];

        // If current user is a manager, only show remove option
        if (currentUserRole === 'manager') {
            return [];  // Return empty array for all role changes
        }

        // For admin users, show all role change options
        switch (memberRole) {
            case 'admin':
                return [
                    { label: 'Make Manager', role: 'manager' as ProfileRole },
                    { label: 'Make User', role: 'user' as ProfileRole }
                ];
            case 'manager':
                return [
                    { label: 'Make Admin', role: 'admin' as ProfileRole },
                    { label: 'Make User', role: 'user' as ProfileRole }
                ];
            case 'user':
                return [
                    { label: 'Make Admin', role: 'admin' as ProfileRole },
                    { label: 'Make Manager', role: 'manager' as ProfileRole }
                ];
            default:
                return [];
        }
    };

    // Helper function to check if user can perform actions
    const canShowQuickActions = () => {
        return currentUserRole !== 'user';
    };

    // Helper function to check if user can remove members
    const canRemoveMember = (memberRole: ProfileRole) => {
        if (currentUserRole === 'admin') return true;
        if (currentUserRole === 'manager') {
            // Managers can't remove admins
            return memberRole !== 'admin';
        }
        return false;
    };

    const getRoleBadgeStyle = (role: ProfileRole) => {
        switch (role) {
            case 'admin':
                return 'bg-primary/10 text-primary';
            case 'manager':
                return 'bg-indigo-100 text-indigo-800';
            default:
                return 'bg-muted text-muted-foreground';
        }
    };

    if (!team) {
        return (
            <div className="flex items-center justify-center h-full text-muted-foreground">
                Select a team to view members
            </div>
        );
    }

    return (
        <div className="p-6 space-y-6">
            <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">{team.name} Members</h2>
                {showAddMemberButton && (
                    <Button
                        className="flex items-center gap-2"
                        onClick={onAddMemberClick}
                    >
                        <UserPlus className="h-4 w-4" />
                        Add Team Member
                    </Button>
                )}
            </div>

            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-[50px]"></TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {teamMembers.map((member) => (
                        <TableRow key={member.id}>
                            <TableCell>
                                <Avatar className="h-8 w-8">
                                    {member.avatarUrl ? (
                                        <AvatarImage src={member.avatarUrl} alt={member.name} />
                                    ) : (
                                        <AvatarFallback>
                                            {member.name.charAt(0).toUpperCase()}
                                        </AvatarFallback>
                                    )}
                                </Avatar>
                            </TableCell>
                            <TableCell>
                                <div className="flex items-center space-x-2">
                                    <span>{member.name}</span>
                                    <Badge variant="outline" className={getRoleBadgeStyle(member.profileRole)}>
                                        {member.profileRole}
                                    </Badge>
                                </div>
                            </TableCell>
                            <TableCell>{member.email}</TableCell>
                            <TableCell>
                                {canShowQuickActions() && (
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            {getRoleActions(member.profileRole).map(action => (
                                                <DropdownMenuItem
                                                    key={action.label}
                                                    onClick={() => {
                                                        setPendingAction({
                                                            type: 'updateRole',
                                                            memberId: member.userId,
                                                            role: action.role
                                                        });
                                                        setIsConfirmDialogOpen(true);
                                                    }}
                                                    disabled={loading}
                                                >
                                                    {action.label}
                                                </DropdownMenuItem>
                                            ))}
                                            {canRemoveMember(member.profileRole) && (
                                                <DropdownMenuItem
                                                    className="text-red-600"
                                                    onClick={() => {
                                                        setPendingAction({
                                                            type: 'remove',
                                                            memberId: member.id
                                                        });
                                                        setIsConfirmDialogOpen(true);
                                                    }}
                                                    disabled={loading}
                                                >
                                                    Remove
                                                </DropdownMenuItem>
                                            )}
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                )}
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>

            <AlertDialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>
                            {pendingAction?.type === 'remove' ? 'Remove Member' : 'Update Role'}
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                            {pendingAction?.type === 'remove'
                                ? 'Are you sure you want to remove this member from the team?'
                                : 'Are you sure you want to update this member\'s role?'}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel onClick={() => setPendingAction(null)}>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={async () => {
                                if (!pendingAction) return;

                                if (pendingAction.type === 'remove') {
                                    await handleRemoveMember(pendingAction.memberId);
                                } else if (pendingAction.type === 'updateRole' && pendingAction.role) {
                                    await handleUpdateProfile(pendingAction.memberId, pendingAction.role);
                                }

                                setIsConfirmDialogOpen(false);
                                setPendingAction(null);
                            }}
                        >
                            Confirm
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    );
}; 