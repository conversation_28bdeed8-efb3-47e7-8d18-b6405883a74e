import { useEffect, useState, useCallback, useMemo } from 'react';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTeamManagerStore } from '../../store/teamManagerStore';
import { useAuthStore } from '@/features/auth/store/authStore';
import { Team } from '@/features/teams/types/team';
import { Search } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface AddTeamMembersDialogProps {
    team: Team | null;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

export function AddTeamMembersDialog({ team, open, onOpenChange }: AddTeamMembersDialogProps) {
    const { availableUsers = [], fetchAvailableUsers, addTeamMembers } = useTeamManagerStore();
    const { user } = useAuthStore();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
    const [isOpen, setIsOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const { toast } = useToast();

    // Handle external open state changes
    useEffect(() => {
        setIsOpen(open);
        if (!open) {
            // Reset state when dialog closes
            setSelectedUsers([]);
            setSearchQuery('');
        }
    }, [open]);

    // Handle internal open state changes
    const handleOpenChange = useCallback((newOpen: boolean) => {
        setIsOpen(newOpen);
        onOpenChange(newOpen);
    }, [onOpenChange]);

    // Fetch available users when dialog opens
    useEffect(() => {
        if (isOpen && team?.id) {
            const fetchUsers = async () => {
                setIsLoading(true);
                try {
                    await fetchAvailableUsers(team.id);
                } catch (error) {
                    console.error('Error fetching available users:', error);
                    toast({
                        title: 'Error',
                        description: 'Failed to fetch available users',
                        variant: 'destructive',
                    });
                } finally {
                    setIsLoading(false);
                }
            };

            fetchUsers();
            setSelectedUsers([]);
            setSearchQuery('');
        }
    }, [isOpen, team?.id, fetchAvailableUsers, toast]);

    const filteredUsers = useMemo(() => {
        if (!availableUsers || !Array.isArray(availableUsers)) return [];

        return availableUsers.filter(user => {
            const searchLower = searchQuery.toLowerCase();
            return (
                (user.name || '').toLowerCase().includes(searchLower) ||
                (user.email || '').toLowerCase().includes(searchLower)
            );
        });
    }, [availableUsers, searchQuery]);

    const handleUserToggle = (userId: string) => {
        setSelectedUsers(prev =>
            prev.includes(userId)
                ? prev.filter(id => id !== userId)
                : [...prev, userId]
        );
    };

    const handleSubmit = async () => {
        if (selectedUsers.length === 0) return;
        if (!team?.id) {
            toast({
                title: 'Error',
                description: 'No team selected. Please select a team first.',
                variant: 'destructive',
            });
            return;
        }

        setIsLoading(true);
        try {
            await addTeamMembers(team.id, selectedUsers);

            toast({
                title: 'Team members added successfully',
                description: `Added ${selectedUsers.length} members to the team.`,
            });
            handleOpenChange(false);
        } catch (err) {
            console.error('Failed to add team members:', err);
            toast({
                title: 'Failed to add team members',
                description: err instanceof Error ? err.message : 'An error occurred while adding team members.',
                variant: 'destructive',
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleOpenChange}>
            <DialogContent
                className="sm:max-w-[500px]"
                onPointerDownOutside={(e) => e.preventDefault()}
                onEscapeKeyDown={(e) => e.preventDefault()}
            >
                <DialogHeader>
                    <DialogTitle>Add Team Members</DialogTitle>
                    <DialogDescription className="space-y-2">
                        <p>Adding members to <span className="font-medium">{team?.name}</span></p>
                        <p className="text-xs text-muted-foreground">
                            You ({user?.email}) can select multiple users to add to the team.
                        </p>
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4 py-4">
                    <div className="relative">
                        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                            placeholder="Search users..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-8"
                        />
                    </div>

                    <ScrollArea className="h-[300px] pr-4">
                        <div className="space-y-4">
                            {filteredUsers.map((user) => (
                                <div
                                    key={user.id}
                                    className="flex items-center space-x-4 rounded-lg border p-4"
                                >
                                    <Checkbox
                                        id={user.id}
                                        checked={selectedUsers.includes(user.id)}
                                        onCheckedChange={() => handleUserToggle(user.id)}
                                    />
                                    <div className="flex-1 space-y-1">
                                        <label
                                            htmlFor={user.id}
                                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                        >
                                            {user.name}
                                        </label>
                                        <p className="text-sm text-muted-foreground">
                                            {user.email}
                                        </p>
                                    </div>
                                </div>
                            ))}
                            {filteredUsers.length === 0 && (
                                <div className="text-center text-muted-foreground py-4">
                                    No users found
                                </div>
                            )}
                        </div>
                    </ScrollArea>
                </div>

                <DialogFooter>
                    <Button
                        variant="outline"
                        onClick={() => handleOpenChange(false)}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        disabled={selectedUsers.length === 0 || isLoading}
                    >
                        {isLoading ? 'Adding...' : 'Add Selected Users'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
} 