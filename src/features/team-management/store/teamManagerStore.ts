import { create } from 'zustand';
import { Team, TeamMember, ProjectStatus, ProfileRole } from '@/features/teams/types/team';
import { supabase } from '@/lib/supabase';

interface CreateTeamInput {
    name: string;
    objective?: string;
    status?: ProjectStatus;
    progress?: number;
}

interface UpdateTeamInput {
    name?: string;
    objective?: string;
    status?: ProjectStatus;
    progress?: number;
}

interface UpdateProfileInput {
    role: ProfileRole;
}

interface TeamManagerState {
    teams: Team[];
    members: TeamMember[];
    loading: boolean;
    error: string | null;
    currentUserRole: ProfileRole;
    availableUsers: Array<{ id: string; name: string; email: string; }>;

    createTeam: (team: CreateTeamInput) => Promise<void>;
    updateTeam: (id: string, team: UpdateTeamInput) => Promise<void>;
    deleteTeam: (id: string) => Promise<void>;
    fetchTeams: () => Promise<void>;
    fetchMembers: () => Promise<void>;
    updateProfile: (userId: string, profile: UpdateProfileInput) => Promise<void>;
    removeMember: (id: string) => Promise<void>;
    fetchAvailableUsers: (teamId: string) => Promise<void>;
    addTeamMembers: (teamId: string, userIds: string[]) => Promise<void>;
}

export const useTeamManagerStore = create<TeamManagerState>((set, get) => ({
    teams: [],
    members: [],
    loading: false,
    error: null,
    currentUserRole: 'user',
    availableUsers: [],

    fetchTeams: async () => {
        set({ loading: true, error: null });
        try {
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError) throw sessionError;
            if (!session) throw new Error('No active session');

            // Get user role
            const { data: userProfile, error: profileError } = await supabase
                .from('profiles')
                .select('role')
                .eq('id', session.user.id)
                .single();

            if (profileError) throw profileError;
            const userRole = (userProfile?.role || 'user') as ProfileRole;
            set({ currentUserRole: userRole });

            let teamsQuery;

            if (userRole === 'admin') {
                // Admins can see all teams
                teamsQuery = await supabase
                    .from('teams')
                    .select(`
                        *,
                        profiles:created_by(
                            id,
                            full_name,
                            avatar_url,
                            role
                        )
                    `)
                    .order('created_at', { ascending: false });
            } else {
                // Users and managers only see teams they are part of
                const { data: memberTeams } = await supabase
                    .from('team_members')
                    .select('team_id')
                    .eq('user_id', session.user.id);

                const teamIds = memberTeams?.map(t => t.team_id) || [];

                teamsQuery = await supabase
                    .from('teams')
                    .select(`
                        *,
                        profiles:created_by(
                            id,
                            full_name,
                            avatar_url,
                            role
                        )
                    `)
                    .in('id', teamIds)
                    .order('created_at', { ascending: false });
            }

            if (teamsQuery.error) throw teamsQuery.error;
            const teams = teamsQuery.data;

            // Transform the data to match the Team interface
            const transformedTeams = teams?.map(team => ({
                id: team.id,
                name: team.name || '',
                objective: team.objective,
                avatarUrl: team.avatar_url,
                createdAt: team.created_at,
                updatedAt: team.updated_at,
                createdBy: team.created_by,
                status: team.status as ProjectStatus,
                progress: team.progress || 0,
                endDate: team.end_date,
            })) || [];

            set({ teams: transformedTeams, loading: false });
        } catch (err) {
            const error = err as Error;
            set({ error: error.message || 'Failed to fetch teams', loading: false });
        }
    },

    fetchMembers: async () => {
        set({ loading: true, error: null });
        try {
            const { data: profiles, error: profilesError } = await supabase
                .from('profiles')
                .select('*');

            if (profilesError) throw profilesError;

            const { data: members, error: fetchError } = await supabase
                .from('team_members')
                .select('*');

            if (fetchError) throw fetchError;

            // Transform the data to match the TeamMember interface
            const transformedMembers = members?.map(member => {
                const profile = profiles?.find(p => p.id === member.user_id);
                return {
                    id: member.id,
                    teamId: member.team_id,
                    userId: member.user_id,
                    name: profile?.full_name || 'Unknown',
                    email: profile?.email || '',
                    profileRole: profile?.role as ProfileRole || 'user',
                    joinedAt: member.joined_at,
                    avatarUrl: profile?.avatar_url || null
                };
            }) || [];

            set({ members: transformedMembers, loading: false });
        } catch (err) {
            const error = err as Error;
            set({ error: error.message || 'Failed to fetch members', loading: false });
        }
    },

    updateProfile: async (userId: string, profile: UpdateProfileInput) => {
        set({ loading: true, error: null });
        try {
            // First verify if the current user has permission to update roles
            const { data: { session }, error: sessionError } = await supabase.auth.getSession();
            if (sessionError) throw sessionError;
            if (!session) throw new Error('No active session');

            const { data: currentUser, error: profileError } = await supabase
                .from('profiles')
                .select('role')
                .eq('id', session.user.id)
                .single();

            if (profileError) throw profileError;
            if (currentUser?.role !== 'admin') {
                throw new Error('Only admins can update user roles');
            }

            // Update the user's role
            const { error: updateError } = await supabase
                .from('profiles')
                .update({ role: profile.role })
                .eq('id', userId);

            if (updateError) throw updateError;

            // Refresh the members list to reflect the role change
            await get().fetchMembers();
            set({ loading: false });
        } catch (err) {
            const error = err as Error;
            set({ error: error.message || 'Failed to update profile', loading: false });
            throw error; // Re-throw the error so we can handle it in the component
        }
    },

    removeMember: async (id: string) => {
        set({ loading: true, error: null });
        try {
            const { error: removeError } = await supabase
                .from('team_members')
                .delete()
                .eq('id', id);

            if (removeError) throw removeError;

            // Refresh the members list
            await get().fetchMembers();
            set({ loading: false });
        } catch (err) {
            const error = err as Error;
            set({ error: error.message || 'Failed to remove member', loading: false });
        }
    },

    createTeam: async (team: CreateTeamInput) => {
        set({ loading: true, error: null });
        try {
            const { error: createError } = await supabase
                .from('teams')
                .insert([team])
                .select()
                .single();

            if (createError) throw createError;

            // Refresh teams list
            await get().fetchTeams();
            set({ loading: false });
        } catch (err) {
            const error = err as Error;
            set({ error: error.message || 'Failed to create team', loading: false });
        }
    },

    updateTeam: async (id: string, team: UpdateTeamInput) => {
        set({ loading: true, error: null });
        try {
            const { error: updateError } = await supabase
                .from('teams')
                .update(team)
                .eq('id', id);

            if (updateError) throw updateError;

            // Refresh teams list
            await get().fetchTeams();
            set({ loading: false });
        } catch (err) {
            const error = err as Error;
            set({ error: error.message || 'Failed to update team', loading: false });
        }
    },

    deleteTeam: async (id: string) => {
        set({ loading: true, error: null });
        try {
            const { error: deleteError } = await supabase
                .from('teams')
                .delete()
                .eq('id', id);

            if (deleteError) throw deleteError;

            // Refresh teams list
            await get().fetchTeams();
            set({ loading: false });
        } catch (err) {
            const error = err as Error;
            set({ error: error.message || 'Failed to delete team', loading: false });
        }
    },

    fetchAvailableUsers: async (teamId: string) => {
        try {
            // First, get existing team members
            const { data: existingMembers, error: membersError } = await supabase
                .from('team_members')
                .select('user_id')
                .eq('team_id', teamId);

            if (membersError) throw membersError;

            // Get the list of existing member IDs
            const existingMemberIds = new Set(existingMembers?.map(m => m.user_id) || []);

            // Fetch all users except those already in the team
            const { data: users, error: usersError } = await supabase
                .from('profiles')
                .select('id, email, full_name')
                .not('id', 'in', `(${Array.from(existingMemberIds).join(',')})`);

            if (usersError) throw usersError;

            // Transform the data to include name
            const transformedUsers = users?.map(user => ({
                id: user.id,
                email: user.email || '',
                name: user.full_name || user.email?.split('@')[0] || 'Unknown User'
            })) || [];

            set({ availableUsers: transformedUsers });
        } catch (error) {
            console.error('Error fetching available users:', error);
            throw error;
        }
    },

    addTeamMembers: async (teamId: string, userIds: string[]) => {
        try {
            // First check for existing members to avoid duplicates
            const { data: existingMembers, error: checkError } = await supabase
                .from('team_members')
                .select('user_id')
                .eq('team_id', teamId)
                .in('user_id', userIds);

            if (checkError) throw checkError;

            // Filter out users that are already members
            const existingUserIds = new Set(existingMembers?.map(member => member.user_id) || []);
            const newUserIds = userIds.filter(userId => !existingUserIds.has(userId));

            if (newUserIds.length === 0) {
                throw new Error('All selected users are already team members.');
            }

            // Create the team members data
            const teamMembersData = newUserIds.map(userId => ({
                team_id: teamId,
                user_id: userId,
                joined_at: new Date().toISOString()
            }));

            // Insert only new members
            const { error } = await supabase
                .from('team_members')
                .insert(teamMembersData);

            if (error) throw error;

            // Update the store state if needed
            const { availableUsers } = get();
            set({
                availableUsers: availableUsers.filter(user => !newUserIds.includes(user.id))
            });

            // Refresh the members list to show new members
            await get().fetchMembers();
        } catch (error) {
            console.error('Error adding team members:', error);
            throw error;
        }
    }
})); 