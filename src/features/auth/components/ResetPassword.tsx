import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Loader2 } from 'lucide-react';
import { supabase } from '@/lib/supabase';

const formSchema = z.object({
    password: z.string()
        .min(6, 'Password must be at least 6 characters')
        .max(72, 'Password must be less than 72 characters'),
    confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});

type FormData = z.infer<typeof formSchema>;

export const ResetPassword = () => {
    const { toast } = useToast();
    const [loading, setLoading] = React.useState(false);
    const [error, setError] = React.useState<string | null>(null);
    const [isVerifying, setIsVerifying] = React.useState(true);

    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            password: '',
            confirmPassword: '',
        },
    });

    useEffect(() => {
        const verifyRecoveryToken = async () => {
            try {
                // Get the recovery token from the URL hash
                const hash = window.location.hash;
                if (!hash) {
                    throw new Error('Invalid or expired recovery link. Please request a new password reset link.');
                }

                // Extract the recovery token
                const hashParams = new URLSearchParams(hash.substring(1));
                const type = hashParams.get('type');
                const accessToken = hashParams.get('access_token');

                if (!accessToken || type !== 'recovery') {
                    throw new Error('Invalid or expired recovery link. Please request a new password reset link.');
                }

                // Just mark verification as complete
                setIsVerifying(false);
            } catch (error) {
                console.error('Recovery verification error:', error);
                setError(error instanceof Error ? error.message : 'Invalid or expired recovery link');
                setTimeout(() => {
                    window.location.href = '/';
                }, 3000);
            }
        };

        verifyRecoveryToken();
    }, []);

    const onSubmit = async (data: FormData) => {
        setError(null);
        setLoading(true);

        try {
            // Get the recovery token from the URL hash
            const hashParams = new URLSearchParams(window.location.hash.substring(1));
            const accessToken = hashParams.get('access_token');

            if (!accessToken) {
                throw new Error('Recovery session expired. Please request a new password reset link.');
            }

            // First update the password using the recovery token
            const { error: updateError } = await supabase.auth.updateUser({
                password: data.password
            });

            if (updateError) throw updateError;

            toast({
                title: 'Password Updated',
                description: 'Your password has been successfully updated. Please log in with your new password.',
            });

            // Redirect to home page which will show the login form
            window.location.href = '/';
        } catch (error) {
            console.error('Reset password error:', error);
            setError(error instanceof Error ? error.message : 'Failed to reset password. Please try again.');
            form.reset();
        } finally {
            setLoading(false);
        }
    };

    if (isVerifying) {
        return (
            <div className="container max-w-[400px] mx-auto pt-8">
                <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground">Verifying recovery link...</p>
                </div>
            </div>
        );
    }

    if (error && !form.formState.isDirty) {
        return (
            <div className="container max-w-[400px] mx-auto pt-8">
                <Alert variant="destructive" className="mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
                <p className="text-sm text-muted-foreground text-center">
                    Redirecting to home page...
                </p>
            </div>
        );
    }

    return (
        <div className="container max-w-[400px] mx-auto pt-8">
            <div className="text-center mb-8">
                <h1 className="text-2xl font-bold text-primary mb-2">Reset Password</h1>
                <p className="text-sm text-muted-foreground">Enter your new password below</p>
            </div>

            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                        control={form.control}
                        name="password"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>New Password</FormLabel>
                                <FormControl>
                                    <Input
                                        type="password"
                                        placeholder="Enter new password"
                                        disabled={loading}
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="confirmPassword"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Confirm Password</FormLabel>
                                <FormControl>
                                    <Input
                                        type="password"
                                        placeholder="Confirm new password"
                                        disabled={loading}
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <Button type="submit" className="w-full relative" disabled={loading}>
                        {loading ? (
                            <>
                                <Loader2 className="h-4 w-4 animate-spin absolute left-1/2 -translate-x-1/2" />
                                <span className="opacity-0">Reset Password</span>
                            </>
                        ) : (
                            'Reset Password'
                        )}
                    </Button>

                    {error && form.formState.isDirty && (
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}
                </form>
            </Form>
        </div>
    );
}; 