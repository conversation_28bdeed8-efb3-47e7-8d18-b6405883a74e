import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { Loader2 } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import { useUser } from '@supabase/auth-helpers-react';

// Helper function to wait
const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const AuthCallback = () => {
    const [searchParams] = useSearchParams();
    const [error, setError] = useState<string | null>(null);
    const { fetchUserProfile } = useAuthStore();
    const user = useUser();

    useEffect(() => {
        async function handleAuthCallback() {
            try {
                // Give the auth system a moment to initialize
                await wait(100);

                // Check if user is already authenticated via useUser hook
                if (user) {
                    console.log('User already authenticated via useUser hook, redirecting');
                    const returnUrl = sessionStorage.getItem('returnUrl') || '/teams';
                    sessionStorage.removeItem('returnUrl');
                    window.location.href = returnUrl;
                    return;
                }

                // Process the callback - exchange the auth code for a session
                const code = searchParams.get('code');

                if (!code) {
                    // No code found but we're on the callback page - this could be a refresh
                    // Check session with retries, as it might take a moment to initialize
                    let sessionData = null;
                    for (let attempt = 0; attempt < 3; attempt++) {
                        const { data } = await supabase.auth.getSession();
                        if (data?.session?.user) {
                            sessionData = data;
                            break;
                        }
                        // Wait a bit before next attempt
                        await wait(300);
                    }

                    // If we found a session after retries
                    if (sessionData?.session?.user) {
                        console.log('Session found after retry, redirecting');
                        await fetchUserProfile();
                        const returnUrl = sessionStorage.getItem('returnUrl') || '/teams';
                        sessionStorage.removeItem('returnUrl');
                        window.location.href = returnUrl;
                        return;
                    }

                    // Truly no code and no session - this is an error
                    throw new Error('No authentication code found. Please try signing in again.');
                }

                // Exchange auth code for session
                const { error } = await supabase.auth.exchangeCodeForSession(code);

                if (error) {
                    throw error;
                }

                // Session is now established - get current user data
                const { data } = await supabase.auth.getSession();

                if (data?.session?.user) {
                    // Fetch user profile after authentication
                    await fetchUserProfile();
                }

                // Check if there's a saved return URL
                const returnUrl = sessionStorage.getItem('returnUrl') || '/teams';
                sessionStorage.removeItem('returnUrl'); // Clear the stored URL

                // Redirect to the saved URL or default route
                window.location.href = returnUrl;

            } catch (err) {
                console.error('Auth callback error:', err);
                setError(err instanceof Error ? err.message : 'Authentication failed. Please try again.');
            }
        }

        handleAuthCallback();
    }, [searchParams, fetchUserProfile, user]);

    // If there was an error, show an error message and provide a button to go back to login
    if (error) {
        return (
            <div className="flex flex-col items-center justify-center h-screen w-screen p-4">
                <div className="max-w-md text-center">
                    <h2 className="text-2xl font-bold text-red-600 mb-4">Authentication Error</h2>
                    <p className="mb-6 text-gray-700">{error}</p>
                    <button
                        onClick={() => window.location.href = '/'}
                        className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
                    >
                        Return to Login
                    </button>
                </div>
            </div>
        );
    }

    // Show loading state while processing
    return (
        <div className="flex flex-col items-center justify-center h-screen w-screen">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-gray-600">Completing authentication...</p>
        </div>
    );
}; 