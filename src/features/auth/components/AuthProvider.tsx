import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { AuthModal } from './AuthModal';
import { Loader2 } from 'lucide-react';
import { useUser } from '@supabase/auth-helpers-react';

interface AuthProviderProps {
    children: React.ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
    const { isInitializing, checkAuth } = useAuthStore();
    const user = useUser();
    const location = useLocation();

    // Check if current route is a public auth route that doesn't require authentication
    const isAuthCallbackRoute = location.pathname === '/auth/callback';
    const isResetPasswordRoute = location.pathname === '/auth/reset-password';
    const isPublicAuthRoute = isAuthCallbackRoute || isResetPasswordRoute;

    useEffect(() => {
        checkAuth();
    }, [checkAuth]);

    if (isInitializing) {
        return (
            <div className="h-screen w-screen flex items-center justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
        );
    }

    // Allow access to public auth routes without authentication
    if (isPublicAuthRoute) {
        return <>{children}</>;
    }

    if (!user && !isInitializing) {
        // Save the attempted URL for redirection after login
        // Only save non-auth routes as return URLs
        const returnUrl = location.pathname + location.search;
        sessionStorage.setItem('returnUrl', returnUrl);

        return <AuthModal />;
    }

    return <>{children}</>;
}; 