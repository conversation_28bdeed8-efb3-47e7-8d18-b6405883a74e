import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import { useAuthStore } from '../store/authStore';
import { Loader2 } from 'lucide-react';
import { ControllerRenderProps } from 'react-hook-form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Mail } from 'lucide-react';

const formSchema = z.object({
    email: z.string()
        .email('Invalid email address')
        .refine(email => email.endsWith('@marketfeed.com'), {
            message: 'Only marketfeed.com email addresses are allowed'
        }),
});

type FormData = z.infer<typeof formSchema>;

export const LoginForm = () => {
    const { signInWithMagicLink, loading } = useAuthStore();
    const { toast } = useToast();
    const [authError, setAuthError] = React.useState<string | null>(null);
    const [magicLinkSent, setMagicLinkSent] = React.useState(false);

    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: '',
        },
    });

    const onSubmit = async (data: FormData) => {
        setAuthError(null);
        try {
            await signInWithMagicLink(data.email);
            setMagicLinkSent(true);
            toast({
                title: 'Magic Link Sent',
                description: 'Please check your email for the login link.',
            });
        } catch (error) {
            console.error('Login error:', error);
            let errorMessage = 'Failed to send magic link. Please try again.';

            if (error instanceof Error) {
                errorMessage = error.message;
            }

            setAuthError(errorMessage);
        }
    };

    if (magicLinkSent) {
        return (
            <div className="space-y-4 text-center">
                <Mail className="mx-auto h-12 w-12 text-primary" />
                <h2 className="text-2xl font-semibold">Check your email</h2>
                <p className="text-muted-foreground">
                    We've sent a magic link to your email address.
                    Click the link to sign in to your account.
                </p>
            </div>
        );
    }

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 mt-4">
                <FormField
                    control={form.control}
                    name="email"
                    render={({ field }: { field: ControllerRenderProps<FormData, 'email'> }) => (
                        <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                                <Input
                                    type="email"
                                    placeholder="Enter your @marketfeed.com email"
                                    disabled={loading}
                                    {...field}
                                />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <Button type="submit" className="w-full relative" disabled={loading}>
                    {loading ? (
                        <>
                            <Loader2 className="h-4 w-4 animate-spin absolute left-1/2 -translate-x-1/2" />
                            <span className="opacity-0">Send Magic Link</span>
                        </>
                    ) : (
                        'Send Magic Link'
                    )}
                </Button>

                {authError && (
                    <Alert variant="destructive" className="mt-4">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{authError}</AlertDescription>
                    </Alert>
                )}
            </form>
        </Form>
    );
}; 