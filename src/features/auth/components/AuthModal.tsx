import { useState } from 'react';
import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogDescription,
} from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { LoginForm } from './LoginForm';
import { SignupForm } from './SignupForm';

interface AuthModalProps {
    defaultTab?: 'login' | 'signup';
}

export const AuthModal = ({ defaultTab = 'login' }: AuthModalProps) => {
    const [activeTab, setActiveTab] = useState<'login' | 'signup'>(defaultTab);

    return (
        <Dialog open={true} onOpenChange={() => { }}>
            <DialogContent className="sm:max-w-[425px]">
                <div className="text-center mb-6">
                    <h1 className="text-2xl font-bold text-primary mb-2">cadence.ai</h1>
                    <p className="text-sm text-muted-foreground">Your OKR cadence powered by AI.</p>
                </div>
                <DialogTitle className="text-center">
                    {activeTab === 'login' ? 'Welcome Back' : 'Create an Account'}
                </DialogTitle>
                <DialogDescription className="text-center">
                    {activeTab === 'login'
                        ? 'Sign in to your account to continue'
                        : 'Sign up for a new account to get started'}
                </DialogDescription>
                <Tabs defaultValue={activeTab} className="w-full" onValueChange={(value: string) => setActiveTab(value as 'login' | 'signup')}>
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="login">Login</TabsTrigger>
                        <TabsTrigger value="signup">Sign Up</TabsTrigger>
                    </TabsList>
                    <TabsContent value="login">
                        <LoginForm />
                    </TabsContent>
                    <TabsContent value="signup">
                        <SignupForm />
                    </TabsContent>
                </Tabs>
            </DialogContent>
        </Dialog>
    );
}; 