import { create } from 'zustand';
import { supabase } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';
import { persist } from 'zustand/middleware';

interface UserProfile {
    id: string;
    full_name: string | null;
    email: string;
    avatar_url: string | null;
}

interface AuthState {
    user: User | null;
    userProfile: UserProfile | null;
    loading: boolean;
    isInitializing: boolean;
    error: Error | null;
    signUp: (email: string, password: string, name: string) => Promise<void>;
    signUpWithMagicLink: (email: string, name: string) => Promise<void>;
    signIn: (email: string, password: string) => Promise<void>;
    signInWithMagicLink: (email: string) => Promise<void>;
    signOut: () => Promise<void>;
    checkAuth: () => Promise<void>;
    resetPassword: (email: string) => Promise<void>;
    setError: (error: Error | null) => void;
    fetchUserProfile: () => Promise<void>;
    updateProfilePicture: (url: string) => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
    persist(
        (set, get) => ({
            user: null,
            userProfile: null,
            loading: false,
            isInitializing: true,
            error: null,

            fetchUserProfile: async () => {
                const { user } = get();
                if (!user) return;

                try {
                    const { data: profile, error } = await supabase
                        .from('profiles')
                        .select('id, full_name, email, avatar_url')
                        .eq('id', user.id)
                        .single();

                    if (error) return;
                    
                    if (profile) {
                        // Ensure the avatar URL is correct by creating a signed URL for authenticated access
                        if (profile.avatar_url && profile.avatar_url.includes('profile-pictures')) {
                            try {
                                const filePath = profile.avatar_url.split('profile-pictures/')[1];
                                if (filePath) {
                                    const { data } = await supabase.storage
                                        .from('profile-pictures')
                                        .createSignedUrl(filePath, 60 * 60 * 24); // 24 hour expiry
                                    
                                    if (data) {
                                        profile.avatar_url = data.signedUrl;
                                    }
                                }
                            } catch (_e) {
                                // Fallback to original URL if signed URL creation fails
                            }
                        }
                        
                        set({ userProfile: profile });
                    }
                } catch (_error) {
                    // Error handled silently as this is a background refresh
                }
            },

            signUp: async (email: string, password: string, name: string) => {
                try {
                    set({ loading: true, error: null });
                    const { data, error } = await supabase.auth.signUp({
                        email,
                        password,
                        options: {
                            data: {
                                full_name: name,
                            },
                        },
                    });

                    if (error) throw error;

                    if (data.user) {
                        const { error: profileError } = await supabase
                            .from('profiles')
                            .insert([
                                {
                                    id: data.user.id,
                                    full_name: name,
                                    email,
                                    role: 'user',
                                    avatar_url: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}`,
                                },
                            ]);

                        if (profileError) throw profileError;

                        set({
                            user: data.user,
                            userProfile: {
                                id: data.user.id,
                                full_name: name,
                                email,
                                avatar_url: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}`,
                            }
                        });
                    }
                } catch (error) {
                    set({ error: error as Error });
                    throw error;
                } finally {
                    set({ loading: false });
                }
            },

            signUpWithMagicLink: async (email: string, name: string) => {
                try {
                    set({ loading: true, error: null });

                    const { error: signInError } = await supabase.auth.signInWithPassword({
                        email,
                        password: 'dummy-password-for-check'
                    });

                    if (!signInError || signInError.status !== 400) {
                        throw new Error('An account with this email already exists. Please try logging in instead.');
                    }

                    const { data: existingProfile } = await supabase
                        .from('profiles')
                        .select('id')
                        .eq('email', email)
                        .single();

                    if (existingProfile) {
                        throw new Error('An account with this email already exists. Please try logging in instead.');
                    }

                    const { error } = await supabase.auth.signInWithOtp({
                        email,
                        options: {
                            data: {
                                full_name: name,
                            },
                            emailRedirectTo: `${window.location.origin}/auth/callback`,
                        },
                    });

                    if (error) throw error;

                } catch (error) {
                    set({ error: error as Error });
                    throw error;
                } finally {
                    set({ loading: false });
                }
            },

            signIn: async (email: string, password: string) => {
                try {
                    set({ loading: true, error: null });

                    const { data, error } = await supabase.auth.signInWithPassword({
                        email,
                        password,
                    });

                    if (error) throw error;

                    if (data.user) {
                        set({ user: data.user });
                        await get().fetchUserProfile();
                    }

                    window.location.href = '/teams';
                } catch (error) {
                    set({ error: error as Error });
                    throw error;
                } finally {
                    set({ loading: false });
                }
            },

            signInWithMagicLink: async (email: string) => {
                try {
                    set({ loading: true, error: null });

                    const { error } = await supabase.auth.signInWithOtp({
                        email,
                        options: {
                            emailRedirectTo: `${window.location.origin}/auth/callback`,
                        },
                    });
                    if (error) throw error;
                } catch (error) {
                    set({ error: error as Error });
                    throw error;
                } finally {
                    set({ loading: false });
                }
            },

            signOut: async () => {
                try {
                    set({ loading: true, error: null });
                    const { error } = await supabase.auth.signOut();
                    if (error) throw error;
                    set({ user: null, userProfile: null });
                } catch (error) {
                    set({ error: error as Error });
                    throw error;
                } finally {
                    set({ loading: false });
                }
            },

            checkAuth: async () => {
                try {
                    set({ isInitializing: true, error: null });
                    const { data: { session }, error } = await supabase.auth.getSession();
                    if (error) throw error;

                    if (session?.user) {
                        set({ user: session.user });
                        await get().fetchUserProfile();
                    } else {
                        set({ user: null, userProfile: null });
                    }
                } catch (error) {
                    set({ error: error instanceof Error ? error : null });
                    throw error;
                } finally {
                    set({ isInitializing: false });
                }
            },

            resetPassword: async (email: string) => {
                try {
                    set({ loading: true, error: null });
                    const { error } = await supabase.auth.resetPasswordForEmail(email, {
                        redirectTo: `${window.location.origin}/auth/reset-password`,
                    });
                    if (error) throw error;
                } catch (error) {
                    set({ error: error as Error });
                    throw error;
                } finally {
                    set({ loading: false });
                }
            },

            setError: (error) => set({ error }),
            updateProfilePicture: async (url: string) => {
                try {
                    set({ loading: true, error: null });
                    const { user } = get();
                    if (!user) return;

                    const { error } = await supabase
                        .from('profiles')
                        .update({ avatar_url: url })
                        .eq('id', user.id);

                    if (error) throw error;

                    set(state => ({
                        userProfile: state.userProfile ? {
                            ...state.userProfile,
                            avatar_url: url
                        } : null
                    }));
                } catch (error) {
                    set({ error: error as Error });
                    throw error;
                } finally {
                    set({ loading: false });
                }
            },
        }),
        {
            name: 'auth-storage',
            partialize: (state) => ({
                user: state.user,
                userProfile: state.userProfile
            })
        }
    )
);