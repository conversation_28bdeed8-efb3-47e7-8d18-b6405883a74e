import { TeamKanbanCard, TeamKanbanSubtask } from "../types";
import { useTeamKanbanStore } from "../store/teamKanbanStore";
import { logger } from "@/utils/logger";

/**
 * Generates a system comment for card creation
 */
export const commentOnCardCreation = async (card: TeamKanbanCard): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    await anyStore.addComment(
      card.id, 
      `📝 A new task "${card.title}" was created.`,
      true // isSystem flag
    );
  } catch (error) {
    logger.error("Failed to add comment on card creation", { error });
  }
};

/**
 * Generates a system comment for card title updates
 */
export const commentOnCardTitleUpdate = async (
  card: TeamKanbanCard, 
  oldTitle: string
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    await anyStore.addComment(
      card.id,
      `✏️ Task title was updated from "${oldTitle}" to "${card.title}".`,
      true // isSystem flag
    );
  } catch (error) {
    logger.error("Failed to add comment on title update", { error });
  }
};

/**
 * Generates a system comment for card description updates
 */
export const commentOnCardDescriptionUpdate = async (
  card: TeamKanbanCard,
  oldDescription: string | null,
  newDescription: string | null
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    
    if (!oldDescription && newDescription) {
      await anyStore.addComment(
        card.id,
        `📄 Task description was added.`,
        true
      );
    } else if (oldDescription && !newDescription) {
      await anyStore.addComment(
        card.id,
        `📄 Task description was removed.`,
        true
      );
    } else {
      await anyStore.addComment(
        card.id,
        `📄 Task description was updated from "${oldDescription}" to "${newDescription}".`,
        true
      );
    }
  } catch (error) {
    logger.error("Failed to add comment on description update", { error });
  }
};

/**
 * Generates a system comment for card due date updates
 */
export const commentOnCardDueDateUpdate = async (
  card: TeamKanbanCard,
  oldDueDate: string | null
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    
    if (!oldDueDate && card.due_date) {
      await anyStore.addComment(
        card.id,
        `📅 Taks due date was set to ${new Date(card.due_date).toLocaleDateString('en-GB')}.`,
        true
      );
    } else if (oldDueDate && !card.due_date) {
      await anyStore.addComment(
        card.id,
        `📅 Taks due date was removed.`,
        true
      );
    } else if (oldDueDate && card.due_date) {
      await anyStore.addComment(
        card.id,
        `📅 Taks due date was updated from ${new Date(oldDueDate).toLocaleDateString('en-GB')} to ${new Date(card.due_date).toLocaleDateString('en-GB')}.`,
        true
      );
    }
  } catch (error) {
    logger.error("Failed to add comment on due date update", { error });
  }
};

/**
 * Generates a system comment for card priority updates
 */
export const commentOnCardPriorityUpdate = async (
  card: TeamKanbanCard,
  oldPriority: string | null
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    
    if (!oldPriority && card.priority) {
      await anyStore.addComment(
        card.id,
        `🔴 Task priority was set to ${card.priority}.`,
        true
      );
    } else if (oldPriority && !card.priority) {
      await anyStore.addComment(
        card.id,
        `⚪ Task priority was removed.`,
        true
      );
    } else if (oldPriority && card.priority) {
      await anyStore.addComment(
        card.id,
        `🔄 Task priority was changed from ${oldPriority} to ${card.priority}.`,
        true
      );
    }
  } catch (error) {
    logger.error("Failed to add comment on priority update", { error });
  }
};

/**
 * Generates a system comment for card assignee updates
 */
export const commentOnCardAssigneeUpdate = async (
  card: TeamKanbanCard,
  _previousAssignee: { id: string; name: string } | null
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    
    if (!_previousAssignee && card.assignee) {
      await anyStore.addComment(
        card.id,
        `👤 ${card.assignee.name} was assigned to this card.`,
        true
      );
    } else if (_previousAssignee && !card.assignee) {
      await anyStore.addComment(
        card.id,
        `👤 ${_previousAssignee.name} was unassigned from this card.`,
        true
      );
    } else if (_previousAssignee && card.assignee && _previousAssignee.id !== card.assignee.id) {
      await anyStore.addComment(
        card.id,
        `👤 Card was reassigned from ${_previousAssignee.name} to ${card.assignee.name}.`,
        true
      );
    }
  } catch (error) {
    logger.error("Failed to add comment on assignee update", { error });
  }
};

/**
 * Generates a system comment when a card is moved between columns
 */
export const commentOnCardMove = async (
  card: TeamKanbanCard,
  sourceColumnTitle: string,
  targetColumnTitle: string
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    
    await anyStore.addComment(
      card.id,
      `🔄 Task was moved from "${sourceColumnTitle}" to "${targetColumnTitle}".`,
      true
    );
  } catch (error) {
    logger.error("Failed to add comment on card movement", { error });
  }
};

/**
 * Generates a system comment when a card's completion status changes
 */
export const commentOnCardCompletion = async (
  card: TeamKanbanCard,
  wasArchived: boolean
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    
    if (card.archived_at && !wasArchived) {
      await anyStore.addComment(
        card.id,
        `✅ Task was marked as completed.`,
        true
      );
    } else if (!card.archived_at && wasArchived) {
      await anyStore.addComment(
        card.id,
        `🔄 Task was marked as not completed.`,
        true
      );
    }
  } catch (error) {
    logger.error("Failed to add comment on completion status change", { error });
  }
};

/**
 * Generates a system comment when a subtask is created
 */
export const commentOnSubtaskCreation = async (
  cardId: string,
  subtask: TeamKanbanSubtask
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    
    await anyStore.addComment(
      cardId,
      `📋 Subtask "${subtask.title}" was added.`,
      true
    );
  } catch (error) {
    logger.error("Failed to add comment on subtask creation", { error });
  }
};

/**
 * Generates a system comment when a subtask's title is updated
 */
export const commentOnSubtaskTitleUpdate = async (
  cardId: string,
  subtask: TeamKanbanSubtask,
  oldTitle: string
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    
    await anyStore.addComment(
      cardId,
      `✏️ Subtask title was updated from "${oldTitle}" to "${subtask.title}".`,
      true
    );
  } catch (error) {
    logger.error("Failed to add comment on subtask title update", { error });
  }
};

/**
 * Generates a system comment when a subtask is completed or uncompleted
 */
export const commentOnSubtaskCompletion = async (
  cardId: string,
  subtask: TeamKanbanSubtask,
  wasCompleted: boolean
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    
    if (subtask.is_completed && !wasCompleted) {
      await anyStore.addComment(
        cardId,
        `✅ Subtask "${subtask.title}" was marked as completed.`,
        true
      );
    } else if (!subtask.is_completed && wasCompleted) {
      await anyStore.addComment(
        cardId,
        `🔄 Subtask "${subtask.title}" was marked as not completed.`,
        true
      );
    }
  } catch (error) {
    logger.error("Failed to add comment on subtask completion status change", { error });
  }
};

/**
 * Generates a system comment when a subtask is deleted
 */
export const commentOnSubtaskDeletion = async (
  cardId: string,
  subtaskTitle: string
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    
    await anyStore.addComment(
      cardId,
      `🗑️ Subtask "${subtaskTitle}" was deleted.`,
      true
    );
  } catch (error) {
    logger.error("Failed to add comment on subtask deletion", { error });
  }
};

/**
 * Generates a system comment when a card is deleted
 */
export const commentOnCardDeletion = async (
  card: TeamKanbanCard
): Promise<void> => {
  try {
    const store = useTeamKanbanStore.getState();
    // Cast the store to any to bypass TypeScript checking
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const anyStore = store as any;
    
    // Since the card is deleted, we might log this in a parent card
    // or in a system log depending on your application's structure
    await anyStore.addComment(
      card.id,
      `🗑️ Card "${card.title}" was deleted.`,
      true
    );
  } catch (error) {
    logger.error("Failed to add comment on card deletion", { error });
  }
};