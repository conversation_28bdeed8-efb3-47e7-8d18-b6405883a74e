/* eslint-disable */
import { TeamKanbanCard as IndexCardType, TeamKanbanSubtask as IndexSubtaskType } from '../types';
import { TeamKanbanCard as KanbanCardType, TeamKanbanSubtask as KanbanSubtaskType } from '../types/kanban';
import { UnifiedKanbanCard, UnifiedKanbanSubtask } from '../types/unified';

// Define a type that matches the inline subtask definition in kanban.ts
interface KanbanInlineSubtask {
    id: string;
    title: string;
    is_completed: boolean;
    order_index: number;
    due_date: string | null;
    assignee: {
        id: string;
        name: string;
        avatar: string;
    } | null;
}

/**
 * Type adapter functions to handle inconsistencies between different TeamKanbanCard imports
 * This helps resolve type issues when components receive cards from different sources
 */

/**
 * Checks if a card is of the kanban type structure
 * @param card Any card object
 * @returns Boolean indicating if it matches kanban structure
 */

export const isKanbanCardType = (card: any): card is KanbanCardType => {
    // Check for specific properties that would identify a kanban card
    return card &&
        typeof card === 'object' &&
        'column_id' in card &&
        Array.isArray(card.subtasks) &&

        (card.subtasks.length === 0 || card.subtasks.some((s: any) => 'is_completed' in s));
}

/**
 * Checks if a card is of the index type structure
 * @param card Any card object
 * @returns Boolean indicating if it matches index structure
 */

export const isIndexCardType = (card: any): card is IndexCardType => {
    if (!card || typeof card !== 'object') return false;
    const cardObj = card as Record<string, unknown>;

    // Check for specific properties that would identify an index card
    return 'id' in cardObj &&
        'title' in cardObj &&
        'subtasks' in cardObj &&
        (!('subtasks' in cardObj) ||
            (Array.isArray(cardObj.subtasks) &&
                (cardObj.subtasks.length === 0 ||
                    (cardObj.subtasks.length > 0 &&
                        typeof cardObj.subtasks[0] === 'object' &&
                        cardObj.subtasks[0] !== null &&
                        'assignee_id' in (cardObj.subtasks[0] as Record<string, unknown>)))));
}

/**
 * Checks if a subtask is of the kanban type structure
 * @param subtask Any subtask object
 * @returns Boolean indicating if it matches kanban structure
 */

export const isKanbanSubtaskType = (subtask: any): subtask is KanbanSubtaskType => {
    if (!subtask || typeof subtask !== 'object') return false;
    const subtaskObj = subtask as Record<string, unknown>;

    // Check for specific properties that would identify a kanban subtask
    return 'id' in subtaskObj &&
        'title' in subtaskObj &&
        'is_completed' in subtaskObj &&
        'assignee' in subtaskObj;
}

/**
 * Checks if a subtask is of the index type structure
 * @param subtask Any subtask object
 * @returns Boolean indicating if it matches index structure
 */

export const isIndexSubtaskType = (subtask: any): subtask is IndexSubtaskType => {
    if (!subtask || typeof subtask !== 'object') return false;
    const subtaskObj = subtask as Record<string, unknown>;

    // Check for specific properties that would identify an index subtask
    return 'id' in subtaskObj &&
        'title' in subtaskObj &&
        'is_completed' in subtaskObj &&
        'assignee_id' in subtaskObj;
}

/**
 * Converts subtask to the kanban inline format
 * @param subtask Source subtask to convert
 * @returns Subtask in KanbanInlineSubtask format
 */

export const toKanbanInlineSubtask = (subtask: any): KanbanInlineSubtask => {
    if (isKanbanSubtaskType(subtask as KanbanSubtaskType)) {
        // If already matching format, return as is
        return {
            id: subtask.id,
            title: subtask.title || '',
            is_completed: subtask.is_completed || false,
            order_index: subtask.order_index || 0,
            due_date: subtask.due_date || null,
            assignee: subtask.assignee || null
        };
    }

    // Otherwise convert from IndexSubtask format
    return {
        id: subtask.id,
        title: subtask.title || '',
        is_completed: subtask.is_completed || false,
        order_index: subtask.order_index || 0,
        due_date: subtask.due_date || null,
        assignee: subtask.assignee_id ? {
            id: subtask.assignee_id,
            name: 'Unknown User',
            avatar: ''
        } : null
    };
};

/**
 * Converts any card type to a KanbanCardType
 * @param card Any card object (KanbanCardType, IndexCardType, or UnifiedKanbanCard)
 * @returns The card as a KanbanCardType
 */
export const asKanbanCard = (card: KanbanCardType | IndexCardType | UnifiedKanbanCard): KanbanCardType => {
    // If it's already a KanbanCardType, return it directly
    if (isKanbanCardType(card)) {
        return card;
    }

    // Convert the subtasks to the expected inline format
    const convertedSubtasks = (card.subtasks || []).map(toKanbanInlineSubtask);

    // Create a KanbanCardType with the properly formatted subtasks
    return {
        ...card,
        subtasks: convertedSubtasks
    } as KanbanCardType;
};

/**
 * Normalizes a subtask to ensure it has the necessary properties
 * Useful for ensuring consistent subtask structure across different types
 * @param subtask A subtask object of any type
 * @returns A normalized subtask with consistent structure
 */

export const normalizeSubtask = (subtask: any): KanbanSubtaskType => {
    // Ensure the subtask has all the required properties
    return {
        id: subtask.id,
        card_id: subtask.card_id,
        title: subtask.title || '',
        is_completed: !!subtask.is_completed,
        created_at: subtask.created_at || new Date().toISOString(),
        updated_at: subtask.updated_at || new Date().toISOString(),
        order_index: subtask.order_index || 0,
        due_date: subtask.due_date || null,
        assignee_id: subtask.assignee_id || (subtask.assignee?.id as string) || null
    };
};

/**
 * Converts any subtask type to a KanbanSubtaskType
 * @param subtask Any subtask object (KanbanSubtaskType, IndexSubtaskType, or UnifiedKanbanSubtask)
 * @returns The subtask as a KanbanSubtaskType
 */
export const asKanbanSubtask = (subtask: KanbanSubtaskType | IndexSubtaskType | UnifiedKanbanSubtask): KanbanSubtaskType => {
    // If it's already a KanbanSubtaskType, return it directly
    if (isKanbanSubtaskType(subtask) && !('assignee' in subtask)) {
        return subtask;
    }

    // Extract the basic properties that match KanbanSubtaskType
    return {
        id: subtask.id,
        card_id: subtask.card_id,
        title: subtask.title,
        is_completed: subtask.is_completed,
        created_at: subtask.created_at,
        updated_at: subtask.updated_at,
        order_index: subtask.order_index,
        due_date: subtask.due_date || null,
        assignee_id: subtask.assignee_id || ((subtask as any).assignee?.id as string) || undefined
    };
};

/**
 * Converts any subtask type to an IndexSubtaskType
 * @param subtask Any subtask object (KanbanSubtaskType, IndexSubtaskType, or UnifiedKanbanSubtask)
 * @returns The subtask as an IndexSubtaskType
 */
export const asIndexSubtask = (subtask: KanbanSubtaskType | IndexSubtaskType | UnifiedKanbanSubtask): IndexSubtaskType => {
    // If it's already an IndexSubtaskType with assignee, return it directly
    if (isIndexSubtaskType(subtask) && 'assignee' in subtask) {
        return subtask as IndexSubtaskType;
    }

    // Start with basic properties 
    const result = {
        id: subtask.id,
        card_id: subtask.card_id,
        title: subtask.title,
        is_completed: subtask.is_completed,
        created_at: subtask.created_at,
        updated_at: subtask.updated_at,
        order_index: subtask.order_index,
        due_date: subtask.due_date,
        assignee_id: subtask.assignee_id
    } as IndexSubtaskType;

    // Add assignee if present or null
    if ('assignee' in subtask && subtask.assignee) {
        result.assignee = subtask.assignee;
    } else if (subtask.assignee_id) {
        // If only assignee_id is available, we need to create a minimal assignee object
        result.assignee = {
            id: subtask.assignee_id,
            name: '',  // These would need to be populated from user data
            avatar: ''
        };
    } else {
        result.assignee = null;
    }

    return result;
};

/**
 * Normalizes all subtasks in a card to ensure consistent structure
 * This is useful when we need to make sure all subtasks have the same shape
 * @param card A card with subtasks
 * @returns The card with normalized subtasks
 */

export const normalizeCardSubtasks = <T extends { subtasks?: any[] }>(card: T): T => {
    // If no subtasks, return as is
    if (!card.subtasks || !Array.isArray(card.subtasks)) {
        return card;
    }

    // Clone the card to avoid mutations
    const result = { ...card };

    // Normalize all subtasks
    result.subtasks = card.subtasks.map(subtask => {
        return normalizeSubtask(subtask);
    });

    return result;
}; 