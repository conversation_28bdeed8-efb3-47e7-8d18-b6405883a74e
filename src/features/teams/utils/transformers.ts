import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TeamKanbanSubtask, User, PriorityLevel } from '../types';

// Input interfaces for transformer functions
interface ProfileInput {
    id?: string;
    email?: string;
    full_name?: string;
    avatar_url?: string;
}

interface SubtaskInput {
    id: string;
    card_id: string;
    title: string;
    is_completed?: boolean;
    order_index?: number;
    assignee?: {
        id: string;
        full_name?: string;
        avatar_url?: string;
    } | null;
    due_date?: string | null;
    created_at: string;
    updated_at: string;
}

interface CardInput {
    id: string;
    title: string;
    description?: string;
    order_index?: number;
    column_id: string;
    team_id: string;
    assignee?: {
        id: string;
        full_name?: string;
        avatar_url?: string;
    } | null;
    priority?: 'P1' | 'P2' | 'P3';
    due_date?: string | null;
    created_at: string;
    updated_at: string;
    position_updated_at: string;
    archived_at?: string | null;
    deleted_at?: string | null;
    subtasks?: SubtaskInput[];
}

export const calculateNewOrderIndex = (items: { order_index: number }[], _oldIndex: number, newIndex: number): number => {
    if (items.length === 0) return 1024;

    // Special handling for single item
    if (items.length === 1) {
        return newIndex === 0 ? items[0].order_index - 1024 : items[0].order_index + 1024;
    }

    // Moving to start
    if (newIndex === 0) {
        return items[0].order_index - 1024;
    }

    // Moving to end
    if (newIndex === items.length) {
        return items[items.length - 1].order_index + 1024;
    }

    // Moving between items
    const prevItem = items[newIndex - 1];
    const nextItem = items[newIndex];
    return prevItem.order_index + Math.floor((nextItem.order_index - prevItem.order_index) / 2);
};

export const calculateNewTimestamp = (items: { position_updated_at: string }[], newIndex: number): string => {
    try {
        const now = new Date();
        const BASE_OFFSET = 60000; // 1 minute in milliseconds

        if (items.length === 0) {
            return now.toISOString();
        }

        if (newIndex === 0) {
            // Moving to start - use timestamp newer than first item
            const firstItem = items[0];
            const firstItemTime = new Date(firstItem.position_updated_at).getTime();
            return new Date(firstItemTime + BASE_OFFSET).toISOString();
        }

        if (newIndex >= items.length) {
            // Moving to end - use timestamp older than last item
            const lastItem = items[items.length - 1];
            const lastItemTime = new Date(lastItem.position_updated_at).getTime();
            return new Date(lastItemTime - BASE_OFFSET).toISOString();
        }

        // Moving between items - calculate a timestamp between the surrounding items
        const prevItem = items[newIndex - 1];
        const nextItem = items[newIndex];
        const prevTime = new Date(prevItem.position_updated_at).getTime();
        const nextTime = new Date(nextItem.position_updated_at).getTime();

        if (prevTime <= nextTime) {
            // If timestamps are out of order, use current time with offset
            return new Date(now.getTime() - (newIndex * BASE_OFFSET)).toISOString();
        }

        // Calculate middle point between the two timestamps
        const midTime = prevTime - Math.floor((prevTime - nextTime) / 2);
        return new Date(midTime).toISOString();
    } catch (error) {
        console.warn('Error calculating timestamp:', error);
        // Fallback: use current time with index-based offset
        const fallbackTime = new Date().getTime() - (newIndex * 60000);
        return new Date(fallbackTime).toISOString();
    }
};

export const transformUser = (profile: ProfileInput): User | null => {
    if (!profile) return null;
    return {
        id: profile.id || '',
        email: profile.email || '',
        name: profile.full_name || profile.email?.split('@')[0] || 'Unknown User',
        avatar: profile.avatar_url || `https://api.dicebear.com/7.x/initials/svg?seed=${profile.id}`
    };
};

export const transformSubtask = (subtask: SubtaskInput): TeamKanbanSubtask => ({
    id: subtask.id,
    card_id: subtask.card_id,
    title: subtask.title,
    is_completed: subtask.is_completed || false,
    order_index: subtask.order_index || 0,
    assignee: subtask.assignee ? {
        id: subtask.assignee.id,
        name: subtask.assignee.full_name || 'Unknown User',
        avatar: subtask.assignee.avatar_url || `https://api.dicebear.com/7.x/initials/svg?seed=${subtask.assignee.id}`
    } : undefined,
    due_date: subtask.due_date || null,
    created_at: subtask.created_at,
    updated_at: subtask.updated_at
});

export const transformCard = (card: CardInput): TeamKanbanCard => ({
    id: card.id,
    title: card.title,
    description: card.description || '',
    order_index: card.order_index || 0,
    column_id: card.column_id,
    team_id: card.team_id,
    assignee: card.assignee ? {
        id: card.assignee.id,
        name: card.assignee.full_name || 'Unknown User',
        avatar: card.assignee.avatar_url || `https://api.dicebear.com/7.x/initials/svg?seed=${card.assignee.id}`
    } : null,
    priority: card.priority ? (card.priority as PriorityLevel) : PriorityLevel.P3,
    due_date: card.due_date !== undefined ? card.due_date : null,
    created_at: card.created_at,
    updated_at: card.updated_at,
    position_updated_at: card.position_updated_at,
    archived_at: card.archived_at !== undefined ? card.archived_at : null,
    deleted_at: card.deleted_at !== undefined ? card.deleted_at : null,
    subtasks: (card.subtasks || []).map(transformSubtask),
    comments: []
});

export function transformComment(comment: {
    id: string;
    content: string;
    user_id: string;
    card_id: string;
    created_at: string;
    updated_at: string;
    is_system: boolean;
    edited_at?: string | null;
    deleted_at?: string | null;
    profile?: {
        id: string;
        full_name?: string;
        avatar_url?: string;
    };
}) {
    // Extract properties we need in our interface
    const { 
        id, 
        content, 
        card_id,
        created_at, 
        updated_at, 
        is_system, 
        user_id, 
        edited_at, 
        deleted_at,
        profile 
    } = comment;
    
    // Add user_id back to match the TeamKanbanComment interface
    return {
        id,
        content,
        card_id,
        user_id,  // Keep the user_id field
        created_at,
        updated_at,
        edited_at: edited_at ?? null,
        deleted_at: deleted_at ?? null,
        is_system,
        user: {
            id: user_id,
            name: profile?.full_name || 'Unknown User',
            avatar: profile?.avatar_url || ''
        }
    };
}
