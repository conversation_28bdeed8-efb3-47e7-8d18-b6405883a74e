import { TeamMember } from '@/features/teams/types/team';

export type { TeamMember };

/** Status of a team */
export type TeamStatus = 'Active' | 'Inactive';

/** 
 * Represents a team goal with progress tracking
 * @interface TeamGoal
 */
interface TeamGoal {
    /** Unique identifier for the goal */
    id: string;
    /** Title of the goal */
    title: string;
    /** Detailed description of the goal */
    description: string;
    /** Progress percentage (0-100) */
    progress: number;
    /** Due date for the goal */
    dueDate: string;
    /** Current status of the goal */
    status: 'Not Started' | 'In Progress' | 'Completed';
}

/**
 * Represents a team with its members and goals
 * @interface Team
 */
export interface Team {
    /** Unique identifier for the team */
    id: string;
    /** Name of the team */
    name: string;
    /** Description of the team's purpose */
    description: string;
    /** Current status of the team */
    status: TeamStatus;
    /** Team leader information */
    lead: {
        id: string;
        name: string;
        avatar: string;
    };
    /** List of team members */
    teamMembers: {
        id: string;
        name: string;
        avatar: string;
    }[];
    /** Overall progress percentage */
    progress: number;
    /** Project start date */
    startDate: string;
    /** Project end date */
    endDate: string;
    /** Team creation date */
    createdAt: string;
    /** Team goals */
    goals: TeamGoal[];
}

/**
 * Base interface for Kanban items with ordering
 * @interface TeamKanbanItem
 */
export interface TeamKanbanItem {
    /** Unique identifier */
    id: string;
    /** Position index for ordering */
    order_index: number;
}

/**
 * Priority levels for cards
 * @enum {string}
 */
export enum PriorityLevel {
    P1 = 'P1',
    P2 = 'P2',
    P3 = 'P3'
}

/**
 * Represents a user in the system
 * @interface User
 */
export interface User {
    /** Unique identifier */
    id: string;
    /** User's email address */
    email: string;
    /** Display name */
    name: string;
    /** Avatar URL */
    avatar: string;
}

/**
 * Represents a column in the Kanban board
 * @interface TeamKanbanColumn
 */
export interface TeamKanbanColumn {
    /** Unique identifier */
    id: string;
    /** Associated team ID */
    team_id: string;
    /** Column title */
    title: string;
    /** Position index for ordering */
    order_index: number;
    /** Creation timestamp */
    created_at: string;
}

/**
 * Contains data and state for a column
 * @interface ColumnData
 */
export interface ColumnData {
    /** Cards in this column */
    cards: TeamKanbanCard[];
    /** Loading state */
    loading: boolean;
    /** Error message if any */
    error: string | null;
    /** Whether more cards can be loaded */
    hasMore: boolean;
    /** Current page number */
    currentPage: number;
}

/**
 * Represents a card in the Kanban board
 * @interface TeamKanbanCard
 * @extends TeamKanbanItem
 */
export interface TeamKanbanCard extends TeamKanbanItem {
    /** Associated column ID */
    column_id: string;
    /** Associated team ID */
    team_id: string;
    /** Card title */
    title: string;
    /** Card description */
    description: string | null;
    /** Position index for ordering */
    order_index: number;
    /** Last position update timestamp */
    position_updated_at: string;
    /** Due date if any */
    due_date: string | null;
    /** Priority level */
    priority: PriorityLevel;
    /** Creation timestamp */
    created_at: string;
    /** Last update timestamp */
    updated_at: string;
    /** Deletion timestamp if deleted */
    deleted_at: string | null;
    /** Archive timestamp if archived */
    archived_at: string | null;
    /** Assigned user if any */
    assignee: {
        id: string;
        name: string;
        avatar: string;
    } | null;
    /** List of subtasks */
    subtasks: TeamKanbanSubtask[];
    /** List of comments */
    comments: TeamKanbanComment[];
}

/**
 * Represents a subtask in a card
 * @interface TeamKanbanSubtask
 * @extends TeamKanbanItem
 */
export interface TeamKanbanSubtask extends TeamKanbanItem {
    /** Associated card ID */
    card_id: string;
    /** Subtask title */
    title: string;
    /** Completion status */
    is_completed: boolean;
    /** Creation timestamp */
    created_at: string;
    /** Last update timestamp */
    updated_at: string;
    /** Due date if any */
    due_date: string | null;
    /** Assigned user ID if any */
    assignee_id?: string;
    /** Assigned user details if any */
    assignee?: {
        id: string;
        name: string;
        avatar: string;
    } | null;
}

/**
 * Represents a comment on a card
 * @interface TeamKanbanComment
 */
export interface TeamKanbanComment {
    /** Unique identifier */
    id: string;
    /** Associated card ID */
    card_id: string;
    /** Author user ID */
    user_id: string;
    /** Comment content */
    content: string;
    /** Creation timestamp */
    created_at: string;
    /** Last update timestamp */
    updated_at: string;
    /** Edit timestamp if edited */
    edited_at: string | null;
    /** Deletion timestamp if deleted */
    deleted_at: string | null;
    /** Whether this is a system-generated comment */
    is_system: boolean;
    /** Author information */
    user: {
        id: string;
        name: string;
        avatar: string;
    };
}

/**
 * Board slice of the Kanban store
 * @interface BoardSlice
 */
export interface BoardSlice {
    /** List of columns */
    columns: TeamKanbanColumn[];
    /** Column data map */
    columnData: Map<string, ColumnData>;
    /** Set of archived column IDs being shown */
    showArchivedColumns: Set<string>;
    /** Loading state */
    loading: boolean;
    /** Error message if any */
    error: string | null;

    // Actions
    /** Fetches board data for a team */
    fetchBoard: (teamId: string) => Promise<void>;
    /** Clears board state */
    clearBoard: () => void;
    /** Adds a new column */
    addColumn: (teamId: string, title: string) => Promise<void>;
    /** Updates column title */
    updateColumn: (columnId: string, title: string) => Promise<void>;
    /** Deletes a column */
    deleteColumn: (columnId: string) => Promise<void>;
    /** Reorders columns */
    reorderColumns: (teamId: string, columnIds: string[]) => Promise<void>;
    /** Toggles column archived state */
    toggleColumnArchived: (columnId: string) => Promise<{ count: number } | void>;
}

/**
 * Card slice of the Kanban store
 * @interface CardSlice
 */
export interface CardSlice {
    /** List of all cards */
    cards: TeamKanbanCard[];
    /** List of all subtasks */
    subtasks: TeamKanbanSubtask[];
    /** Card pages by column */
    cardPages: Record<string, TeamKanbanCard[]>;
    /** Whether more cards can be loaded by column */
    hasMoreCards: Record<string, boolean>;
    /** Loading states by column */
    loadingCards: Record<string, boolean>;

    // Actions
    /** Fetches cards for a column */
    fetchColumnCards: (columnId: string, page?: number, limit?: number, showArchived?: boolean) => Promise<{ count: number }>;
    /** Adds a new card */
    addCard: (columnId: string, teamId: string, title: string, description?: string) => Promise<void>;
    /** Updates a card */
    updateCard: (cardId: string, updates: Partial<TeamKanbanCard>) => Promise<TeamKanbanCard>;
    /** Deletes a card */
    deleteCard: (cardId: string) => Promise<void>;
    /** Moves a card to a different column */
    moveCard: (cardId: string, columnId: string, newIndex: number) => Promise<void>;
    /** Adds a subtask to a card */
    addSubtask: (cardId: string, title: string) => Promise<void>;
    /** Updates a subtask */
    updateSubtask: (subtaskId: string, updates: Partial<TeamKanbanSubtask>) => Promise<void>;
    /** Deletes a subtask */
    deleteSubtask: (subtaskId: string) => Promise<void>;
    /** Reorders subtasks */
    reorderSubtasks: (cardId: string, subtaskIds: string[]) => Promise<void>;
}

/**
 * Comment slice of the Kanban store
 * @interface CommentSlice
 */
export interface CommentSlice {
    /** List of all comments */
    comments: TeamKanbanComment[];
    /** Comment pages by card */
    commentPages: Record<string, TeamKanbanComment[]>;
    /** Whether more comments can be loaded by card */
    hasMoreComments: Record<string, boolean>;
    /** Loading states by card */
    loadingComments: Record<string, boolean>;

    // Actions
    /** Fetches comments for a card */
    fetchComments: (cardId: string, page?: number, limit?: number) => Promise<{ count: number }>;
    /** Adds a new comment */
    addComment: (cardId: string, content: string) => Promise<TeamKanbanComment>;
    /** Updates a comment */
    updateComment: (commentId: string, content: string) => Promise<TeamKanbanComment>;
    /** Deletes a comment */
    deleteComment: (commentId: string) => Promise<TeamKanbanComment>;
}

/**
 * User slice of the Kanban store
 * @interface UserSlice
 */
export interface UserSlice {
    /** List of all users */
    users: User[];
    /** Cached team members by team ID */
    teamMembers: Record<string, User[]>;
    /** Loading state for team members by team ID */
    loadingTeamMembers: Record<string, boolean>;

    // Actions
    /** Updates the users list */
    updateUsers: () => Promise<void>;
    /** Assigns a user to a card */
    assignUser: (userId: string, cardId: string) => Promise<void>;
    /** Unassigns a user from a card */
    unassignUser: (cardId: string) => Promise<void>;
    /** Fetches and caches team members for a specific team */
    fetchTeamMembers: (teamId: string) => Promise<User[]>;
}

/**
 * Filter slice for managing Kanban board filters
 */
export interface FilterSlice {
    filters: {
        showArchived: boolean;
        priorityFilter: PriorityLevel | null;
        assigneeFilter: string | null;
        presets: FilterPreset[];
        activePresetId: string | null;
    };
    setShowArchived: (show: boolean) => void;
    setPriorityFilter: (priority: PriorityLevel | null) => void;
    setAssigneeFilter: (assigneeId: string | null) => void;
    resetFilters: () => void;
    savePreset: (name: string, icon?: string) => string;
    updatePreset: (id: string, updates: Partial<FilterPreset>) => void;
    deletePreset: (id: string) => void;
    applyPreset: (id: string) => void;
    clearActivePreset: () => void;
}

/**
 * Complete Kanban store combining all slices
 * @interface TeamKanbanStore
 */
export interface TeamKanbanStore extends BoardSlice, CardSlice, CommentSlice, UserSlice, FilterSlice {
    // Store initialization and cleanup
    /** Initializes the store with team data */
    initializeStore: (teamId: string) => Promise<boolean>;
    /** Cleans up the store state */
    cleanup: () => void;
    /** Debug method to help diagnose store issues */
    debug?: () => {
        methods: string[];
        hasInitializeStore: boolean;
        hasFetchBoard: boolean;
        storeKeys: string[];
    };
    /** Utility to update a card in all parts of the store */
    updateCardInStore?: (updatedCard: TeamKanbanCard) => void;
}

/**
 * Represents a saved filter preset
 */
export interface FilterPreset {
    /** Unique identifier for the preset */
    id: string;

    /** Display name for the preset */
    name: string;

    /** Filter configuration */
    filters: {
        showArchived: boolean;
        priorityFilter: PriorityLevel | null;
        assigneeFilter: string | null;
    };

    /** Whether this is a default preset or user-created */
    isDefault?: boolean;

    /** Icon name if available */
    icon?: string;

    /** Order for displaying in the list */
    order?: number;
}

// Re-export all types from kanban
export * from './kanban'; 