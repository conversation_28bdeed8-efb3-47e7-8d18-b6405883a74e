export type TeamRole = 'admin' | 'manager' | 'member';
export type ProfileRole = 'admin' | 'manager' | 'user';
export type ProjectStatus = 'Active' | 'Inactive';

export interface Profile {
    id: string;
    fullName: string | null;
    email: string | null;
    avatarUrl: string | null;
    role: ProfileRole;
    createdAt: string;
}

export interface TeamMember {
    id: string;
    teamId: string;
    userId: string;
    name: string;
    email: string;
    profileRole: ProfileRole;
    joinedAt: string;
    avatarUrl: string | null;
}

export interface Team {
    id: string;
    name: string;
    objective: string | null;
    avatarUrl: string | null;
    createdAt: string;
    updatedAt: string;
    createdBy: Profile;
    status: ProjectStatus;
    progress: number;
    endDate: string | null;
}

export interface TeamWithMembers extends Team {
    members: TeamMember[];
}