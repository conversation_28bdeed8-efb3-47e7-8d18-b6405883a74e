export interface TeamKanbanItem {
    id: string;
    order_index: number;
}

export enum PriorityLevel {
    P1 = 'P1',
    P2 = 'P2',
    P3 = 'P3'
}

export interface TeamKanbanColumn {
    id: string;
    team_id: string;
    title: string;
    order_index: number;
    created_at: string;
}

export interface TeamKanbanCard extends TeamKanbanItem {
    column_id: string;
    team_id: string;
    title: string;
    description: string | null;
    order_index: number;
    position_updated_at: string;
    due_date: string | null;
    priority: PriorityLevel;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    archived_at: string | null;
    assignee: {
        id: string;
        name: string;
        avatar: string;
    } | null;
    subtasks: {
        id: string;
        title: string;
        is_completed: boolean;
        order_index: number;
        due_date: string | null;
        assignee: {
            id: string;
            name: string;
            avatar: string;
        } | null;
    }[];
    comments: {
        id: string;
        content: string;
        created_at: string;
        edited_at: string | null;
        deleted_at: string | null;
        user: {
            id: string;
            name: string;
            avatar: string;
        };
    }[];
}

export interface TeamKanbanSubtask extends TeamKanbanItem {
    card_id: string;
    title: string;
    is_completed: boolean;
    created_at: string;
    updated_at: string;
    due_date: string | null;
    assignee_id?: string;
}

export interface TeamKanbanComment {
    id: string;
    card_id: string;
    user_id: string;
    content: string;
    created_at: string;
    updated_at: string;
    edited_at: string | null;
    deleted_at: string | null;
}

export interface User {
    id: string;
    email: string;
    name: string;
    avatar: string;
}

export interface FilterState {
    /** Show archived cards */
    showArchived: boolean;
    /** Filter by priority level */
    priorityFilter: PriorityLevel | null;
    /** Filter by assignee ID */
    assigneeFilter: string | null;
}

export interface ColumnData {
    cards: TeamKanbanCard[];
    loading: boolean;
    error: string | null;
    hasMore: boolean;
    currentPage: number;
}

export interface TeamKanbanState {
    columns: TeamKanbanColumn[];
    cards: TeamKanbanCard[];
    subtasks: TeamKanbanSubtask[];
    comments: TeamKanbanComment[];
    users: User[];
    loading: boolean;
    error: string | null;
    commentPages: Record<string, TeamKanbanComment[]>;
    hasMoreComments: Record<string, boolean>;
    loadingComments: Record<string, boolean>;
    cardPages: Record<string, TeamKanbanCard[]>;
    hasMoreCards: Record<string, boolean>;
    loadingCards: Record<string, boolean>;
    columnData: Map<string, ColumnData>;
    filters: FilterState;

    // Board Operations
    fetchBoard: (teamId: string) => Promise<void>;
    setError: (error: Error) => void;

    // Column Operations
    addColumn: (teamId: string, title: string) => Promise<void>;
    deleteColumn: (columnId: string) => Promise<void>;
    updateColumn: (columnId: string, title: string) => Promise<void>;
    reorderColumns: (teamId: string, columnIds: string[]) => Promise<void>;

    // Card Operations
    fetchColumnCards: (columnId: string, page?: number, limit?: number, showArchived?: boolean) => Promise<{ count: number }>;
    addCard: (columnId: string, teamId: string, title: string) => Promise<void>;
    moveCard: (cardId: string, newColumnId: string, newIndex: number) => Promise<void>;
}

export interface TeamKanbanStore extends TeamKanbanState {
    // Filter state properties
    showArchived: boolean;
    priorityFilter: PriorityLevel | null;
    assigneeFilter: string | null;

    // Filter actions
    setShowArchived: (show: boolean) => void;
    setPriorityFilter: (priority: PriorityLevel | null) => void;
    setAssigneeFilter: (assigneeId: string | null) => void;
    resetFilters: () => void;

    // Other existing properties
    setError: (error: Error) => void;
    fetchBoard: (teamId: string) => Promise<void>;
    clearBoard: () => void;

    // Store initialization and cleanup
    initializeStore: (teamId: string) => Promise<boolean>;
    cleanup: () => void;

    // Column actions
    addColumn: (teamId: string, title: string) => Promise<void>;
    updateColumn: (columnId: string, title: string) => Promise<void>;
    deleteColumn: (columnId: string) => Promise<void>;
    reorderColumns: (teamId: string, columnIds: string[]) => Promise<void>;
    toggleColumnArchived: (columnId: string) => Promise<void>;

    // Card actions
    fetchColumnCards: (columnId: string, page?: number, limit?: number, showArchived?: boolean) => Promise<{ count: number }>;
    addCard: (columnId: string, teamId: string, title: string, description?: string) => Promise<void>;
    updateCard: (cardId: string, updates: Partial<TeamKanbanCard>) => Promise<TeamKanbanCard>;
    deleteCard: (cardId: string) => Promise<void>;
    moveCard: (cardId: string, newColumnId: string, newIndex: number) => Promise<void>;

    // Subtask actions
    addSubtask: (cardId: string, title: string) => Promise<void>;
    updateSubtask: (subtaskId: string, updates: Partial<TeamKanbanSubtask>) => Promise<void>;
    deleteSubtask: (subtaskId: string) => Promise<void>;
    reorderSubtasks: (cardId: string, subtaskIds: string[]) => Promise<void>;

    // Comment actions
    fetchComments: (cardId: string, page?: number, limit?: number) => Promise<{ count: number | null }>;
    addComment: (cardId: string, content: string) => Promise<TeamKanbanComment>;
    updateComment: (commentId: string, content: string) => Promise<TeamKanbanComment>;
    deleteComment: (commentId: string) => Promise<TeamKanbanComment>;

    // User actions
    updateUsers: () => Promise<void>;
    assignUser: (userId: string, cardId: string) => Promise<void>;
    unassignUser: (cardId: string) => Promise<void>;
    fetchTeamMembers: (teamId: string) => Promise<User[]>;
} 