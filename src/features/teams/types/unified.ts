/**
 * Unified Type System for TeamKanban
 * 
 * This file provides a consolidated type system that resolves inconsistencies
 * between the parallel type systems in index.ts and kanban.ts.
 * 
 * It defines a canonical type for each entity and provides type guards
 * to ensure type safety across the application.
 */

import { TeamKanbanCard as IndexCardType } from './index';
import { TeamKanbanCard as KanbanCardType, PriorityLevel, FilterState } from './kanban';

/**
 * Canonical priority levels enum
 */
export type { PriorityLevel };

/**
 * Canonical filter state interface
 */
export type { FilterState };

/**
 * Represents a user in the system (canonical)
 */
export interface UnifiedUser {
    id: string;
    email: string;
    name: string;
    avatar: string;
}

/**
 * Represents a basic Kanban item with ordering (canonical)
 */
export interface UnifiedKanbanItem {
    id: string;
    order_index: number;
}

/**
 * Represents an assignee (canonical)
 */
export interface UnifiedAssignee {
    id: string;
    name: string;
    avatar: string;
}

/**
 * Unified subtask type that combines properties from both systems
 */
export interface UnifiedKanbanSubtask extends UnifiedKanbanItem {
    card_id: string;
    title: string;
    is_completed: boolean;
    created_at: string;
    updated_at: string;
    due_date: string | null;
    // Support both ways of representing assignees
    assignee_id?: string;
    assignee?: UnifiedAssignee | null;
}

/**
 * Unified comment type
 */
export interface UnifiedKanbanComment {
    id: string;
    card_id: string;
    user_id: string;
    content: string;
    created_at: string;
    updated_at: string;
    edited_at: string | null;
    deleted_at: string | null;
    user: {
        id: string;
        name: string;
        avatar: string;
    };
}

/**
 * Canonical card type that combines properties from both systems
 */
export interface UnifiedKanbanCard extends UnifiedKanbanItem {
    column_id: string;
    team_id: string;
    title: string;
    description: string | null;
    order_index: number;
    position_updated_at: string;
    due_date: string | null;
    priority: PriorityLevel;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    archived_at: string | null;
    assignee: UnifiedAssignee | null;
    subtasks: UnifiedKanbanSubtask[];
    comments: UnifiedKanbanComment[];
}

/**
 * Canonical column type
 */
export interface UnifiedKanbanColumn {
    id: string;
    team_id: string;
    title: string;
    order_index: number;
    created_at: string;
}

/**
 * Type guards to check which version of the type system an object belongs to
 */

/**
 * Checks if a card is from the kanban.ts type system
 */
export function isKanbanCardType(card: unknown): card is KanbanCardType {
    if (!card || typeof card !== 'object') return false;
    const cardObj = card as Record<string, unknown>;
    if (!cardObj.subtasks || !Array.isArray(cardObj.subtasks)) return false;
    // Check if the first subtask has the assignee property (kanban.ts)
    return cardObj.subtasks.length === 0 ||
        (cardObj.subtasks[0] && typeof cardObj.subtasks[0] === 'object' && 'assignee' in cardObj.subtasks[0]);
}

/**
 * Checks if a card is from the index.ts type system
 */
export function isIndexCardType(card: unknown): card is IndexCardType {
    if (!card || typeof card !== 'object') return false;
    const cardObj = card as Record<string, unknown>;
    if (!cardObj.subtasks || !Array.isArray(cardObj.subtasks)) return false;
    // Check if the first subtask has the assignee_id property (index.ts)
    return cardObj.subtasks.length === 0 ||
        (cardObj.subtasks[0] && typeof cardObj.subtasks[0] === 'object' && 'assignee_id' in cardObj.subtasks[0]);
}

/**
 * Type assertion for better type safety
 */
export function assertUnifiedCard(card: unknown): asserts card is UnifiedKanbanCard {
    if (!card || typeof card !== 'object') {
        throw new Error('Not a valid card object');
    }
    const cardObj = card as Record<string, unknown>;
    if (!('id' in cardObj) || !('title' in cardObj) || !('subtasks' in cardObj)) {
        throw new Error('Missing required card properties');
    }
    if (!Array.isArray(cardObj.subtasks)) {
        throw new Error('Card subtasks must be an array');
    }
} 