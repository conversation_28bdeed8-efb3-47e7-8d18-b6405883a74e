import { supabase } from '@/lib/supabase';
import { transformComment } from '../utils/transformers';
import { errorHandler, ErrorType, ErrorSeverity } from '@/utils/errorHandler';

/**
 * Type assertion function to transform raw Supabase data into the expected format
 * for the transformComment function. This provides a type-safe way to transform the data
 * without using 'any'.
 */
function prepareCommentForTransformer(rawData: unknown): {
    id: string;
    content: string;
    user_id: string;
    card_id: string;
    created_at: string;
    updated_at: string;
    is_system: boolean;
    edited_at?: string | null;
    deleted_at?: string | null;
    profile?: {
        id: string;
        full_name?: string;
        avatar_url?: string;
    };
} {
    // Type assertion to object with minimal validation
    const data = rawData as Record<string, unknown>;

    return {
        id: String(data.id || ''),
        content: String(data.content || ''),
        user_id: String(data.user_id || ''),
        card_id: String(data.card_id || ''),
        created_at: String(data.created_at || new Date().toISOString()),
        updated_at: String(data.updated_at || new Date().toISOString()),
        is_system: Boolean(data.is_system || false),
        edited_at: data.edited_at as string | null,
        deleted_at: data.deleted_at as string | null,
        profile: data.profile as { id: string; full_name?: string; avatar_url?: string; } | undefined
    };
}

const COMMENT_SELECT = `
    id,
    card_id,
    user_id,
    content,
    created_at,
    updated_at,
    edited_at,
    deleted_at,
    is_system,
    profile:profiles!user_id(
        id,
        email,
        full_name,
        avatar_url
    )
`;

export const commentApi = {
    fetchComments: async (cardId: string, page: number = 1, limit: number = 10) => {
        try {
            const { data: comments, error, count } = await supabase
                .from('kanban_comments')
                .select(COMMENT_SELECT, { count: 'exact' })
                .eq('card_id', cardId)
                .order('created_at', { ascending: false })
                .range((page - 1) * limit, page * limit - 1);

            if (error) {
                throw error;
            }

            return { comments: (comments || []).map(comment => transformComment(prepareCommentForTransformer(comment))), count };
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'commentApi',
                action: 'fetchComments',
                context: { cardId, page, limit }
            });
            throw new Error('Failed to fetch comments');
        }
    },

    addComment: async (cardId: string, content: string, userId: string, isSystem: boolean = false) => {
        try {
            const { data: comment, error } = await supabase
                .from('kanban_comments')
                .insert({
                    card_id: cardId,
                    user_id: userId,
                    content,
                    is_system: isSystem
                })
                .select(COMMENT_SELECT)
                .single();

            if (error) {
                throw error;
            }

            return transformComment(prepareCommentForTransformer(comment));
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'commentApi',
                action: 'addComment',
                context: { cardId, userId, contentLength: content?.length, isSystem }
            });
            throw new Error('Failed to add comment');
        }
    },

    addSystemComment: async (cardId: string, content: string, userId: string) => {
        return commentApi.addComment(cardId, content, userId, true);
    },

    updateComment: async (commentId: string, content: string) => {
        try {
            const { data: comment, error } = await supabase
                .from('kanban_comments')
                .update({
                    content,
                    edited_at: new Date().toISOString()
                })
                .eq('id', commentId)
                .select(COMMENT_SELECT)
                .single();

            if (error) {
                throw error;
            }

            return transformComment(prepareCommentForTransformer(comment));
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'commentApi',
                action: 'updateComment',
                context: { commentId }
            });
            throw new Error('Failed to update comment');
        }
    },

    deleteComment: async (commentId: string): Promise<boolean> => {
        try {
            // Get current user ID for permission check
            const { data: { user }, error: authError } = await supabase.auth.getUser();

            if (authError) {
                errorHandler.handleAuthError(authError, {
                    component: 'commentApi',
                    action: 'deleteComment',
                    context: { commentId, stage: 'auth-check' }
                });
                throw new Error('Authentication error');
            }

            const currentUserId = user?.id;

            // First fetch the comment to verify ownership
            const { data: commentData, error: getError } = await supabase
                .from('kanban_comments')
                .select('*')
                .eq('id', commentId)
                .single();

            if (getError) {
                errorHandler.handleApiError(getError, {
                    component: 'commentApi',
                    action: 'deleteComment',
                    context: {
                        commentId,
                        userId: currentUserId,
                        stage: 'fetch-comment'
                    }
                });
                throw getError;
            }

            // Already soft-deleted check
            if (commentData.deleted_at !== null) {
                return true;
            }

            // Permission check
            if (currentUserId !== commentData.user_id) {
                const permissionError = new Error('You do not have permission to delete this comment');
                errorHandler.captureError(permissionError, ErrorType.AUTHORIZATION, {
                    component: 'commentApi',
                    action: 'deleteComment',
                    context: {
                        commentId,
                        userId: currentUserId,
                        commentUserId: commentData.user_id
                    }
                });
                throw permissionError;
            }

            // Try various approaches to update the comment
            let deleteSuccess = false;

            // Approach 1: UPDATE soft delete with RLS policy
            try {
                const { error: updateError } = await supabase
                    .from('kanban_comments')
                    .update({
                        deleted_at: new Date().toISOString(),
                        content: '[deleted]'
                    })
                    .eq('id', commentId)
                    .eq('user_id', currentUserId);

                if (!updateError) {
                    deleteSuccess = true;
                }
            } catch (_err) {
                // Continue to next approach
            }

            // Approach 2: Use stored function/procedure
            if (!deleteSuccess) {
                try {
                    const { error: updateError } = await supabase
                        .rpc('soft_delete_comment', {
                            comment_id: commentId,
                            user_id: currentUserId
                        });

                    if (!updateError) {
                        deleteSuccess = true;
                    }
                } catch (_err) {
                    // Continue to next approach
                }
            }

            // Approach 3: UPSERT with RLS policy
            if (!deleteSuccess) {
                try {
                    const { error: upsertError } = await supabase
                        .from('kanban_comments')
                        .upsert({
                            id: commentId,
                            deleted_at: new Date().toISOString(),
                            content: '[deleted]',
                            user_id: currentUserId // Required for RLS policy matching
                        });

                    if (!upsertError) {
                        deleteSuccess = true;
                    }
                } catch (_err) {
                    // Continue to fallback
                }
            }

            // If all approaches failed, try client-side fallback
            if (!deleteSuccess) {
                errorHandler.captureError(
                    new Error('Database deletion failed, falling back to client-side deletion'),
                    ErrorType.API,
                    {
                        component: 'commentApi',
                        action: 'deleteComment',
                        context: {
                            commentId,
                            syntheticUpdate: true
                        },
                        severity: ErrorSeverity.MEDIUM
                    }
                );
                return true;
            }

            return deleteSuccess;
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'commentApi',
                action: 'deleteComment',
                context: { commentId }
            });
            throw error; // Rethrow to allow UI to show appropriate error
        }
    }
};
