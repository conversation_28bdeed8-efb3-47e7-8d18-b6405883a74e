import { supabase } from '@/lib/supabase';
import { TeamKanbanColumn } from '../types';
import { errorHandler } from '@/utils/errorHandler';

export const boardApi = {
    async fetchColumns(teamId: string): Promise<TeamKanbanColumn[]> {
        try {
            const { data, error } = await supabase
                .from('kanban_columns')
                .select('*')
                .eq('team_id', teamId)
                .order('order_index');

            if (error) {
                throw error;
            }

            return data;
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'boardApi',
                action: 'fetchColumns',
                context: { teamId }
            });
            throw new Error('Failed to fetch columns');
        }
    },

    addColumn: async (teamId: string, title: string, orderIndex: number) => {
        try {
            const { data: newColumn, error } = await supabase
                .from('kanban_columns')
                .insert({
                    team_id: teamId,
                    title,
                    order_index: orderIndex
                })
                .select()
                .single();

            if (error) {
                throw error;
            }

            return newColumn as TeamKanbanColumn;
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'boardApi',
                action: 'addColumn',
                context: { teamId, title, orderIndex }
            });
            throw new Error('Failed to add column');
        }
    },

    updateColumn: async (columnId: string, title: string) => {
        try {
            const { data: updatedColumn, error } = await supabase
                .from('kanban_columns')
                .update({ title })
                .eq('id', columnId)
                .select()
                .single();

            if (error) {
                throw error;
            }

            return updatedColumn as TeamKanbanColumn;
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'boardApi',
                action: 'updateColumn',
                context: { columnId, title }
            });
            throw new Error('Failed to update column');
        }
    },

    deleteColumn: async (columnId: string) => {
        try {
            const { error } = await supabase
                .from('kanban_columns')
                .delete()
                .eq('id', columnId);

            if (error) {
                throw error;
            }
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'boardApi',
                action: 'deleteColumn',
                context: { columnId }
            });
            throw new Error('Failed to delete column');
        }
    },

    reorderColumns: async (teamId: string, updates: { id: string; order_index: number }[]) => {
        try {
            // First, fetch existing columns to preserve required fields like title
            const { data: existingColumns, error: fetchError } = await supabase
                .from('kanban_columns')
                .select('*')
                .in('id', updates.map(update => update.id));

            if (fetchError) {
                throw fetchError;
            }

            if (!existingColumns || existingColumns.length !== updates.length) {
                throw new Error('Could not find all columns to update');
            }

            // Create updates that preserve existing data while updating order_index
            const columnsToUpdate = updates.map(update => {
                const existingColumn = existingColumns.find(col => col.id === update.id);
                if (!existingColumn) {
                    throw new Error(`Column ${update.id} not found`);
                }

                // Keep all existing column data but update the order_index and ensure team_id is set
                return {
                    ...existingColumn,
                    order_index: update.order_index,
                    team_id: teamId
                };
            });

            const { error } = await supabase
                .from('kanban_columns')
                .upsert(columnsToUpdate);

            if (error) {
                throw error;
            }
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'boardApi',
                action: 'reorderColumns',
                context: { teamId, updateCount: updates.length }
            });
            throw new Error('Failed to reorder columns');
        }
    }
};

