import { supabase } from '@/lib/supabase';
import { User } from '../types';

interface UserProfile {
    id: string;
    email?: string;
    full_name?: string;
    avatar_url?: string;
}

const transformUser = (profile: UserProfile): User => ({
    id: profile.id,
    email: profile.email || '',
    name: profile.full_name || profile.email?.split('@')[0] || 'Unknown User',
    avatar: profile.avatar_url || `https://api.dicebear.com/7.x/initials/svg?seed=${profile.email}`
});

export const userApi = {
    fetchUsers: async () => {
        const { data, error } = await supabase
            .from('profiles')
            .select(`
                id,
                email,
                full_name,
                avatar_url
            `);

        if (error) {
            console.error('Error fetching users:', error);
            return [];
        }

        // Transform the data to match the User type
        return (data || []).map(profile => transformUser(profile as UserProfile));
    },

    fetchTeamMembers: async (teamId: string) => {
        // Fetch users who are members of the specified team
        const { data, error } = await supabase
            .from('team_members')
            .select(`
                user_id,
                profiles:user_id(id, email, full_name, avatar_url)
            `)
            .eq('team_id', teamId);

        if (error) throw error;

        // Transform the data to match the User type
        return (data || [])
            .filter(item => item.profiles) // Filter out any null profiles
            .map(item => transformUser(item.profiles as unknown as UserProfile));
    },

    assignUserToCard: async (userId: string, cardId: string) => {
        const { error } = await supabase
            .from('kanban_cards')
            .update({ assignee_id: userId })
            .eq('id', cardId);

        if (error) throw error;
    },

    unassignUserFromCard: async (cardId: string) => {
        const { error } = await supabase
            .from('kanban_cards')
            .update({ assignee_id: null })
            .eq('id', cardId);

        if (error) throw error;
    },

    assignUserToSubtask: async (userId: string, subtaskId: string) => {
        const { error } = await supabase
            .from('kanban_subtasks')
            .update({ assignee_id: userId })
            .eq('id', subtaskId);

        if (error) throw error;
    },

    unassignUserFromSubtask: async (subtaskId: string) => {
        const { error } = await supabase
            .from('kanban_subtasks')
            .update({ assignee_id: null })
            .eq('id', subtaskId);

        if (error) throw error;
    }
};

