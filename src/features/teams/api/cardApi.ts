import { supabase } from '@/lib/supabase';
import { TeamKanbanCard, TeamKanbanSubtask, PriorityLevel } from '../types';
import { transformCard } from '../utils/transformers';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/utils/errorHandler';

/**
 * This file contains the API functions for interacting with cards in the kanban board.
 *
 * NOTE ON RELATIONSHIP QUERIES:
 * When querying Supabase with multiple tables that have the same foreign key name (assignee_id),
 * we need to use explicit relationship references like `profiles!kanban_cards_assignee_id_fkey`
 * instead of `assignee: assignee_id(...)` to avoid the "more than one relationship found" error.
 *
 * We also need to transform the returned data structure to match what our transformers expect.
 */

interface FetchCardsResponse {
    cards: TeamKanbanCard[];
    count: number;
}

/**
 * Type assertion function to transform raw Supabase data into the expected format
 * for the transformCard function. This provides a type-safe way to transform the data
 * without using 'any'.
 */
const prepareCardForTransformer = (rawData: unknown) => {
    // Type assertion to object with minimal validation
    const data = rawData as Record<string, unknown>;

    // Ensure priority is one of the valid PriorityLevel values
    let priority: 'P1' | 'P2' | 'P3' | undefined = undefined;
    if (data.priority === 'P1' || data.priority === 'P2' || data.priority === 'P3') {
        priority = data.priority;
    }

    // Process subtasks if they exist
    const processedSubtasks = (data.subtasks || []) as Array<{
        id: string;
        card_id: string;
        title: string;
        is_completed?: boolean;
        order_index?: number;
        assignee?: {
            id: string;
            full_name?: string;
            avatar_url?: string;
        } | null;
        due_date?: string | null;
        created_at: string;
        updated_at: string;
    }>;

    return {
        id: String(data.id || ''),
        title: String(data.title || ''),
        description: String(data.description || ''),
        column_id: String(data.column_id || ''),
        team_id: String(data.team_id || ''),
        order_index: Number(data.order_index || 0),
        created_at: String(data.created_at || new Date().toISOString()),
        updated_at: String(data.updated_at || new Date().toISOString()),
        position_updated_at: String(data.position_updated_at || new Date().toISOString()),
        assignee_id: data.assignee_id as string | null,
        due_date: data.due_date as string | null,
        archived_at: data.archived_at as string | null,
        deleted_at: data.deleted_at as string | null,
        priority,
        assignee: data.assignee as { id: string; full_name?: string; avatar_url?: string; } | undefined,
        subtasks: processedSubtasks
    };
};

const CARD_SELECT = `
    id, title, description, column_id, team_id, order_index, created_at, updated_at,
    archived_at, deleted_at, due_date, priority, position_updated_at,
    profiles!kanban_cards_assignee_id_fkey (id, full_name, avatar_url),
    kanban_subtasks (
        id, title, is_completed, order_index, due_date,
        profiles!kanban_subtasks_assignee_id_fkey (id, full_name, avatar_url)
    )
`;

export const cardApi = {
    async fetchCards(columnId: string, page: number = 1, limit: number = 10, showArchived: boolean = false): Promise<FetchCardsResponse> {
        // Log the raw input value
        logger.debug(`fetchCards raw showArchived: ${showArchived}, type: ${typeof showArchived}`);

        // Convert showArchived to boolean explicitly to handle any non-boolean values
        const showArchivedBool = Boolean(showArchived);

        // Log after conversion
        logger.debug(`fetchCards converted showArchived: ${showArchivedBool}`);

        // Ensure we have valid parameters
        if (!columnId) {
            throw new Error('Column ID is required');
        }

        logger.debug(`Fetching cards for column ${columnId} (page ${page}, limit ${limit}, showArchived: ${showArchivedBool})`);

        try {
            // Calculate start and end for pagination
            const start = (page - 1) * limit;
            const end = start + limit - 1;

            let query = supabase
                .from('kanban_cards')
                .select(CARD_SELECT, { count: 'exact' })
                .eq('column_id', columnId)
                .is('deleted_at', null);

            // Filter for archived/active cards
            if (showArchived) {
                query = query.not('archived_at', 'is', null);
            } else {
                query = query.is('archived_at', null);
            }

            // Order by position_updated_at (newest first) instead of order_index
            query = query
                .order('position_updated_at', { ascending: false })
                .range(start, end);

            const { data, error, count } = await query;

            if (error) {
                throw error;
            }

            // Transform the data to match the expected format
            const transformedData = data?.map(card => {
                return {
                    ...card,
                    assignee: card.profiles,
                    subtasks: card.kanban_subtasks?.map(subtask => ({
                        ...subtask,
                        assignee: subtask.profiles
                    })) || []
                };
            });

            return {
                cards: (transformedData || []).map(card => transformCard(prepareCardForTransformer(card))),
                count: count || 0
            };
        } catch (error) {
            // Improved error handling with better error details
            const errorMessage = error instanceof Error
                ? error.message
                : (error && typeof error === 'object' && 'message' in error)
                    ? String((error as { message: unknown }).message)
                    : 'Unknown error fetching cards';

            logger.error(`Error fetching cards for column ${columnId}:`, {
                error,
                errorMessage,
                columnId,
                showArchived: showArchivedBool
            });

            errorHandler.handleApiError(
                error instanceof Error ? error : new Error(errorMessage),
                {
                    component: 'cardApi',
                    action: 'fetchCards',
                    context: { columnId, page, limit, showArchived: showArchivedBool }
                }
            );

            throw new Error(`Failed to fetch cards: ${errorMessage}`);
        }
    },

    addCard: async (columnId: string, teamId: string, title: string, description?: string, assigneeId?: string, priority?: PriorityLevel) => {
        try {
            const { data: rawCard, error } = await supabase
                .from('kanban_cards')
                .insert({
                    column_id: columnId,
                    team_id: teamId,
                    title,
                    description,
                    assignee_id: assigneeId || null,
                    priority: priority || PriorityLevel.P3,
                    position_updated_at: new Date().toISOString(),
                    order_index: 0
                })
                .select(CARD_SELECT)
                .single();

            if (error) {
                throw error;
            }

            // Transform the data to match the expected format
            const transformedCard = {
                ...rawCard,
                assignee: rawCard.profiles,
                subtasks: rawCard.kanban_subtasks?.map(subtask => ({
                    ...subtask,
                    assignee: subtask.profiles
                })) || []
            };

            return transformCard(prepareCardForTransformer(transformedCard));
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'cardApi',
                action: 'addCard',
                context: { columnId, teamId, title }
            });
            throw new Error('Failed to add card');
        }
    },

    updateCard: async (cardId: string, updates: Partial<TeamKanbanCard>) => {
        try {
            // When updating a card, we need to handle assignee in a different way
            // This extracts assignee and changes to assignee_id
            const { assignee, ...otherUpdates } = updates;

            // Prepare the database updates
            const dbUpdates: Record<string, unknown> = { ...otherUpdates };

            // Set assignee_id based on the assignee object
            if (assignee !== undefined) {
                dbUpdates.assignee_id = assignee?.id || null;
            }

            const { data: updatedCard, error } = await supabase
                .from('kanban_cards')
                .update(dbUpdates)
                .eq('id', cardId)
                .select(CARD_SELECT)
                .single();

            if (error) throw error;

            // Transform the card data
            const transformedCard = transformCard(prepareCardForTransformer(updatedCard));

            // Return the transformed card
            return transformedCard;
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'cardApi',
                action: 'updateCard',
                context: { cardId }
            });
            throw error;
        }
    },

    deleteCard: async (cardId: string) => {
        try {
            const { error } = await supabase
                .from('kanban_cards')
                .update({
                    deleted_at: new Date().toISOString(),
                    archived_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                })
                .eq('id', cardId);

            if (error) {
                throw error;
            }
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'cardApi',
                action: 'deleteCard',
                context: { cardId }
            });
            throw new Error('Failed to delete card');
        }
    },

    moveCard: async (cardId: string, columnId: string, positionUpdatedAt: string) => {
        try {
            const { error } = await supabase
                .from('kanban_cards')
                .update({
                    column_id: columnId,
                    position_updated_at: positionUpdatedAt,
                    updated_at: new Date().toISOString()
                })
                .eq('id', cardId);

            if (error) {
                throw error;
            }
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'cardApi',
                action: 'moveCard',
                context: { cardId, columnId }
            });
            throw new Error('Failed to move card');
        }
    },

    // Subtask operations
    addSubtask: async (cardId: string, title: string, orderIndex: number) => {
        try {
            const { data: newSubtask, error } = await supabase
                .from('kanban_subtasks')
                .insert({
                    card_id: cardId,
                    title,
                    order_index: orderIndex
                })
                .select()
                .single();

            if (error) {
                throw error;
            }

            return newSubtask as TeamKanbanSubtask;
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'cardApi',
                action: 'addSubtask',
                context: { cardId, title, orderIndex }
            });
            throw new Error('Failed to add subtask');
        }
    },

    updateSubtask: async (subtaskId: string, updates: Partial<TeamKanbanSubtask>) => {
        try {
            // Extract assignee from updates but don't send to database
            const { assignee: _assignee, ...otherUpdates } = updates;

            // Prepare the database updates
            const dbUpdates: Record<string, unknown> = { ...otherUpdates };

            // Set assignee_id based on the assignee object if provided
            if ('assignee' in updates) {
                dbUpdates.assignee_id = updates.assignee?.id || null;
            }

            const { data: updatedSubtask, error } = await supabase
                .from('kanban_subtasks')
                .update(dbUpdates)
                .eq('id', subtaskId)
                .select(`
                    *,
                    assignee:profiles!kanban_subtasks_assignee_id_fkey(*)
                `)
                .single();

            if (error) {
                throw error;
            }

            const transformedSubtask = {
                ...updatedSubtask,
                assignee: updatedSubtask.assignee ? {
                    id: updatedSubtask.assignee.id,
                    name: updatedSubtask.assignee.full_name || '',
                    avatar: updatedSubtask.assignee.avatar_url || ''
                } : null
            };

            return transformedSubtask as TeamKanbanSubtask;
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'cardApi',
                action: 'updateSubtask',
                context: { subtaskId, updates }
            });
            throw new Error('Failed to update subtask');
        }
    },

    deleteSubtask: async (subtaskId: string) => {
        try {
            const { error } = await supabase
                .from('kanban_subtasks')
                .delete()
                .eq('id', subtaskId);

            if (error) {
                throw error;
            }
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'cardApi',
                action: 'deleteSubtask',
                context: { subtaskId }
            });
            throw new Error('Failed to delete subtask');
        }
    },

    reorderSubtasks: async (updates: { id: string; order_index: number }[]) => {
        try {
            const { error } = await supabase
                .from('kanban_subtasks')
                .upsert(updates);

            if (error) {
                throw error;
            }
        } catch (error) {
            errorHandler.handleApiError(error, {
                component: 'cardApi',
                action: 'reorderSubtasks',
                context: { updates }
            });
            throw new Error('Failed to reorder subtasks');
        }
    }
};
