import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { TeamKanbanStore } from '../types';
import { createBoardSlice } from './slices/board';
import { createCardSlice } from './slices/card';
import { createCommentSlice } from './slices/comment';
import { createUserSlice } from './slices/user';
import { createFilterSlice } from './slices/filters';
// import { supabase } from '@/lib/supabase';
// These types are imported but not directly used in this file
// They are kept here for documentation purposes
import {
    // TeamKanbanCard,
    // TeamKanbanSubtask,
    // PriorityLevel,
    // TeamKanbanColumn,
    // TeamKanbanComment,
    // User,
    // FilterState
} from '@/features/teams/types/kanban';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/utils/errorHandler';
import { TeamKanbanCard } from '@/features/teams/types';

// Commented out but kept for future reference as it may be used later
// const calculateNewOrderIndex = (items: { order_index: number }[], oldIndex: number, newIndex: number): number => {
//     if (!items.length) return 0;
//     if (items.length === 1) return 1;

//     if (newIndex === 0) {
//         return items[0].order_index - 1;
//     }

//     if (newIndex >= items.length) {
//         return items[items.length - 1].order_index + 1;
//     }

//     const prevItemIndex = Math.max(0, newIndex - 1);
//     const nextItemIndex = Math.min(items.length - 1, newIndex);

//     return (items[prevItemIndex].order_index + items[nextItemIndex].order_index) / 2;
// }

// Type for user profiles from the database
// Keeping these interfaces for documentation purposes even if not used directly
// interface TeamMemberProfile {
//     id: string;
//     email: string | null;
//     full_name: string | null;
//     avatar_url: string | null;
// }

// interface ColumnData {
//     cards: TeamKanbanCard[];
//     loading: boolean;
//     error: string | null;
//     hasMore: boolean;
//     currentPage: number;
// }

// interface UserProfile {
//     id: string;
//     full_name: string | null;
//     avatar_url: string | null;
// }

// These interfaces are not directly used but kept for documentation purposes
// interface KanbanSubtaskResponse {
//     id: string;
//     title: string;
//     is_completed: boolean;
//     order_index: number;
//     due_date: string | null;
//     assignee: UserProfile | null;
// }

// interface KanbanCommentResponse {
//     id: string;
//     content: string;
//     created_at: string;
//     edited_at: string | null;
//     deleted_at: string | null;
//     user: UserProfile;
// }

// interface KanbanCardResponse {
//     id: string;
//     title: string;
//     description: string | null;
//     column_id: string;
//     team_id: string;
//     order_index: number;
//     due_date: string | null;
//     priority: string | null;
//     created_at: string;
//     updated_at: string;
//     position_updated_at: string;
//     deleted_at: string | null;
//     archived_at: string | null;
//     assignee: UserProfile | null;
//     subtasks: KanbanSubtaskResponse[];
//     comments: KanbanCommentResponse[];
// }

// interface TeamKanbanState {
//     columns: TeamKanbanColumn[];
//     cards: TeamKanbanCard[];
//     subtasks: TeamKanbanSubtask[];
//     comments: TeamKanbanComment[];
//     users: User[];
//     loading: boolean;
//     error: string | null;
//     commentPages: Record<string, TeamKanbanComment[]>;
//     hasMoreComments: Record<string, boolean>;
//     loadingComments: Record<string, boolean>;
//     cardPages: Record<string, TeamKanbanCard[]>;
//     hasMoreCards: Record<string, boolean>;
//     loadingCards: Record<string, boolean>;
//     columnData: Map<string, ColumnData>;
//     showArchivedColumns: Set<string>;
//     teamMembers: Record<string, User[]>;
//     loadingTeamMembers: Record<string, boolean>;
//     filters: FilterState;
// }

// This initialState definition is not used directly but kept for reference
// const initialState: Omit<TeamKanbanState, 'setError' | 'updateSubtask' | 'deleteCard' | 'updateCard' | 'addSubtask' | 'deleteSubtask' | 'addComment' | 'fetchComments' | 'updateComment' | 'deleteComment' | 'fetchColumnCards' | 'updateUsers' | 'fetchSubtasks' | 'toggleColumnArchived' | 'moveCard' | 'fetchTeamMembers' | 'setShowArchived' | 'setPriorityFilter' | 'setAssigneeFilter' | 'resetFilters'> = {
//     columns: [],
//     cards: [],
//     subtasks: [],
//     comments: [],
//     users: [],
//     loading: false,
//     error: null,
// };

// Keep a singleton instance to prevent double-initialization issues
// Using a more explicit type instead of 'any' to prevent type errors
let storeInstance: TeamKanbanStore | null = null;

/**
 * Creates the main Kanban store with all slices combined.
 * Uses Zustand's devtools middleware for Redux DevTools integration.
 * 
 * The store is structured into five main slices:
 * - Board: Manages columns and board layout
 * - Card: Manages cards and subtasks
 * - Comment: Manages card comments
 * - User: Manages users and assignments
 * - Filter: Manages filtering state
 * 
 * @example
 * ```typescript
 * const store = useTeamKanbanStore();
 * 
 * // Initialize store for a team
 * await store.initializeStore('team-123');
 * 
 * // Access state
 * const columns = store.columns;
 * const cards = store.cards;
 * 
 * // Perform actions
 * await store.addCard('column-123', 'team-123', 'New Task');
 * await store.moveCard('card-123', 'column-456', 2);
 * ```
 */
export const useTeamKanbanStore = create<TeamKanbanStore>()(
    devtools(
        (...args) => {
            // If we already have a store instance, return it to prevent double-initialization
            if (storeInstance) {
                return storeInstance;
            }

            // Create a new store instance
            const [set, get] = args;

            // Create slices with explicit method binding to prevent minification issues
            const boardSlice = createBoardSlice(...args);
            const cardSlice = createCardSlice(...args);
            const commentSlice = createCommentSlice(...args);
            const userSlice = createUserSlice(...args);
            const filterSlice = createFilterSlice(...args);

            const store = {
                // Spread slices
                ...boardSlice,
                ...cardSlice,
                ...commentSlice,
                ...userSlice,
                ...filterSlice,

                // Add a debug method to help diagnose issues
                debug: () => {
                    const state = get();
                    const methods = Object.keys(state).filter(
                        key => typeof state[key as keyof TeamKanbanStore] === 'function'
                    );

                    return {
                        methods,
                        hasInitializeStore: typeof state.initializeStore === 'function',
                        hasFetchBoard: typeof state.fetchBoard === 'function',
                        storeKeys: Object.keys(state)
                    };
                },

                /**
                 * Initializes the store with data for a specific team.
                 * This includes:
                 * 1. Clearing existing data
                 * 2. Loading users
                 * 3. Loading board layout
                 * 4. Loading initial cards for each column
                 * 
                 * @param teamId - The ID of the team to initialize
                 * @returns Promise<boolean> - True if initialization was successful
                 */
                initializeStore: async (teamId: string) => {
                    const store = get() as TeamKanbanStore;

                    // Ensure teamId is valid
                    if (!teamId) {
                        const error = new Error('Invalid team ID for store initialization');
                        logger.error('Store initialization failed:', error);
                        set({ error: error.message });
                        return false;
                    }

                    try {
                        logger.debug(`Initializing TeamKanban store for team: ${teamId}`);

                        // Reset state but preserve methods
                        if (typeof store.cleanup === 'function') {
                            store.cleanup();
                            logger.debug('Store state cleaned up');
                        } else {
                            logger.warn('Cleanup method not available, attempting to continue initialization');
                            // Apply a minimal cleanup to prevent conflicts
                            set({
                                columns: [],
                                columnData: new Map(),
                                error: null
                            });
                        }

                        // Initialize with data - using defensive programming
                        let hasErrors = false;

                        // Step 1: Load users
                        try {
                            if (typeof store.updateUsers === 'function') {
                                await store.updateUsers();
                                logger.debug('Users loaded successfully');
                            } else {
                                logger.warn('updateUsers method not available, skipping user loading');
                                hasErrors = true;
                            }
                        } catch (error) {
                            // Continue even if user loading fails
                            logger.error('Failed to load users:', error);
                            errorHandler.handleApiError(error, {
                                component: 'TeamKanbanStore',
                                action: 'initializeStore/updateUsers',
                                context: { teamId }
                            });
                            hasErrors = true;
                        }

                        // Step 2: Load board structure - use direct method reference to avoid minification issues
                        try {
                            // Use the fetchBoard method directly from the boardSlice to avoid minification issues
                            await boardSlice.fetchBoard(teamId);
                            logger.debug('Board structure loaded successfully');
                        } catch (error) {
                            // This is a critical failure
                            logger.error('Failed to load board structure:', error);
                            errorHandler.handleApiError(error, {
                                component: 'TeamKanbanStore',
                                action: 'initializeStore/fetchBoard',
                                context: { teamId }
                            });
                            set({ error: error instanceof Error ? error.message : 'Failed to load board structure' });
                            return false;
                        }

                        // Step 3: Load team members
                        try {
                            // Use direct slice reference to avoid minification issues
                            await userSlice.fetchTeamMembers(teamId);
                            logger.debug('Team members loaded successfully');
                        } catch (error) {
                            // Continue even if team member loading fails
                            logger.error('Failed to load team members:', error);
                            errorHandler.handleApiError(error, {
                                component: 'TeamKanbanStore',
                                action: 'initializeStore/fetchTeamMembers',
                                context: { teamId }
                            });
                            hasErrors = true;
                        }

                        // Step 4: Initialize local filter presets
                        try {
                            // Local filter presets are already handled by the filter slice
                            // No need to fetch from remote source
                            logger.debug('Using local filter presets');
                        } catch (error) {
                            logger.error('Failed to initialize filter presets:', error);
                            hasErrors = true;
                        }

                        // Log initialization status
                        if (hasErrors) {
                            logger.warn(`TeamKanban store initialized with some errors for team: ${teamId}`);
                        } else {
                            logger.info(`TeamKanban store successfully initialized for team: ${teamId}`);
                        }

                        return true;
                    } catch (error) {
                        // Handle any unexpected errors during initialization
                        logger.error('Unexpected error during store initialization:', error);
                        errorHandler.handleApiError(error, {
                            component: 'TeamKanbanStore',
                            action: 'initializeStore',
                            context: { teamId }
                        });
                        set({ error: error instanceof Error ? error.message : 'Store initialization failed' });
                        return false;
                    }
                },

                /**
                 * Cleans up the store state by clearing all data.
                 * Should be called when unmounting the Kanban board or switching teams.
                 * In development mode (with StrictMode), we preserve methods to handle double-invocation.
                 */
                cleanup: () => {
                    const [set] = args;
                    logger.debug('Cleaning up TeamKanban store');

                    // Create a reset state object for both dev and prod modes
                    const resetState = {
                        columns: [],
                        cards: [],
                        subtasks: [],
                        comments: [],
                        users: [],
                        cardPages: {},
                        commentPages: {},
                        hasMoreCards: {},
                        hasMoreComments: {},
                        loadingCards: {},
                        loadingComments: {},
                        columnData: new Map(),
                        showArchivedColumns: new Set<string>(),
                        teamMembers: {},
                        loadingTeamMembers: {},
                        loading: false,
                        error: null,
                        filters: {
                            showArchived: false,
                            priorityFilter: null,
                            assigneeFilter: null,
                            presets: [],
                            activePresetId: null
                        }
                    };

                    try {
                        // Always preserve methods regardless of environment
                        // This ensures that initialization methods are available after cleanup
                        set(resetState, false); // Use false to avoid replacing methods
                        logger.debug('Store cleaned up (preserving methods)');
                    } catch (error) {
                        logger.error('Error during store cleanup:', error);
                        // In case of error, try the most basic cleanup
                        set({
                            columns: [],
                            columnData: new Map(),
                            error: 'Store cleanup failed'
                        }, false); // Still try to preserve methods
                    }
                },

                /**
                 * Utility method to update a card in all relevant data structures
                 * This ensures the card is updated consistently across all parts of the store
                 * 
                 * @param updatedCard - The updated card data
                 */
                updateCardInStore: (updatedCard: TeamKanbanCard) => {
                    const [set, get] = args;
                    const state = get();

                    try {
                        // Update in cards array
                        const cards = state.cards.map(card =>
                            card.id === updatedCard.id ? updatedCard : card
                        );

                        // Update in cardPages
                        const cardPages = { ...state.cardPages };
                        Object.keys(cardPages).forEach(columnId => {
                            cardPages[columnId] = cardPages[columnId].map(card =>
                                card.id === updatedCard.id ? updatedCard : card
                            );
                        });

                        // Update in columnData
                        const columnData = new Map(state.columnData);
                        for (const [columnId, data] of columnData.entries()) {
                            // Skip columns that don't have this card
                            if (!data.cards.some(card => card.id === updatedCard.id)) continue;

                            columnData.set(columnId, {
                                ...data,
                                cards: data.cards.map(card =>
                                    card.id === updatedCard.id ? updatedCard : card
                                )
                            });
                        }

                        // Apply all updates at once
                        set({ cards, cardPages, columnData });
                    } catch (error) {
                        logger.error('Error updating card in store:', error);
                    }
                }
            };

            // Save the store instance
            storeInstance = store;
            return store;
        },
        { name: 'TeamKanbanStore' }
    )
);

// Export store type for type inference
export type { TeamKanbanStore };

/**
 * Hook to access the Kanban store with proper error handling.
 * Wraps Zustand's useStore hook to provide better error messages.
 * 
 * @template T - The type of the selected state
 * @param selector - Function to select state from the store
 * @param equalityFn - Optional equality function for state comparison
 * @returns T - The selected state
 * 
 * @example
 * ```typescript
 * const columns = useKanbanStore(state => state.columns);
 * const cards = useKanbanStore(state => state.cards);
 * ```
 */
export function useKanbanStore<T>(
    selector: (state: TeamKanbanStore) => T,
    equalityFn?: (a: T, b: T) => boolean
): T {
    try {
        return useTeamKanbanStore(selector, equalityFn as (a: T, b: T) => boolean);
    } catch (error) {
        console.error('Error accessing Kanban store:', error);
        throw error;
    }
}

/**
 * Collection of selector functions for common store operations.
 * These selectors are memoized for better performance.
 */
export const kanbanSelectors = {
    // Board selectors
    /** Select all columns */
    selectColumns: (state: TeamKanbanStore) => state.columns,

    /** Select a column by ID */
    selectColumnById: (columnId: string) => (state: TeamKanbanStore) =>
        state.columns.find(col => col.id === columnId),

    /** Select column data (cards, loading state, etc.) */
    selectColumnData: (columnId: string) => (state: TeamKanbanStore) =>
        state.columnData.get(columnId),

    /** Check if a column is archived */
    selectIsColumnArchived: (columnId: string) => (state: TeamKanbanStore) =>
        state.showArchivedColumns.has(columnId),

    // Card selectors
    /** Select a card by ID */
    selectCardById: (cardId: string) => (state: TeamKanbanStore) =>
        state.cards.find(card => card.id === cardId),

    /** Select all cards in a column */
    selectColumnCards: (columnId: string) => (state: TeamKanbanStore) =>
        state.cardPages[columnId] || [],

    /** Select card loading state for a column */
    selectCardLoadingState: (columnId: string) => (state: TeamKanbanStore) =>
        state.loadingCards[columnId] || false,

    /** Check if a column has more cards to load */
    selectHasMoreCards: (columnId: string) => (state: TeamKanbanStore) =>
        state.hasMoreCards[columnId] || false,

    // Comment selectors
    /** Select all comments for a card */
    selectCardComments: (cardId: string) => (state: TeamKanbanStore) =>
        state.commentPages[cardId] || [],

    /** Select comment loading state for a card */
    selectCommentLoadingState: (cardId: string) => (state: TeamKanbanStore) =>
        state.loadingComments[cardId] || false,

    /** Check if a card has more comments to load */
    selectHasMoreComments: (cardId: string) => (state: TeamKanbanStore) =>
        state.hasMoreComments[cardId] || false,

    // Team members selectors
    /** Select team members for a specific team */
    selectTeamMembers: (teamId: string) => (state: TeamKanbanStore) =>
        state.teamMembers[teamId] || [],

    /** Select loading state for team members */
    selectTeamMembersLoading: (teamId: string) => (state: TeamKanbanStore) =>
        state.loadingTeamMembers[teamId] || false,

    // User selectors
    /** Select all users */
    selectUsers: (state: TeamKanbanStore) => state.users,

    /** Select a user by ID */
    selectUserById: (userId: string) => (state: TeamKanbanStore) =>
        state.users.find(user => user.id === userId),

    // Filter selectors
    selectFilters: (state: TeamKanbanStore) => state.filters,
    selectShowArchived: (state: TeamKanbanStore) => state.filters.showArchived,
    selectPriorityFilter: (state: TeamKanbanStore) => state.filters.priorityFilter,
    selectAssigneeFilter: (state: TeamKanbanStore) => state.filters.assigneeFilter,

    // Filtered data selectors
    selectFilteredColumnCards: (columnId: string) => (state: TeamKanbanStore) => {
        const columnData = state.columnData.get(columnId);
        if (!columnData) return [];

        let cards = columnData.cards;
        const { showArchived, priorityFilter, assigneeFilter } = state.filters;

        // Apply filters
        if (!showArchived) {
            cards = cards.filter(card => !card.archived_at);
        }

        if (priorityFilter !== null) {
            cards = cards.filter(card => card.priority === priorityFilter);
        }

        if (assigneeFilter !== null) {
            if (assigneeFilter === 'unassigned') {
                cards = cards.filter(card => !card.assignee);
            } else {
                cards = cards.filter(card => card.assignee?.id === assigneeFilter);
            }
        }

        return cards;
    },
}; 