import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { Team, TeamStatus } from '../types';
import { supabase } from '@/lib/supabase';

interface TeamsStore {
    teams: Team[];
    loading: boolean;
    error: string | null;
    showInactive: boolean;
    showAllTeams: boolean;
    canToggleInactive: boolean;
    isAdmin: boolean;
    setTeams: (teams: Team[]) => void;
    addTeam: (team: Team) => void;
    updateTeam: (id: string, team: Partial<Team>) => void;
    deleteTeam: (id: string) => void;
    fetchTeams: () => Promise<void>;
    toggleShowInactive: () => void;
    toggleShowAllTeams: () => void;
}

export const useTeamsStore = create<TeamsStore>()(
    persist(
        (set, get) => ({
            teams: [],
            loading: false,
            error: null,
            showInactive: false,
            showAllTeams: false,
            canToggleInactive: false,
            isAdmin: false,
            setTeams: (teams) => set({ teams }),
            addTeam: (team) =>
                set((state) => ({ teams: [...state.teams, team] })),
            updateTeam: (id, updatedTeam) =>
                set((state) => ({
                    teams: state.teams.map((team) =>
                        team.id === id ? { ...team, ...updatedTeam } : team
                    ),
                })),
            deleteTeam: (id) =>
                set((state) => ({
                    teams: state.teams.filter((team) => team.id !== id),
                })),
            fetchTeams: async () => {
                set({ loading: true, error: null });
                try {
                    // Get current user's session and role
                    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                    if (sessionError) throw sessionError;
                    if (!session) throw new Error('No active session');

                    const { data: userProfile, error: profileError } = await supabase
                        .from('profiles')
                        .select('role')
                        .eq('id', session.user.id)
                        .single();

                    if (profileError) throw profileError;
                    const isAdmin = userProfile?.role?.toLowerCase() === 'admin';
                    const isManager = userProfile?.role?.toLowerCase() === 'manager';
                    const canToggleInactive = isAdmin || isManager;
                    const showInactive = get().showInactive && canToggleInactive;
                    const showAllTeams = get().showAllTeams && isAdmin;

                    set({ canToggleInactive, isAdmin });

                    // Query teams based on user role and settings
                    let query = supabase
                        .from('teams')
                        .select(`
                            *,
                            team_members (
                                user_id,
                                profiles (
                                    id,
                                    full_name,
                                    avatar_url,
                                    role
                                )
                            )
                        `);

                    // For admin users showing all teams, don't filter by team membership
                    // For everyone else (including admins not showing all teams), filter by team membership
                    if (!showAllTeams) {
                        query = supabase
                            .from('teams')
                            .select(`
                                *,
                                team_members!inner (
                                    user_id,
                                    profiles!inner (
                                        id,
                                        full_name,
                                        avatar_url,
                                        role
                                    )
                                )
                            `)
                            .eq('team_members.user_id', session.user.id);
                    }

                    // Only fetch active teams unless showInactive is true
                    if (!showInactive) {
                        query = query.eq('status', 'Active');
                    }

                    const { data: teams, error: fetchError } = await query
                        .order('status', { ascending: true }) // 'Active' comes before 'Inactive'
                        .order('created_at', { ascending: false });

                    if (fetchError) throw fetchError;
                    if (!teams) throw new Error('No teams data received');

                    const transformedTeams = teams?.map(team => {
                        const members = team.team_members || [];
                        const lead = members.find((m: { profiles: { role: string } }) =>
                            m.profiles?.role?.toLowerCase() === 'admin'
                        ) || members[0];
                        const status = team.status?.toLowerCase() === 'active' ? 'Active' : 'Inactive';

                        return {
                            id: team.id,
                            name: team.name || 'Unnamed Team',
                            description: team.objective || '',
                            status: status as TeamStatus,
                            lead: {
                                id: lead?.profiles?.id || '',
                                name: lead?.profiles?.full_name || 'No Lead',
                                avatar: lead?.profiles?.avatar_url || `https://ui-avatars.com/api/?name=No+Lead`,
                            },
                            teamMembers: members.map((member: {
                                profiles: {
                                    id: string;
                                    full_name: string | null;
                                    avatar_url: string | null;
                                }
                            }) => ({
                                id: member.profiles?.id || '',
                                name: member.profiles?.full_name || 'Unknown Member',
                                avatar: member.profiles?.avatar_url || `https://ui-avatars.com/api/?name=Unknown+Member`,
                            })),
                            progress: typeof team.progress === 'number' ? team.progress : 0,
                            startDate: team.created_at || new Date().toISOString(),
                            endDate: team.end_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                            createdAt: team.created_at || new Date().toISOString(),
                            goals: []
                        };
                    }) || [];

                    set({ teams: transformedTeams, loading: false });
                } catch (err) {
                    const error = err as Error;
                    set({ error: error.message || 'Failed to fetch teams', loading: false });
                }
            },
            toggleShowInactive: () => {
                set((state) => ({ showInactive: !state.showInactive }));
                get().fetchTeams(); // Refetch teams when toggling
            },
            toggleShowAllTeams: () => {
                set((state) => ({ showAllTeams: !state.showAllTeams }));
                get().fetchTeams(); // Refetch teams when toggling
            },
        }),
        {
            name: 'teams-storage',
        }
    )
); 