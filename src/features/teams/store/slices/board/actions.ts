import { TeamKanbanStore } from '../../../types';
import { BoardActions, BoardState } from './types';
import { boardApi } from '../../../api/boardApi';

/**
 * Type definition for setState function provided by Zustand
 * Used to update the board slice state
 */
type SetState = (
    partial: BoardState | Partial<BoardState> | ((state: BoardState) => BoardState | Partial<BoardState>),
    replace?: boolean
) => void;

/**
 * Type definition for getState function provided by Zustand
 * Used to access the full store state
 */
type GetState = () => TeamKanbanStore;

/**
 * Creates the actions for the board slice
 * Implements all board-related operations including fetching, adding, updating, and deleting columns
 * 
 * @param set - <PERSON>ust<PERSON>'s setState function
 * @param get - Zustand's getState function
 * @returns {BoardActions} Object containing all board actions
 */
export function createBoardActions(
    set: SetState,
    get: GetState
): BoardActions {
    return {
        /**
         * Fetches board data for a team
         * Initializes column data structures and prepares for card loading
         * 
         * @param teamId - The ID of the team to fetch board data for
         */
        fetchBoard: async (teamId: string) => {
            try {
                set({ loading: true, error: null });
                const columns = await boardApi.fetchColumns(teamId);

                // Initialize columnData Map with empty arrays
                const newColumnData = new Map();
                columns.forEach(column => {
                    newColumnData.set(column.id, {
                        cards: [],
                        loading: false,
                        error: null,
                        hasMore: true,
                        currentPage: 0
                    });
                });

                set({
                    columns,
                    columnData: newColumnData,
                    loading: false
                });

                // Fetch initial cards for each column
                await Promise.all(columns.map(column => {
                    // Check if the column is set to show archived
                    const isShowingArchived = get().showArchivedColumns.has(column.id);
                    return get().fetchColumnCards(column.id, 1, undefined, isShowingArchived);
                }));
            } catch (
             
            _error
            ) {
                set({
                    loading: false,
                    error: 'Failed to fetch board'
                });
            }
        },

        /**
         * Clears all board state
         * Used when unmounting the board or switching teams
         */
        clearBoard: () => {
            set({
                columns: [],
                columnData: new Map(),
                showArchivedColumns: new Set(),
                loading: false,
                error: null
            });
        },

        /**
         * Adds a new column to the board
         * Automatically calculates the order index based on existing columns
         * 
         * @param teamId - The ID of the team the column belongs to
         * @param title - The title of the new column
         */
        addColumn: async (teamId: string, title: string) => {
            try {
                set({ loading: true, error: null });
                const state = get() as BoardState;
                const lastOrderIndex = state.columns.length > 0
                    ? state.columns[state.columns.length - 1].order_index
                    : 0;

                const newColumn = await boardApi.addColumn(teamId, title, lastOrderIndex + 1024);

                const columnData = new Map(state.columnData);
                columnData.set(newColumn.id, {
                    cards: [],
                    loading: false,
                    error: null,
                    hasMore: true,
                    currentPage: 0
                });

                set({
                    columns: [...state.columns, newColumn],
                    columnData,
                    loading: false
                });
            } catch (error) {
                set({
                    error: error instanceof Error ? error.message : 'Failed to add column',
                    loading: false
                });
            }
        },

        /**
         * Updates a column's title
         * 
         * @param columnId - The ID of the column to update
         * @param title - The new title for the column
         */
        updateColumn: async (columnId: string, title: string) => {
            try {
                set({ loading: true, error: null });
                const updatedColumn = await boardApi.updateColumn(columnId, title);

                const state = get() as BoardState;
                const columns = state.columns.map(col =>
                    col.id === columnId ? updatedColumn : col
                );

                set({ columns, loading: false });
            } catch (error) {
                set({
                    error: error instanceof Error ? error.message : 'Failed to update column',
                    loading: false
                });
            }
        },

        /**
         * Deletes a column from the board
         * Also removes associated column data and cards
         * 
         * @param columnId - The ID of the column to delete
         */
        deleteColumn: async (columnId: string) => {
            try {
                set({ loading: true, error: null });
                await boardApi.deleteColumn(columnId);

                const state = get() as BoardState;
                const columns = state.columns.filter(col => col.id !== columnId);

                const columnData = new Map(state.columnData);
                columnData.delete(columnId);

                set({ columns, columnData, loading: false });
            } catch (error) {
                set({
                    error: error instanceof Error ? error.message : 'Failed to delete column',
                    loading: false
                });
            }
        },

        /**
         * Reorders columns in the board
         * Updates order indices for all affected columns
         * 
         * @param teamId - The ID of the team the columns belong to
         * @param columnIds - Array of column IDs in their new order
         */
        reorderColumns: async (teamId: string, columnIds: string[]) => {
            try {
                set({ loading: true, error: null });
                const updates = columnIds.map((id, index) => ({
                    id,
                    order_index: index * 1024
                }));

                await boardApi.reorderColumns(teamId, updates);

                const state = get() as BoardState;
                const orderedColumns = columnIds
                    .map(id => state.columns.find(col => col.id === id))
                    .filter((col): col is NonNullable<typeof col> => col !== undefined)
                    .map((col, index) => ({
                        ...col,
                        order_index: index * 1024
                    }));

                set({ columns: orderedColumns, loading: false });
            } catch (error) {
                set({
                    error: error instanceof Error ? error.message : 'Failed to reorder columns',
                    loading: false
                });
            }
        },

        /**
         * Toggles the archived state of a column
         * Resets and reloads card data when toggling
         * 
         * @param columnId - The ID of the column to toggle
         */
        toggleColumnArchived: async (columnId: string) => {
            try {
                set({ loading: true, error: null });
                const state = get() as BoardState;
                const showArchivedColumns = new Set(state.showArchivedColumns);

                if (showArchivedColumns.has(columnId)) {
                    showArchivedColumns.delete(columnId);
                } else {
                    showArchivedColumns.add(columnId);
                }

                // Reset column data and fetch cards with new archived state
                const columnData = state.columnData.get(columnId) || {
                    cards: [],
                    loading: false,
                    error: null,
                    hasMore: true,
                    currentPage: 0
                };

                const newColumnData = new Map(state.columnData);
                newColumnData.set(columnId, {
                    ...columnData,
                    cards: [], // Clear existing cards
                    currentPage: 0,
                    hasMore: true
                });

                set({
                    showArchivedColumns,
                    columnData: newColumnData,
                    loading: false
                });

                // Fetch cards with new archived state
                const store = get() as TeamKanbanStore;
                const isShowingArchived = store.showArchivedColumns.has(columnId);
                await store.fetchColumnCards(columnId, 1, undefined, isShowingArchived);
            } catch (error) {
                set({
                    error: error instanceof Error ? error.message : 'Failed to toggle column archive state',
                    loading: false
                });
            }
        }
    };
} 