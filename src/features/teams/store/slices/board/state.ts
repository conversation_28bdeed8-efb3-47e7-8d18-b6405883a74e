import { BoardState } from './types';

/**
 * Initial state for the board slice
 * Initializes empty collections and default values for all state properties
 * 
 * @type {BoardState}
 */
export const initialBoardState: BoardState = {
    /** Empty array of columns */
    columns: [],

    /** Empty map for column data */
    columnData: new Map(),

    /** Empty set for archived columns */
    showArchivedColumns: new Set(),

    /** Initial loading state is false */
    loading: false,

    /** Initial error state is null */
    error: null
}; 