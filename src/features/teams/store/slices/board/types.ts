import { TeamKanbanColumn, ColumnData } from '../../../types';

/**
 * State interface for the board slice
 * @interface BoardState
 */
export interface BoardState {
    /** List of all columns in the board */
    columns: TeamKanbanColumn[];

    /** Map of column data indexed by column ID */
    columnData: Map<string, ColumnData>;

    /** Set of archived column IDs that are currently visible */
    showArchivedColumns: Set<string>;

    /** Loading state for board operations */
    loading: boolean;

    /** Error message if any operation fails */
    error: string | null;
}

/**
 * Actions interface for the board slice
 * @interface BoardActions
 */
export interface BoardActions {
    /**
     * Fetches board data for a team
     * @param teamId - The ID of the team to fetch board data for
     */
    fetchBoard: (teamId: string) => Promise<void>;

    /**
     * Clears all board data
     */
    clearBoard: () => void;

    /**
     * Adds a new column to the board
     * @param teamId - The ID of the team the column belongs to
     * @param title - The title of the new column
     */
    addColumn: (teamId: string, title: string) => Promise<void>;

    /**
     * Updates a column's title
     * @param columnId - The ID of the column to update
     * @param title - The new title for the column
     */
    updateColumn: (columnId: string, title: string) => Promise<void>;

    /**
     * Deletes a column from the board
     * @param columnId - The ID of the column to delete
     */
    deleteColumn: (columnId: string) => Promise<void>;

    /**
     * Reorders columns in the board
     * @param teamId - The ID of the team the columns belong to
     * @param columnIds - Array of column IDs in their new order
     */
    reorderColumns: (teamId: string, columnIds: string[]) => Promise<void>;

    /**
     * Toggles the archived state of a column
     * @param columnId - The ID of the column to toggle
     */
    toggleColumnArchived: (columnId: string) => Promise<{ count: number } | void>;
}

/**
 * Combined interface for the board slice
 * @interface BoardSlice
 * @extends BoardState
 * @extends BoardActions
 */
export interface BoardSlice extends BoardState, BoardActions { } 