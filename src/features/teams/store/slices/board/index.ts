import { StateCreator } from 'zustand';
import { TeamKanbanStore } from '../../../types';
import { BoardSlice } from './types';
import { initialBoardState } from './state';
import { createBoardActions } from './actions';

export const createBoardSlice: StateCreator<
    TeamKanbanStore,
    [],
    [],
    BoardSlice
> = (set, get) => ({
    ...initialBoardState,
    ...createBoardActions(set, get)
}); 