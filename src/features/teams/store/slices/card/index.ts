import { StateCreator } from 'zustand';
import { TeamKanbanStore } from '../../../types';
import { CardSlice } from './types';
import { initialCardState } from './state';
import { createCardActions } from './actions';

/**
 * Creates the card slice for the Kanban store
 * Combines the initial state with card actions
 * 
 * @param set - <PERSON>ustand's setState function
 * @param get - Zustand's getState function
 * @returns {CardSlice} Combined card state and actions
 */
export const createCardSlice: StateCreator<TeamKanbanStore, [], [], CardSlice> = (set, get) => ({
    ...initialCardState,
    ...createCardActions(set, get)
});
