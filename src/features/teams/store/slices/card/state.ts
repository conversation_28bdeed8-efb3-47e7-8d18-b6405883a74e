import { CardState } from './types';

/**
 * Initial state for the card slice
 * Initializes empty collections and default values for all state properties
 * 
 * @type {CardState}
 */
export const initialCardState: CardState = {
    /** Empty array of cards */
    cards: [],

    /** Empty array of subtasks */
    subtasks: [],

    /** Empty record of card pages by column */
    cardPages: {},

    /** Empty record of hasMore flags by column */
    hasMoreCards: {},

    /** Empty record of loading states by column */
    loadingCards: {},

    /** Column data map */
    columnData: new Map(),
    
    /** Column definitions */
    columns: []
}; 