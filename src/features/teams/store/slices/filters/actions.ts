import { StateCreator } from 'zustand';
import { TeamKanbanStore } from '../../../types';
import { FilterPreset, PriorityLevel } from '../../../types';

/**
 * Generate a unique ID for presets
 * This is a simple implementation since we couldn't install uuid
 */
const generateId = (): string => {
    return Math.random().toString(36).substring(2, 15) +
        Math.random().toString(36).substring(2, 15);
};

// Default presets that are available to all users
const DEFAULT_PRESETS: FilterPreset[] = [
    {
        id: 'all-tasks',
        name: 'All Tasks',
        filters: {
            showArchived: false,
            priorityFilter: null,
            assigneeFilter: null
        },
        isDefault: true,
        icon: 'layers',
        order: 0
    },
    {
        id: 'my-tasks',
        name: 'My Tasks',
        filters: {
            showArchived: false,
            priorityFilter: null,
            assigneeFilter: null // This will be set dynamically when applied
        },
        isDefault: true,
        icon: 'user',
        order: 1
    },
    {
        id: 'high-priority',
        name: 'High Priority',
        filters: {
            showArchived: false,
            priorityFilter: PriorityLevel.P1,
            assigneeFilter: null
        },
        isDefault: true,
        icon: 'alert-triangle',
        order: 2
    },
    {
        id: 'archived',
        name: 'Archived',
        filters: {
            showArchived: true,
            priorityFilter: null,
            assigneeFilter: null
        },
        isDefault: true,
        icon: 'archive',
        order: 3
    }
];

/**
 * Creates the filter slice for the Kanban store.
 * This slice manages filter-related state and actions.
 * 
 * @param set - Zustand set function
 * @param get - Zustand get function
 * @returns Filter slice object with state and actions
 */
export const createFilterSlice: StateCreator<
    TeamKanbanStore,
    [],
    [],
    {
        filters: TeamKanbanStore['filters'];
        setShowArchived: TeamKanbanStore['setShowArchived'];
        setPriorityFilter: TeamKanbanStore['setPriorityFilter'];
        setAssigneeFilter: TeamKanbanStore['setAssigneeFilter'];
        resetFilters: TeamKanbanStore['resetFilters'];
        savePreset: TeamKanbanStore['savePreset'];
        updatePreset: TeamKanbanStore['updatePreset'];
        deletePreset: TeamKanbanStore['deletePreset'];
        applyPreset: TeamKanbanStore['applyPreset'];
        clearActivePreset: TeamKanbanStore['clearActivePreset'];
    }
> = (set, get) => {
    // Load saved presets from localStorage if available
    const loadSavedPresets = (): FilterPreset[] => {
        try {
            const savedPresets = localStorage.getItem('kanban-filter-presets');
            const userPresets = savedPresets ? JSON.parse(savedPresets) : [];

            // Always include the default presets
            return [
                ...DEFAULT_PRESETS,
                ...userPresets.filter((p: FilterPreset) => !DEFAULT_PRESETS.some(dp => dp.id === p.id))
            ].sort((a, b) => (a.order || 0) - (b.order || 0));
        } catch (error) {
            console.error('Error loading saved presets:', error);
            return DEFAULT_PRESETS;
        }
    };

    // Save presets to localStorage
    const savePresetsToStorage = (presets: FilterPreset[]) => {
        try {
            // Only save user-created presets
            const userPresets = presets.filter(p => !p.isDefault);
            localStorage.setItem('kanban-filter-presets', JSON.stringify(userPresets));
        } catch (error) {
            console.error('Error saving presets:', error);
        }
    };

    return {
        // Initial filter state
        filters: {
            showArchived: false,
            priorityFilter: null,
            assigneeFilter: null,
            presets: loadSavedPresets(),
            activePresetId: null
        },

        // Filter actions
        setShowArchived: (show: boolean) => {
            set((state) => {
                // When manually changing filters, clear the active preset
                const newState = {
                    filters: {
                        ...state.filters,
                        showArchived: show,
                        activePresetId: null
                    },
                };
                return newState;
            });
        },

        setPriorityFilter: (priority: PriorityLevel | null) => {
            set((state) => ({
                filters: {
                    ...state.filters,
                    priorityFilter: priority,
                    activePresetId: null
                },
            }));
        },

        setAssigneeFilter: (assigneeId: string | null) => {
            set((state) => ({
                filters: {
                    ...state.filters,
                    assigneeFilter: assigneeId,
                    activePresetId: null
                },
            }));
        },

        resetFilters: () => {
            set((state) => ({
                filters: {
                    ...state.filters,
                    showArchived: false,
                    priorityFilter: null,
                    assigneeFilter: null,
                    activePresetId: null
                },
            }));
        },

        savePreset: (name: string, icon?: string) => {
            const state = get();
            const id = generateId();

            const newPreset: FilterPreset = {
                id,
                name,
                filters: {
                    showArchived: state.filters.showArchived,
                    priorityFilter: state.filters.priorityFilter,
                    assigneeFilter: state.filters.assigneeFilter
                },
                icon,
                order: state.filters.presets.length
            };

            set((state) => {
                const updatedPresets = [...state.filters.presets, newPreset];
                savePresetsToStorage(updatedPresets);

                return {
                    filters: {
                        ...state.filters,
                        presets: updatedPresets,
                        activePresetId: id
                    }
                };
            });

            return id;
        },

        updatePreset: (id: string, updates: Partial<FilterPreset>) => {
            set((state) => {
                const presetIndex = state.filters.presets.findIndex(p => p.id === id);
                if (presetIndex === -1) return state;

                // Don't allow modifying isDefault status of default presets
                if (state.filters.presets[presetIndex].isDefault) {
                    delete updates.isDefault;
                }

                const updatedPresets = [...state.filters.presets];
                updatedPresets[presetIndex] = {
                    ...updatedPresets[presetIndex],
                    ...updates
                };

                // If not a default preset, save to localStorage
                if (!updatedPresets[presetIndex].isDefault) {
                    savePresetsToStorage(updatedPresets);
                }

                return {
                    filters: {
                        ...state.filters,
                        presets: updatedPresets
                    }
                };
            });
        },

        deletePreset: (id: string) => {
            set((state) => {
                // Don't allow deleting default presets
                const preset = state.filters.presets.find(p => p.id === id);
                if (preset?.isDefault) return state;

                const updatedPresets = state.filters.presets.filter(p => p.id !== id);
                savePresetsToStorage(updatedPresets);

                return {
                    filters: {
                        ...state.filters,
                        presets: updatedPresets,
                        // Clear active preset if it was deleted
                        activePresetId: state.filters.activePresetId === id ? null : state.filters.activePresetId
                    }
                };
            });
        },

        applyPreset: (id: string) => {
            set((state) => {
                const preset = state.filters.presets.find(p => p.id === id);
                if (!preset) return state;

                // Special handling for "My Tasks" preset
                const assigneeFilter = preset.filters.assigneeFilter;

                // For the "My Tasks" preset, try to get the current user ID
                // We don't have direct access to the current user in the store
                // So for this preset, the assignee will need to be set by the component
                if (id === 'my-tasks') {
                    console.log('Applying "My Tasks" preset - current user ID will be set by component');
                }

                return {
                    filters: {
                        ...state.filters,
                        showArchived: preset.filters.showArchived,
                        priorityFilter: preset.filters.priorityFilter,
                        assigneeFilter,
                        activePresetId: id
                    }
                };
            });
        },

        clearActivePreset: () => {
            set((state) => ({
                filters: {
                    ...state.filters,
                    activePresetId: null
                }
            }));
        }
    };
}; 