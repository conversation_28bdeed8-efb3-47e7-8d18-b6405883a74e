import { FilterPreset, PriorityLevel } from '../../../types';

/**
 * State interface for the filters slice
 */
export interface FilterState {
    /** Show archived cards */
    showArchived: boolean;

    /** Filter by priority level */
    priorityFilter: PriorityLevel | null;

    /** Filter by assignee ID */
    assigneeFilter: string | null;

    /** Available filter presets */
    presets: FilterPreset[];

    /** Currently active preset ID */
    activePresetId: string | null;
}

/**
 * Actions interface for the filters slice
 */
export interface FilterActions {
    /** Set show archived filter */
    setShowArchived: (show: boolean) => void;

    /** Set priority filter */
    setPriorityFilter: (priority: PriorityLevel | null) => void;

    /** Set assignee filter */
    setAssigneeFilter: (assigneeId: string | null) => void;

    /** Reset all filters to default values */
    resetFilters: () => void;

    /** Save current filters as a preset */
    savePreset: (name: string, icon?: string) => string;

    /** Update an existing preset */
    updatePreset: (id: string, updates: Partial<FilterPreset>) => void;

    /** Delete a preset */
    deletePreset: (id: string) => void;

    /** Apply a preset */
    applyPreset: (id: string) => void;

    /** Clear the active preset */
    clearActivePreset: () => void;
}

/**
 * Combined interface for the filters slice
 */
export interface FilterSlice extends FilterState, FilterActions { } 