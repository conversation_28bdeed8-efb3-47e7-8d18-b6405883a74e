import { TeamKanbanStore } from '../../../types';

/**
 * Selectors for the filters slice
 * These selectors help extract and derive state from the Kanban store
 */
export const filterSelectors = {
    /**
     * Select the entire filters object
     */
    selectFilters: (state: TeamKanbanStore) => state.filters,

    /**
     * Select the showArchived filter
     */
    selectShowArchived: (state: TeamKanbanStore) => state.filters.showArchived,

    /**
     * Select the priority filter
     */
    selectPriorityFilter: (state: TeamKanbanStore) => state.filters.priorityFilter,

    /**
     * Select the assignee filter
     */
    selectAssigneeFilter: (state: TeamKanbanStore) => state.filters.assigneeFilter,

    /**
     * Select all available filter presets
     */
    selectPresets: (state: TeamKanbanStore) => state.filters.presets || [],

    /**
     * Select the currently active preset ID
     */
    selectActivePresetId: (state: TeamKanbanStore) => state.filters.activePresetId,

    /**
     * Select the currently active preset
     */
    selectActivePreset: (state: TeamKanbanStore) => {
        const activeId = state.filters.activePresetId;
        if (!activeId) return null;
        return state.filters.presets?.find(preset => preset.id === activeId) || null;
    },

    /**
     * Select default presets
     */
    selectDefaultPresets: (state: TeamKanbanStore) =>
        state.filters.presets?.filter(preset => preset.isDefault) || [],

    /**
     * Select user-created presets
     */
    selectUserPresets: (state: TeamKanbanStore) =>
        state.filters.presets?.filter(preset => !preset.isDefault) || [],

    /**
     * Select filtered cards for a column
     * 
     * @param columnId - The column ID to get cards for
     * @returns A selector function that returns filtered cards
     */
    selectFilteredColumnCards: (columnId: string) => (state: TeamKanbanStore) => {
        const columnData = state.columnData.get(columnId);
        if (!columnData) return [];

        let cards = columnData.cards;

        // Filter by archived status based on the column-specific setting
        // instead of the global filter
        const isShowingArchived = state.showArchivedColumns.has(columnId);
        if (!isShowingArchived) {
            cards = cards.filter(card => !card.archived_at);
        }

        // Apply priority filter
        if (state.filters.priorityFilter !== null) {
            cards = cards.filter(card => card.priority === state.filters.priorityFilter);
        }

        // Apply assignee filter
        if (state.filters.assigneeFilter !== null) {
            if (state.filters.assigneeFilter === 'unassigned') {
                cards = cards.filter(card => !card.assignee);
            } else {
                cards = cards.filter(card => card.assignee?.id === state.filters.assigneeFilter);
            }
        }

        return cards;
    }
}; 