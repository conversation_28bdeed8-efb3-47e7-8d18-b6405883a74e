/**
 * Filters slice for the Kanban board
 * 
 * This module exports the filter state structure and actions for filtering cards
 * in the Kanban board. It includes:
 * - Filter state (show archived, priority filter, assignee filter)
 * - Filter actions (set/reset filters)
 * - Selectors to access filtered data
 */

import { createFilterSlice } from './actions';
import { filterSelectors } from './selectors';

export { createFilterSlice, filterSelectors }; 