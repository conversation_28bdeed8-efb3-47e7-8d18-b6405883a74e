// import { StateCreator } from 'zustand';
import { TeamKanbanStore } from '../../../types';
import { CommentActions, CommentState } from './types';
import { commentApi } from '../../../api/commentApi';
import { supabase } from '@/lib/supabase';

/**
 * Type definition for setState function provided by Zustand
 * Used to update the comment slice state
 */
type SetState = (
    partial: CommentState | Partial<CommentState> | ((state: CommentState) => CommentState | Partial<CommentState>),
    replace?: boolean
) => void;

/**
 * Type definition for getState function provided by <PERSON>ustand
 * Used to access the full store state
 */
type GetState = () => TeamKanbanStore;

/**
 * Creates the actions for the comment slice
 * Implements all comment-related operations including fetching, adding, updating, and deleting comments
 * 
 * @param set - Zustand's setState function
 * @param get - Zustand's getState function
 * @returns {CommentActions} Object containing all comment actions
 */
export function createCommentActions(
    set: SetState,
    get: GetState
): CommentActions {
    return {
        /**
         * Fetches comments for a specific card
         * Supports pagination and updates the state with new comments
         * 
         * @param cardId - The ID of the card to fetch comments for
         * @param page - Optional page number for pagination (default: 1)
         * @param limit - Optional limit of comments per page (default: 10)
         * @returns Promise with the total count of comments
         */
        fetchComments: async (cardId: string, page: number = 1, limit: number = 10) => {
            try {
                const state = get() as CommentState;

                // Set loading state for this card
                set({
                    loadingComments: {
                        ...state.loadingComments,
                        [cardId]: true
                    }
                });

                // Fetch comments from API
                const { comments, count } = await commentApi.fetchComments(cardId, page, limit);

                // Since API now returns newest first (ascending: false), we need to reverse
                // the order to display oldest first for chat-like behavior
                const orderedComments = [...comments].reverse();

                // Update state with new comments
                set({
                    comments: [...state.comments, ...orderedComments.filter(c => !state.comments.some(sc => sc.id === c.id))],
                    commentPages: {
                        ...state.commentPages,
                        [cardId]: page === 1
                            ? orderedComments
                            : [...orderedComments, ...(state.commentPages[cardId] || [])] // Put older comments at the beginning
                    },
                    hasMoreComments: {
                        ...state.hasMoreComments,
                        [cardId]: comments.length === limit
                    },
                    loadingComments: {
                        ...state.loadingComments,
                        [cardId]: false
                    }
                });

                return { count: count || 0 };
            } catch (error) {
                const state = get() as CommentState;

                set({
                    loadingComments: {
                        ...state.loadingComments,
                        [cardId]: false
                    }
                });
                throw error;
            }
        },

        /**
         * Adds a new comment to a card
         * Updates both comment slice and card slice
         * 
         * @param cardId - The ID of the card to add the comment to
         * @param content - The content of the comment
         * @param isSystem - Whether this is a system-generated comment (default: false)
         * @returns Promise with the created comment
         */
        addComment: async (cardId: string, content: string, isSystem: boolean = false) => {
            try {
                const { data: { user } } = await supabase.auth.getUser();
                if (!user) throw new Error('No authenticated user');

                // Add comment in database
                const newComment = isSystem
                    ? await commentApi.addSystemComment(cardId, content, user.id)
                    : await commentApi.addComment(cardId, content, user.id);
                    
                const state = get() as CommentState;

                // Update comments array and comment pages in comment slice
                const updatedComments = [...(state.comments || []), newComment];

                // Get existing comments for this card
                const cardComments = state.commentPages[cardId] || [];

                // Append the new comment at the end (oldest first, newest last)
                // Make sure we're not adding duplicates
                const updatedPages = [
                    ...cardComments.filter(comment => comment.id !== newComment.id),
                    newComment
                ];

                // Update state with new comment
                set({
                    comments: updatedComments,
                    commentPages: {
                        ...state.commentPages,
                        [cardId]: updatedPages
                    }
                });

                // Update card slice to reflect new comment
                // This is necessary because we're accessing card slice state from the comment slice
                type CardState = {
                    cards: {
                        id: string;
                        comments: Array<unknown>;
                    }[];
                };

                (set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void)(
                    (state: TeamKanbanStore & CardState) => {
                        // Only proceed if cards array exists
                        if (!state.cards) return {};

                        // Update cards array to include the new comment
                        const updatedCards = state.cards.map((c) => {
                            if (c.id === cardId) {
                                return {
                                    ...c,
                                    comments: [...(c.comments || []), newComment]
                                };
                            }
                            return c;
                        });

                        return { cards: updatedCards };
                    }
                );

                return newComment;
            } catch (error) {
                // Use properly typed error setter
                (set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void)(

                    (_state: TeamKanbanStore) => ({
                        error: error instanceof Error ? error.message : 'Failed to add comment'
                    })
                );
                throw error;
            }
        },

        /**
         * Adds a system-generated comment to a card
         * This is a convenience method that calls addComment with isSystem=true
         * 
         * @param cardId - The ID of the card to add the comment to
         * @param content - The system message content
         * @returns Promise with the created comment
         */
        addSystemComment: async (cardId: string, content: string) => {
            // Cast the Store to any to avoid strict type checking
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const store = get() as any;
            return store.addComment(cardId, content, true);
        },

        /**
         * Updates an existing comment
         * Updates both comment slice and card slice
         * 
         * @param commentId - The ID of the comment to update
         * @param content - The new content for the comment
         * @returns Promise with the updated comment
         */
        updateComment: async (commentId: string, content: string) => {
            try {
                // Update comment in database
                const updatedComment = await commentApi.updateComment(commentId, content);
                const state = get() as CommentState;

                // Update comments array in comment slice
                const comments = (state.comments || []).map(comment =>
                    comment.id === commentId ? updatedComment : comment
                );

                // Update comment pages in comment slice
                const commentPages = { ...state.commentPages };

                // Update the comment in all cards' comment pages
                Object.keys(commentPages).forEach(cardId => {
                    // Make sure we correctly update each comment by id
                    commentPages[cardId] = commentPages[cardId].map(comment =>
                        comment.id === commentId ? updatedComment : comment
                    );
                });

                // Update the state
                set({ comments, commentPages });

                // Update card slice to reflect updated comment
                // This is necessary because we're accessing card slice state from the comment slice
                type CardState = {
                    cards: {
                        id: string;
                        comments: Array<unknown>;
                    }[];
                };

                (set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void)(
                    (state: TeamKanbanStore & CardState) => {
                        // Only proceed if cards array exists
                        if (!state.cards) return {};

                        // Find the card that contains this comment
                        const updatedCards = state.cards.map((c) => {
                            if (c.comments && c.comments.some((comment) => comment.id === commentId)) {
                                return {
                                    ...c,
                                    comments: c.comments.map(comment =>
                                        comment.id === commentId ? updatedComment : comment
                                    )
                                };
                            }
                            return c;
                        });

                        return { cards: updatedCards };
                    }
                );

                return updatedComment;
            } catch (error) {
                // Use properly typed error setter
                (set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void)(

                    (_state: TeamKanbanStore) => ({
                        error: error instanceof Error ? error.message : 'Failed to update comment'
                    })
                );
                throw error;
            }
        },

        /**
         * Deletes a comment
         * Updates both comment slice and card slice
         * 
         * @param commentId - The ID of the comment to delete
         * @returns Promise with the deleted comment
         */
        deleteComment: async (commentId: string) => {
            try {
                // Find the comment to delete to get its card_id
                const state = get() as CommentState;
                const commentToDelete = state.comments.find(c => c.id === commentId);
                if (!commentToDelete) throw new Error('Comment not found');

                // Delete comment in database (which marks it as deleted rather than removing it)
                // We intentionally ignore the return value since we're doing optimistic updates
                await commentApi.deleteComment(commentId);

                // Create a comment object from the one we found earlier for consistency
                // since the API actually returns a boolean instead of the updated comment
                const deletedComment = {
                    ...commentToDelete,
                    deleted_at: new Date().toISOString(),
                    content: '[deleted]'
                };

                // Update comments array in comment slice - update the comment rather than removing it
                const comments = (state.comments || []).map(comment =>
                    comment.id === commentId ? deletedComment : comment
                );

                // Update comment pages in comment slice - update the comment rather than removing it
                const commentPages = { ...state.commentPages };

                // Update the deleted comment in all cards' comment pages
                Object.keys(commentPages).forEach(cardId => {
                    // Update the comment rather than filtering it out
                    commentPages[cardId] = commentPages[cardId].map(comment =>
                        comment.id === commentId ? deletedComment : comment
                    );
                });

                // Update the state
                set({ comments, commentPages });

                // Update card slice to reflect deleted comment
                // This is necessary because we're accessing card slice state from the comment slice
                type CardState = {
                    cards: {
                        id: string;
                        comments: Array<unknown>;
                    }[];
                };

                (set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void)(
                    (state: TeamKanbanStore & CardState) => {
                        // Only proceed if cards array exists
                        if (!state.cards) return {};

                        // Find the card that contains this comment
                        const updatedCards = state.cards.map((c) => {
                            if (c.comments && c.comments.some((comment) => comment.id === commentId)) {
                                return {
                                    ...c,
                                    comments: c.comments.map(comment =>
                                        comment.id === commentId ? deletedComment : comment
                                    )
                                };
                            }
                            return c;
                        });

                        return { cards: updatedCards };
                    }
                );

                return deletedComment;
            } catch (error) {
                // Use properly typed error setter
                (set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void)(

                    (_state: TeamKanbanStore) => ({
                        error: error instanceof Error ? error.message : 'Failed to delete comment'
                    })
                );
                throw error;
            }
        }
    };
}