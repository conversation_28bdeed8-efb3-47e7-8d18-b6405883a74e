import { CommentState } from './types';

/**
 * Initial state for the comment slice
 * Initializes empty collections and default values for all state properties
 * 
 * @type {CommentState}
 */
export const initialCommentState: CommentState = {
    /** Empty array of comments */
    comments: [],

    /** Empty record of comment pages by card */
    commentPages: {},

    /** Empty record of hasMore flags by card */
    hasMoreComments: {},

    /** Empty record of loading states by card */
    loadingComments: {}
}; 