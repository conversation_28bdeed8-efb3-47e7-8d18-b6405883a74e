import { StateCreator } from 'zustand';
import { TeamKanbanStore } from '../../../types';
import { CommentSlice } from './types';
import { initialCommentState } from './state';
import { createCommentActions } from './actions';

/**
 * Creates the comment slice for the Kanban store
 * Combines the initial state with comment actions
 * 
 * @param set - Zustand's setState function
 * @param get - Zustand's getState function
 * @returns {CommentSlice} Combined comment state and actions
 */
export const createCommentSlice: StateCreator<TeamKanbanStore, [], [], CommentSlice> = (set, get) => ({
    ...initialCommentState,
    ...createCommentActions(set, get)
}); 