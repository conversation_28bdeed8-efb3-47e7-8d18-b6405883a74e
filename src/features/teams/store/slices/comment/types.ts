import { TeamKanbanComment } from '../../../types';

/**
 * State interface for the comment slice
 * @interface CommentState
 */
export interface CommentState {
    /** List of all comments */
    comments: TeamKanbanComment[];

    /** Map of comment pages by card ID */
    commentPages: Record<string, TeamKanbanComment[]>;

    /** Map of whether more comments can be loaded by card ID */
    hasMoreComments: Record<string, boolean>;

    /** Map of loading states by card ID */
    loadingComments: Record<string, boolean>;
}

/**
 * Actions interface for the comment slice
 * @interface CommentActions
 */
export interface CommentActions {
    /**
     * Fetches comments for a specific card
     * @param cardId - The ID of the card to fetch comments for
     * @param page - Optional page number for pagination
     * @param limit - Optional limit of comments per page
     * @returns Promise with the total count of comments
     */
    fetchComments: (
        cardId: string,
        page?: number,
        limit?: number
    ) => Promise<{ count: number }>;

    /**
     * Adds a new comment to a card
     * @param cardId - The ID of the card to add the comment to
     * @param content - The content of the comment
     * @param isSystem - Whether this is a system-generated comment
     * @returns Promise with the created comment
     */
    addComment: (
        cardId: string,
        content: string,
        isSystem?: boolean
    ) => Promise<TeamKanbanComment>;

    /**
     * Adds a system-generated comment to a card
     * @param cardId - The ID of the card to add the comment to
     * @param content - The system message content
     * @returns Promise with the created comment
     */
    addSystemComment: (
        cardId: string,
        content: string
    ) => Promise<TeamKanbanComment>;

    /**
     * Updates an existing comment
     * @param commentId - The ID of the comment to update
     * @param content - The new content for the comment
     * @returns Promise with the updated comment
     */
    updateComment: (
        commentId: string,
        content: string
    ) => Promise<TeamKanbanComment>;

    /**
     * Deletes a comment
     * @param commentId - The ID of the comment to delete
     * @returns Promise with the deleted comment
     */
    deleteComment: (commentId: string) => Promise<TeamKanbanComment>;
}

/**
 * Combined interface for the comment slice
 * @interface CommentSlice
 * @extends CommentState
 * @extends CommentActions
 */
export interface CommentSlice extends CommentState, CommentActions { }