import { StateCreator } from 'zustand';
import { BoardSlice, TeamKanbanStore } from '../../types';
import { boardApi } from '../../api/boardApi';
import { logger } from '@/utils/logger';

export const createBoardSlice: StateCreator<
    TeamKanbanStore,
    [],
    [],
    BoardSlice
> = (set, get) => ({
    // State
    columns: [],
    columnData: new Map(),
    showArchivedColumns: new Set(),
    loading: false,
    error: null,

    // Actions
    fetchBoard: async (teamId: string) => {
        try {
            set({ loading: true, error: null });
            const columns = await boardApi.fetchColumns(teamId);

            // Initialize columnData Map with empty arrays
            const newColumnData = new Map();
            columns.forEach(column => {
                newColumnData.set(column.id, {
                    cards: [],
                    loading: false,
                    error: null,
                    hasMore: true,
                    currentPage: 0
                });
            });

            set({
                columns,
                columnData: newColumnData,
                loading: false
            });

            // Fetch initial cards for each column
            await Promise.all(columns.map(column => {
                // Check if the column is set to show archived
                const isShowingArchived = get().showArchivedColumns.has(column.id);
                return get().fetchColumnCards(column.id, 1, undefined, isShowingArchived);
            }));
        } catch (error) {
            set({
                loading: false,
                error: error instanceof Error ? error.message : 'Failed to fetch board'
            });
        }
    },

    clearBoard: () => {
        set({
            columns: [],
            columnData: new Map(),
            showArchivedColumns: new Set(),
            loading: false,
            error: null
        });
    },

    addColumn: async (teamId: string, title: string) => {
        try {
            const columns = get().columns;
            const lastOrderIndex = columns.length > 0
                ? columns[columns.length - 1].order_index
                : 0;

            const newColumn = await boardApi.addColumn(teamId, title, lastOrderIndex + 1024);

            set(state => ({
                columns: [...state.columns, newColumn],
                columnData: new Map(state.columnData).set(newColumn.id, {
                    cards: [],
                    loading: false,
                    error: null,
                    hasMore: true,
                    currentPage: 0
                })
            }));
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to add column'
            });
        }
    },

    updateColumn: async (columnId: string, title: string) => {
        try {
            const updatedColumn = await boardApi.updateColumn(columnId, title);

            set(state => ({
                columns: state.columns.map(col =>
                    col.id === columnId ? updatedColumn : col
                )
            }));
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to update column'
            });
        }
    },

    deleteColumn: async (columnId: string) => {
        try {
            await boardApi.deleteColumn(columnId);

            set(state => {
                const newColumnData = new Map(state.columnData);
                newColumnData.delete(columnId);

                return {
                    columns: state.columns.filter(col => col.id !== columnId),
                    columnData: newColumnData
                };
            });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to delete column'
            });
        }
    },

    reorderColumns: async (teamId: string, columnIds: string[]) => {
        try {
            const updates = columnIds.map((id, index) => ({
                id,
                order_index: index * 1024
            }));

            await boardApi.reorderColumns(teamId, updates);

            set(state => ({
                columns: state.columns
                    .map(col => ({
                        ...col,
                        order_index: columnIds.indexOf(col.id) * 1024
                    }))
                    .sort((a, b) => a.order_index - b.order_index)
            }));
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to reorder columns'
            });
        }
    },

    toggleColumnArchived: async (columnId: string) => {
        try {
            // Update the state with the new archives visibility value
            set(state => {
                const newShowArchivedColumns = new Set(state.showArchivedColumns);
                const shouldShowArchived = !newShowArchivedColumns.has(columnId);

                if (shouldShowArchived) {
                    // Switch to showing archived cards
                    newShowArchivedColumns.add(columnId);
                    logger.debug(`Added column ${columnId} to archived columns set`);
                } else {
                    // Switch to showing active cards
                    newShowArchivedColumns.delete(columnId);
                    logger.debug(`Removed column ${columnId} from archived columns set`);
                }

                // Reset column data to trigger a fresh fetch
                const columnData = state.columnData.get(columnId) || {
                    cards: [],
                    loading: false,
                    error: null,
                    hasMore: true,
                    currentPage: 0
                };

                const newColumnData = new Map(state.columnData);
                newColumnData.set(columnId, {
                    ...columnData,
                    cards: [], // Clear existing cards
                    loading: true, // Set loading to true immediately
                    currentPage: 0,
                    hasMore: true,
                    error: null // Clear any previous errors
                });

                logger.debug('toggleColumnArchived - Updated state:', {
                    columnId,
                    shouldShowArchived,
                    showArchivedColumns: Array.from(newShowArchivedColumns)
                });

                return {
                    showArchivedColumns: newShowArchivedColumns,
                    columnData: newColumnData
                };
            });

            // Get the updated state
            const updatedState = get();
            const updatedShowArchived = updatedState.showArchivedColumns.has(columnId);

            // Fetch cards with the new archived state - single API call
            const result = await get().fetchColumnCards(columnId, 1, 20, updatedShowArchived);

            logger.debug('toggleColumnArchived - Fetch completed successfully:', {
                columnId,
                cardsCount: result?.count || 0,
                showArchived: updatedShowArchived
            });

            return result;
        } catch (error) {
            logger.error('toggleColumnArchived - Error fetching cards:', error);

            // Update state to show error and reset loading
            set(state => {
                const columnData = state.columnData.get(columnId) || {
                    cards: [],
                    loading: false,
                    error: null,
                    hasMore: true,
                    currentPage: 0
                };

                const newColumnData = new Map(state.columnData);
                newColumnData.set(columnId, {
                    ...columnData,
                    loading: false,
                    error: error instanceof Error ? error.message : 'Failed to load cards'
                });

                return { columnData: newColumnData };
            });

            throw error;
        }
    }
});

