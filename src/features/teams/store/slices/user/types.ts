import { User } from '../../../types';

export interface UserState {
    users: User[];
    teamMembers: Record<string, User[]>;
    loadingTeamMembers: Record<string, boolean>;
}

export interface UserActions {
    updateUsers: () => Promise<void>;
    assignUser: (userId: string, cardId: string) => Promise<void>;
    unassignUser: (cardId: string) => Promise<void>;
    fetchTeamMembers: (teamId: string) => Promise<User[]>;
}

export interface UserSlice extends UserState, UserActions { } 