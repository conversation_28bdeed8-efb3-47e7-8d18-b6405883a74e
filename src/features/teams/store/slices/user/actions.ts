import { TeamKanbanStore } from '../../../types';
import { UserActions, UserState } from './types';
import { userApi } from '../../../api/userApi';

type SetState = (
    partial: UserState | Partial<UserState> | ((state: UserState) => UserState | Partial<UserState>),
    replace?: boolean
) => void;

export const createUserActions = (
    set: SetState,
    get: () => TeamKanbanStore
): UserActions => ({
    updateUsers: async () => {
        const users = await userApi.fetchUsers();
        set({ users });
    },

    assignUser: async (userId: string, cardId: string) => {
        await userApi.assignUserToCard(userId, cardId);
        // Note: Card state will be updated through the card slice
    },

    unassignUser: async (cardId: string) => {
        await userApi.unassignUserFromCard(cardId);
        // Note: Card state will be updated through the card slice
    },

    // Add function to fetch and cache team members
    fetchTeamMembers: async (teamId: string) => {
        try {
            // Get the current state
            const state = get();

            // Check if we already have cached data for this team
            // and it's not currently being loaded
            if (state.teamMembers[teamId] && !state.loadingTeamMembers[teamId]) {
                return state.teamMembers[teamId];
            }

            // Set loading state
            set({
                loadingTeamMembers: {
                    ...state.loadingTeamMembers,
                    [teamId]: true
                }
            });

            // Fetch team members from the API
            const members = await userApi.fetchTeamMembers(teamId);

            // Update the cache
            set({
                teamMembers: {
                    ...state.teamMembers,
                    [teamId]: members
                },
                loadingTeamMembers: {
                    ...state.loadingTeamMembers,
                    [teamId]: false
                }
            });

            // Return the fetched members
            return members;
        } catch (error) {
            // Update loading state on error
            const state = get();
            set({
                loadingTeamMembers: {
                    ...state.loadingTeamMembers,
                    [teamId]: false
                }
            });

            throw error;
        }
    }
});