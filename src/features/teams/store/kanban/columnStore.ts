import { create } from 'zustand';
import { supabase } from '@/lib/supabase';
import type { TeamKanbanColumn } from '@/features/teams/types/kanban';

interface KanbanColumnState {
    columns: TeamKanbanColumn[];
    loading: boolean;
    error: string | null;

    // Actions
    loadColumns: (teamId: string) => Promise<void>;
    addColumn: (teamId: string, title: string) => Promise<void>;
    updateColumn: (columnId: string, title: string) => Promise<void>;
    deleteColumn: (columnId: string) => Promise<void>;
    reorderColumns: (teamId: string, columnIds: string[]) => Promise<void>;
    setError: (error: string | null) => void;
    clearColumns: () => void;
    setColumns: (columns: TeamKanbanColumn[]) => void;
}

export const useKanbanColumnStore = create<KanbanColumnState>((set, get) => ({
    columns: [],
    loading: false,
    error: null,

    loadColumns: async (teamId: string) => {
        try {
            set({ loading: true, error: null });

            const { data, error } = await supabase
                .from('kanban_columns')
                .select('*')
                .eq('team_id', teamId)
                .order('order_index');

            if (error) throw error;

            set({ columns: data || [], loading: false });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to load columns',
                loading: false
            });
        }
    },

    addColumn: async (teamId: string, title: string) => {
        try {
            set({ error: null });
            const currentColumns = get().columns;
            const newOrderIndex = currentColumns.length;

            const { data, error } = await supabase
                .from('kanban_columns')
                .insert({
                    team_id: teamId,
                    title,
                    order_index: newOrderIndex
                })
                .select()
                .single();

            if (error) throw error;

            set(state => ({
                columns: [...state.columns, data]
            }));
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to add column'
            });
        }
    },

    updateColumn: async (columnId: string, title: string) => {
        try {
            set({ error: null });

            const { error } = await supabase
                .from('kanban_columns')
                .update({ title })
                .eq('id', columnId)
                .select()
                .single();

            if (error) throw error;

            set(state => ({
                columns: state.columns.map(col =>
                    col.id === columnId ? { ...col, title } : col
                )
            }));
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to update column'
            });
        }
    },

    deleteColumn: async (columnId: string) => {
        try {
            set({ error: null });

            const { error } = await supabase
                .from('kanban_columns')
                .delete()
                .eq('id', columnId);

            if (error) throw error;

            set(state => ({
                columns: state.columns.filter(col => col.id !== columnId)
            }));
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to delete column'
            });
        }
    },

    reorderColumns: async (_teamId: string, columnIds: string[]) => {
        try {
            set({ error: null });
            const updates = columnIds.map((id, index) => ({
                id,
                order_index: index
            }));

            const { error } = await supabase
                .from('kanban_columns')
                .upsert(updates);

            if (error) throw error;

            const reorderedColumns = columnIds.map((id, index) => {
                const column = get().columns.find(col => col.id === id);
                return column ? { ...column, order_index: index } : null;
            }).filter((col): col is TeamKanbanColumn => col !== null);

            set({ columns: reorderedColumns });
        } catch (error) {
            set({
                error: error instanceof Error ? error.message : 'Failed to reorder columns'
            });
        }
    },

    setError: (error: string | null) => set({ error }),

    clearColumns: () => set({ columns: [], error: null }),

    setColumns: (columns: TeamKanbanColumn[]) => set({ columns })
})); 