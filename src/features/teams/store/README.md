# Team Kanban Store

A modular Zustand store for managing Kanban board state with Supabase integration.

## Features

- 🔄 Real-time updates with Supabase
- 📦 Modular slice-based architecture
- 🎯 Type-safe state management
- 🔍 Optimized performance with selective updates
- 📝 Comprehensive error handling
- 🔄 Built-in pagination support

## Architecture

The store is divided into four main slices:

```
store/
├── slices/
│   ├── board/        # Board layout and columns
│   ├── card/         # Cards and subtasks
│   ├── comment/      # Card comments
│   └── user/         # Users and assignments
└── teamKanbanStore.ts
```

## Usage Examples

### Initialize Store

```typescript
import { useTeamKanbanStore } from './store/teamKanbanStore';

// Initialize store for a team
const store = useTeamKanbanStore();
await store.initializeStore('team-123');

// Clean up when unmounting
store.cleanup();
```

### Board Operations

```typescript
// Fetch board data
await store.fetchBoard('team-123');

// Add a new column
await store.addColumn('team-123', 'In Progress');

// Reorder columns
await store.reorderColumns('team-123', ['col-1', 'col-2', 'col-3']);
```

### Card Operations

```typescript
// Fetch cards for a column
await store.fetchColumnCards('column-123');

// Add a new card
await store.addCard('column-123', 'team-123', 'New Task', 'Task description');

// Move a card
await store.moveCard('card-123', 'column-456', 2);

// Update a card
await store.updateCard('card-123', {
    title: 'Updated Task',
    description: 'New description',
    priority: 'P1'
});
```

### Comment Operations

```typescript
// Fetch comments for a card
await store.fetchComments('card-123');

// Add a comment
await store.addComment('card-123', 'This is a comment');

// Update a comment
await store.updateComment('comment-123', 'Updated comment');
```

### User Operations

```typescript
// Update users list
await store.updateUsers();

// Assign user to card
await store.assignUser('user-123', 'card-456');

// Unassign user from card
await store.unassignUser('card-456');
```

### Using Selectors

```typescript
import { kanbanSelectors } from './store/teamKanbanStore';

// Select all columns
const columns = useKanbanStore(kanbanSelectors.selectColumns);

// Select cards in a column
const columnCards = useKanbanStore(kanbanSelectors.selectColumnCards('column-123'));

// Select a specific card
const card = useKanbanStore(kanbanSelectors.selectCardById('card-123'));

// Select comments for a card
const comments = useKanbanStore(kanbanSelectors.selectCardComments('card-123'));
```

## Error Handling

The store includes comprehensive error handling:

```typescript
try {
    await store.addCard('column-123', 'team-123', 'New Task');
} catch (error) {
    console.error('Failed to add card:', error);
}

// Or use the store's error state
const error = useKanbanStore(state => state.error);
if (error) {
    // Handle error
}
```

## Performance Optimization

1. Use selectors for efficient re-renders
2. Implement proper memoization
3. Use pagination for large datasets
4. Cache results where appropriate

## Type Safety

The store is fully typed with TypeScript:

```typescript
import { TeamKanbanStore } from './types';

// All state and actions are type-safe
const store: TeamKanbanStore = useTeamKanbanStore();
```

## Testing

Run tests with:

```bash
npm test
```

Test files are located alongside their corresponding components:

```
__tests__/
├── api/
│   ├── boardApi.test.ts
│   ├── cardApi.test.ts
│   ├── commentApi.test.ts
│   └── userApi.test.ts
├── store/
│   └── slices/
│       ├── board/
│       ├── card/
│       ├── comment/
│       └── user/
└── utils/
    └── transformers.test.ts
``` 