import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createBoardSlice } from '../../../../store/slices/board';
import { boardApi } from '../../../../api/boardApi';
import { TeamKanbanColumn } from '../../../../types';
import { StoreApi } from 'zustand';

// Mock the board API
vi.mock('../../../../api/boardApi', () => ({
    boardApi: {
        fetchColumns: vi.fn(),
        addColumn: vi.fn(),
        updateColumn: vi.fn(),
        deleteColumn: vi.fn(),
        reorderColumns: vi.fn()
    }
}));

describe('Board Slice', () => {
    const mockColumn: TeamKanbanColumn = {
        id: '123',
        team_id: '456',
        title: 'Test Column',
        order_index: 1000,
        created_at: '2024-02-24T12:00:00Z'
    };

    let set: any;
    let get: any;
    let store: ReturnType<typeof createBoardSlice>;
    let initialState: any;

    beforeEach(() => {
        set = vi.fn();
        get = vi.fn();
        const mockStoreApi: StoreApi<any> = {
            setState: set,
            getState: get,
            subscribe: vi.fn(),
            destroy: vi.fn(),
            getInitialState: vi.fn()
        };
        store = createBoardSlice(set, get, mockStoreApi);

        // Initialize state for each test
        initialState = {
            columns: [],
            columnData: new Map(),
            showArchivedColumns: new Set(),
            loading: false,
            error: null,
            fetchColumnCards: vi.fn().mockResolvedValue({ count: 1 })
        };
        get.mockReturnValue(initialState);
    });

    describe('fetchBoard', () => {
        it('should fetch board and update state', async () => {
            const mockColumns = [
                { id: '123', title: 'Test Column', team_id: '456', order_index: 0, created_at: '2024-02-24T12:00:00Z' }
            ];
            (boardApi.fetchColumns as jest.Mock).mockResolvedValue(mockColumns);

            // Mock the fetchColumnCards function for testing
            const fetchColumnCardsMock = vi.fn().mockResolvedValue({ count: 5 });
            get.mockReturnValue({
                ...initialState,
                fetchColumnCards: fetchColumnCardsMock,
                showArchivedColumns: new Set()
            });

            await store.fetchBoard('456');

            expect(boardApi.fetchColumns).toHaveBeenCalledWith('456');
            expect(set).toHaveBeenCalledWith({ loading: true, error: null });

            // Check that the columns and columnData were set correctly
            expect(set).toHaveBeenCalledWith({
                columns: mockColumns,
                columnData: expect.any(Map),
                loading: false
            });

            // Verify fetchColumnCards was called for each column
            expect(fetchColumnCardsMock).toHaveBeenCalledWith('123', 1, undefined, false);
        });

        it('should handle errors when fetching board', async () => {
            const error = new Error('Failed to fetch board');
            (boardApi.fetchColumns as jest.Mock).mockRejectedValue(error);

            await store.fetchBoard('456');

            expect(set).toHaveBeenCalledWith({ loading: true, error: null });
            expect(set).toHaveBeenCalledWith({
                loading: false,
                error: 'Failed to fetch board'
            });
        });
    });

    describe('clearBoard', () => {
        it('should reset board state', () => {
            store.clearBoard();

            expect(set).toHaveBeenCalledWith({
                columns: [],
                columnData: new Map(),
                showArchivedColumns: new Set(),
                loading: false,
                error: null
            });
        });
    });

    describe('addColumn', () => {
        it('should add column and update state', async () => {
            const mockNewColumn = { ...mockColumn, id: '789' };
            (boardApi.addColumn as jest.Mock).mockResolvedValue(mockNewColumn);

            // Mock existing columns to test order_index calculation
            const existingColumns = [
                { ...mockColumn, id: '123', order_index: 1000 }
            ];
            get.mockReturnValue({
                ...initialState,
                columns: existingColumns
            });

            await store.addColumn('456', 'New Column');

            expect(boardApi.addColumn).toHaveBeenCalledWith('456', 'New Column', 2024); // 1000 + 1024
            expect(set).toHaveBeenCalledWith({ loading: true, error: null });

            // Check that the columns and columnData were updated correctly
            expect(set).toHaveBeenCalledWith({
                columns: [...existingColumns, mockNewColumn],
                columnData: expect.any(Map),
                loading: false
            });
        });

        it('should handle add error', async () => {
            const mockError = new Error('Failed to add column');
            (boardApi.addColumn as jest.Mock).mockRejectedValue(mockError);

            await store.addColumn('456', 'New Column');

            expect(set).toHaveBeenCalledWith({ loading: true, error: null });
            expect(set).toHaveBeenCalledWith({
                error: 'Failed to add column',
                loading: false
            });
        });
    });

    describe('updateColumn', () => {
        it('should update column and update state', async () => {
            const mockUpdatedColumn = { ...mockColumn, title: 'Updated Column' };
            (boardApi.updateColumn as jest.Mock).mockResolvedValue(mockUpdatedColumn);

            // Mock existing columns
            const existingColumns = [mockColumn];
            get.mockReturnValue({
                ...initialState,
                columns: existingColumns
            });

            await store.updateColumn('123', 'Updated Column');

            expect(boardApi.updateColumn).toHaveBeenCalledWith('123', 'Updated Column');
            expect(set).toHaveBeenCalledWith({ loading: true, error: null });

            // Check that the columns were updated correctly
            expect(set).toHaveBeenCalledWith({
                columns: [mockUpdatedColumn],
                loading: false
            });
        });

        it('should handle update error', async () => {
            const mockError = new Error('Failed to update column');
            (boardApi.updateColumn as jest.Mock).mockRejectedValue(mockError);

            get.mockReturnValue({
                ...initialState,
                columns: [mockColumn]
            });

            await store.updateColumn('123', 'Updated Column');

            expect(set).toHaveBeenCalledWith({ loading: true, error: null });
            expect(set).toHaveBeenCalledWith({
                error: 'Failed to update column',
                loading: false
            });
        });
    });

    describe('deleteColumn', () => {
        it('should delete column and update state', async () => {
            (boardApi.deleteColumn as jest.Mock).mockResolvedValue({ id: '123' });

            // Setup column data map with the column to be deleted
            const columnDataMap = new Map();
            columnDataMap.set('123', { cards: [], loading: false, error: null, hasMore: true, currentPage: 0 });

            get.mockReturnValue({
                ...initialState,
                columns: [mockColumn],
                columnData: columnDataMap
            });

            await store.deleteColumn('123');

            expect(boardApi.deleteColumn).toHaveBeenCalledWith('123');
            expect(set).toHaveBeenCalledWith({ loading: true, error: null });

            // Check that the column was removed and columnData was updated
            expect(set).toHaveBeenCalledWith({
                columns: [],
                columnData: expect.any(Map),
                loading: false
            });

            // Verify the columnData Map no longer contains the deleted column
            const updatedColumnData = set.mock.calls.find(
                (call: any) => call[0].columnData !== undefined
            )[0].columnData;
            expect(updatedColumnData.has('123')).toBe(false);
        });

        it('should handle delete error', async () => {
            const mockError = new Error('Failed to delete column');
            (boardApi.deleteColumn as jest.Mock).mockRejectedValue(mockError);

            get.mockReturnValue({
                ...initialState,
                columns: [mockColumn]
            });

            await store.deleteColumn('123');

            expect(set).toHaveBeenCalledWith({ loading: true, error: null });
            expect(set).toHaveBeenCalledWith({
                error: 'Failed to delete column',
                loading: false
            });
        });
    });

    describe('reorderColumns', () => {
        it('should reorder columns and update state', async () => {
            const column1 = { ...mockColumn, id: '1', order_index: 0 };
            const column2 = { ...mockColumn, id: '2', order_index: 1 };

            // Expected columns after reordering with updated order_index values
            const expectedColumns = [
                { ...column2, order_index: 0 },
                { ...column1, order_index: 1024 }
            ];

            (boardApi.reorderColumns as jest.Mock).mockResolvedValue(true);
            get.mockReturnValue({
                ...initialState,
                columns: [column1, column2]
            });

            await store.reorderColumns('456', ['2', '1']);

            // Check that the API was called with the correct parameters
            expect(boardApi.reorderColumns).toHaveBeenCalledWith('456', [
                { id: '2', order_index: 0 },
                { id: '1', order_index: 1024 }
            ]);

            expect(set).toHaveBeenCalledWith({ loading: true, error: null });

            // Check that the columns were updated with the new order
            expect(set).toHaveBeenCalledWith({
                columns: expectedColumns,
                loading: false
            });
        });

        it('should handle reorder error', async () => {
            const mockError = new Error('Failed to reorder columns');
            (boardApi.reorderColumns as jest.Mock).mockRejectedValue(mockError);

            get.mockReturnValue({
                ...initialState,
                columns: [mockColumn]
            });

            await store.reorderColumns('456', ['123']);

            expect(set).toHaveBeenCalledWith({ loading: true, error: null });
            expect(set).toHaveBeenCalledWith({
                error: 'Failed to reorder columns',
                loading: false
            });
        });
    });

    describe('toggleColumnArchived', () => {
        it('should toggle column archived state from not showing to showing archived', async () => {
            // Setup initial state with empty showArchivedColumns Set
            const showArchivedColumns = new Set<string>();
            const columnDataMap = new Map();
            columnDataMap.set('123', {
                cards: [],
                loading: false,
                error: null,
                hasMore: true,
                currentPage: 0
            });

            // Mock fetchColumnCards to return expected result
            const fetchColumnCardsMock = vi.fn().mockResolvedValue({ count: 3 });

            get.mockReturnValue({
                ...initialState,
                columns: [mockColumn],
                showArchivedColumns,
                columnData: columnDataMap,
                fetchColumnCards: fetchColumnCardsMock
            });

            await store.toggleColumnArchived('123');

            expect(set).toHaveBeenCalledWith({ loading: true, error: null });

            // Verify the showArchivedColumns Set was updated to include the column
            const updatedState = set.mock.calls.find(
                (call: any) => call[0].showArchivedColumns !== undefined
            )[0];

            expect(updatedState.showArchivedColumns.has('123')).toBe(true);
            expect(updatedState.columnData.get('123').cards).toEqual([]);
            expect(updatedState.loading).toBe(false);

            // Verify fetchColumnCards was called with the correct parameters
            expect(fetchColumnCardsMock).toHaveBeenCalledWith('123', 1, undefined, false);
        });

        it('should toggle column archived state from showing to not showing archived', async () => {
            // Setup initial state with column ID in showArchivedColumns Set
            const showArchivedColumns = new Set<string>(['123']);
            const columnDataMap = new Map();
            columnDataMap.set('123', {
                cards: [{ id: 'card1', archived_at: new Date().toISOString() }],
                loading: false,
                error: null,
                hasMore: false,
                currentPage: 1
            });

            // Mock fetchColumnCards to return expected result
            const fetchColumnCardsMock = vi.fn().mockResolvedValue({ count: 2 });

            get.mockReturnValue({
                ...initialState,
                columns: [mockColumn],
                showArchivedColumns,
                columnData: columnDataMap,
                fetchColumnCards: fetchColumnCardsMock
            });

            await store.toggleColumnArchived('123');

            expect(set).toHaveBeenCalledWith({ loading: true, error: null });

            // Verify the showArchivedColumns Set no longer includes the column
            const updatedState = set.mock.calls.find(
                (call: any) => call[0].showArchivedColumns !== undefined
            )[0];

            expect(updatedState.showArchivedColumns.has('123')).toBe(false);
            expect(updatedState.columnData.get('123').cards).toEqual([]);
            expect(updatedState.loading).toBe(false);

            // Verify fetchColumnCards was called with the correct parameters
            expect(fetchColumnCardsMock).toHaveBeenCalledWith('123', 1, undefined, true);
        });

        it('should handle errors when toggling column archived state', async () => {
            // Setup initial state
            const showArchivedColumns = new Set<string>();

            // Mock fetchColumnCards to throw an error
            const mockError = new Error('Failed to toggle column archive state');
            const fetchColumnCardsMock = vi.fn().mockRejectedValue(mockError);

            get.mockReturnValue({
                ...initialState,
                columns: [mockColumn],
                showArchivedColumns,
                fetchColumnCards: fetchColumnCardsMock
            });

            await store.toggleColumnArchived('123');

            // Verify error handling
            expect(set).toHaveBeenCalledWith({ loading: true, error: null });
            expect(set).toHaveBeenCalledWith({
                error: 'Failed to toggle column archive state',
                loading: false
            });
        });
    });
}); 