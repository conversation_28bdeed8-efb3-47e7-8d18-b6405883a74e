import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createCommentSlice } from '../../../../store/slices/comment';
import { commentApi } from '../../../../api/commentApi';
import { TeamKanbanComment } from '../../../../types';
import { StoreApi } from 'zustand';
import { supabase } from '@/lib/supabase';

// Mock Supabase client
vi.mock('@/lib/supabase', () => ({
    supabase: {
        auth: {
            getUser: vi.fn().mockResolvedValue({ data: { user: { id: '789' } } })
        }
    }
}));

// Mock the comment API
vi.mock('../../../../api/commentApi', () => ({
    commentApi: {
        fetchComments: vi.fn(),
        addComment: vi.fn(),
        updateComment: vi.fn(),
        deleteComment: vi.fn()
    }
}));

describe('Comment Slice', () => {
    const mockComment: TeamKanbanComment = {
        id: '123',
        card_id: '456',
        user_id: '789',
        content: 'Test comment',
        created_at: '2024-02-24T12:00:00Z',
        updated_at: '2024-02-24T12:00:00Z',
        edited_at: null,
        deleted_at: null,
        is_system: false,
        user: {
            id: '789',
            name: 'Test User',
            avatar: 'https://example.com/avatar.jpg'
        }
    };

    let set: any;
    let get: any;
    let store: ReturnType<typeof createCommentSlice>;
    let initialState: any;

    beforeEach(() => {
        set = vi.fn();
        get = vi.fn();
        const mockStoreApi: StoreApi<any> = {
            setState: set,
            getState: get,
            subscribe: vi.fn(),
            destroy: vi.fn(),
            getInitialState: vi.fn()
        };
        store = createCommentSlice(set, get, mockStoreApi);

        // Initialize state for each test
        initialState = {
            comments: [],
            commentPages: {},
            hasMoreComments: {},
            loadingComments: {}
        };
        get.mockReturnValue(initialState);
    });

    describe('fetchComments', () => {
        it('should fetch first page of comments and update state', async () => {
            const mockComments = [mockComment];
            (commentApi.fetchComments as jest.Mock).mockResolvedValue({ comments: mockComments, count: 2 });

            const result = await store.fetchComments('456');

            // Verify API call
            expect(commentApi.fetchComments).toHaveBeenCalledWith('456', 1, 10);

            // Verify first state update (loading)
            expect(set).toHaveBeenCalledWith(expect.objectContaining({
                loadingComments: expect.objectContaining({
                    '456': true
                })
            }));

            // Verify second state update (with data)
            expect(set).toHaveBeenCalledWith(expect.objectContaining({
                comments: mockComments,
                commentPages: expect.objectContaining({
                    '456': mockComments
                }),
                hasMoreComments: expect.objectContaining({
                    '456': false
                }),
                loadingComments: expect.objectContaining({
                    '456': false
                })
            }));

            expect(result).toEqual({ count: 2 });
        });

        it('should fetch subsequent pages and append to existing comments', async () => {
            const firstPageComments = [{ id: '1', content: 'First comment' }];
            const secondPageComments = [{ id: '2', content: 'Second comment' }];

            // Set up initial state with first page already loaded
            initialState.comments = [...firstPageComments];
            initialState.commentPages = { '456': [...firstPageComments] };
            initialState.hasMoreComments = { '456': true };
            initialState.loadingComments = { '456': false };
            get.mockReturnValue(initialState);

            // Mock API response for second page
            (commentApi.fetchComments as jest.Mock).mockResolvedValue({
                comments: secondPageComments,
                count: 2
            });

            const result = await store.fetchComments('456', 2);

            // Verify API call for page 2
            expect(commentApi.fetchComments).toHaveBeenCalledWith('456', 2, 10);

            // Verify loading state update
            expect(set).toHaveBeenCalledWith(expect.objectContaining({
                loadingComments: expect.objectContaining({
                    '456': true
                })
            }));

            // Verify data update after loading
            expect(set).toHaveBeenCalledWith(expect.objectContaining({
                comments: expect.arrayContaining([...firstPageComments, ...secondPageComments]),
                commentPages: expect.objectContaining({
                    '456': expect.arrayContaining([...firstPageComments, ...secondPageComments])
                }),
                hasMoreComments: expect.objectContaining({
                    '456': false
                }),
                loadingComments: expect.objectContaining({
                    '456': false
                })
            }));

            expect(result).toEqual({ count: 2 });
        });

        it('should handle empty response', async () => {
            (commentApi.fetchComments as jest.Mock).mockResolvedValue({
                comments: [],
                count: 0
            });

            await store.fetchComments('456');

            expect(set).toHaveBeenCalledWith({
                loadingComments: {
                    '456': true
                }
            });
            expect(set).toHaveBeenCalledWith({
                comments: [],
                commentPages: {
                    '456': []
                },
                hasMoreComments: {
                    '456': false
                },
                loadingComments: {
                    '456': false
                }
            });
        });

        it('should handle fetch error', async () => {
            const mockError = new Error('Fetch failed');
            (commentApi.fetchComments as jest.Mock).mockRejectedValue(mockError);

            await expect(store.fetchComments('456')).rejects.toThrow(mockError);
            expect(set).toHaveBeenCalledWith({
                loadingComments: {
                    '456': false
                }
            });
        });

        it('should maintain loading state during fetch', async () => {
            (commentApi.fetchComments as jest.Mock).mockImplementation(() =>
                new Promise(resolve => setTimeout(() => resolve({ comments: [], count: 0 }), 100))
            );

            const fetchPromise = store.fetchComments('456');

            // Verify loading state is set
            expect(set).toHaveBeenCalledWith({
                loadingComments: {
                    '456': true
                }
            });

            await fetchPromise;
        });
    });

    describe('addComment', () => {
        it('should add comment and update state', async () => {
            (commentApi.addComment as jest.Mock).mockResolvedValue(mockComment);
            get.mockReturnValue({
                ...initialState,
                comments: [],
                commentPages: { '456': [] }
            });

            const result = await store.addComment('456', 'Test comment');

            // Verify the API was called with the correct parameters
            expect(commentApi.addComment).toHaveBeenCalledWith('456', 'Test comment', '789');
            expect(set).toHaveBeenCalledWith({
                comments: [mockComment],
                commentPages: {
                    '456': [mockComment]
                }
            });
            expect(result).toEqual(mockComment);
        });

        it('should handle missing user authentication', async () => {
            // Mock auth to return no user
            (supabase.auth.getUser as jest.Mock).mockResolvedValueOnce({ data: { user: null } });

            await expect(store.addComment('456', 'Test comment'))
                .rejects.toThrow('No authenticated user');
        });

        it('should append new comment to existing comments', async () => {
            const existingComment = { ...mockComment, id: '999' };
            const newComment = { ...mockComment, id: '123' };

            (commentApi.addComment as jest.Mock).mockResolvedValue(newComment);
            get.mockReturnValue({
                ...initialState,
                comments: [existingComment],
                commentPages: { '456': [existingComment] }
            });

            await store.addComment('456', 'Test comment');

            expect(set).toHaveBeenCalledWith({
                comments: [existingComment, newComment],
                commentPages: {
                    '456': [existingComment, newComment]
                }
            });
        });
    });

    describe('updateComment', () => {
        it('should update comment and update state', async () => {
            const updatedComment = { ...mockComment, content: 'Updated comment' };
            (commentApi.updateComment as jest.Mock).mockResolvedValue(updatedComment);
            get.mockReturnValue({
                ...initialState,
                comments: [mockComment],
                commentPages: { '456': [mockComment] }
            });

            await store.updateComment('123', 'Updated comment');

            expect(commentApi.updateComment).toHaveBeenCalledWith('123', 'Updated comment');
            expect(set).toHaveBeenCalledWith({
                comments: [updatedComment],
                commentPages: {
                    '456': [updatedComment]
                }
            });
        });

        it('should handle update error', async () => {
            const mockError = new Error('Update failed');
            (commentApi.updateComment as jest.Mock).mockRejectedValue(mockError);

            await expect(store.updateComment('123', 'Updated comment')).rejects.toThrow(mockError);
        });
    });

    describe('deleteComment', () => {
        it('should mark comment as deleted and update state', async () => {
            (commentApi.deleteComment as jest.Mock).mockResolvedValue(true);

            initialState.comments = [mockComment];
            initialState.commentPages = { '456': [mockComment] };
            get.mockReturnValue(initialState);

            await store.deleteComment('123');

            expect(set).toHaveBeenCalledWith(expect.objectContaining({
                comments: expect.arrayContaining([
                    expect.objectContaining({
                        id: '123',
                        deleted_at: expect.any(String),
                        content: '[deleted]'
                    })
                ])
            }));

            expect(set).toHaveBeenCalledWith(expect.objectContaining({
                commentPages: expect.objectContaining({
                    '456': expect.arrayContaining([
                        expect.objectContaining({
                            id: '123',
                            deleted_at: expect.any(String),
                            content: '[deleted]'
                        })
                    ])
                })
            }));
        });

        it('should handle delete error', async () => {
            (commentApi.deleteComment as jest.Mock).mockRejectedValue(new Error('Comment not found'));

            initialState.comments = [mockComment];
            get.mockReturnValue(initialState);

            await expect(store.deleteComment('123')).rejects.toThrow('Comment not found');
        });
    });
});