import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createUserSlice } from '../../../../store/slices/user';
import { userApi } from '../../../../api/userApi';
import { User } from '../../../../types';
import { StoreApi } from 'zustand';

// Mock the user API
vi.mock('../../../../api/userApi', () => ({
    userApi: {
        fetchUsers: vi.fn(),
        assignUserToCard: vi.fn(),
        unassignUserFromCard: vi.fn(),
        fetchTeamMembers: vi.fn()
    }
}));

describe('User Slice', () => {
    const mockUser: User = {
        id: '123',
        email: '<EMAIL>',
        name: 'Test User',
        avatar: 'https://example.com/avatar.jpg'
    };

    let set: any;
    let get: any;
    let store: ReturnType<typeof createUserSlice>;

    beforeEach(() => {
        set = vi.fn();
        get = vi.fn();
        const mockStoreApi: StoreApi<any> = {
            setState: set,
            getState: get,
            subscribe: vi.fn(),
            destroy: vi.fn(),
            getInitialState: vi.fn()
        };
        store = createUserSlice(set, get, mockStoreApi);
        vi.clearAllMocks();
    });

    describe('updateUsers', () => {
        it('should fetch users and update state', async () => {
            const mockUsers = [mockUser];
            (userApi.fetchUsers as jest.Mock).mockResolvedValue(mockUsers);

            await store.updateUsers();

            // Verify API call
            expect(userApi.fetchUsers).toHaveBeenCalled();
            expect(set).toHaveBeenCalledWith({ users: mockUsers });
        });

        it('should handle fetch error', async () => {
            const mockError = new Error('Fetch failed');
            (userApi.fetchUsers as jest.Mock).mockRejectedValue(mockError);

            await expect(store.updateUsers()).rejects.toThrow(mockError);
        });

        it('should update state with empty array when no users returned', async () => {
            (userApi.fetchUsers as jest.Mock).mockResolvedValue([]);

            await store.updateUsers();

            expect(set).toHaveBeenCalledWith({ users: [] });
        });
    });

    describe('assignUser', () => {
        it('should assign user to card successfully', async () => {
            await store.assignUser('123', '456');

            // Verify API call
            expect(userApi.assignUserToCard).toHaveBeenCalledWith('123', '456');
        });

        it('should handle assign error', async () => {
            const mockError = new Error('Assign failed');
            (userApi.assignUserToCard as jest.Mock).mockRejectedValue(mockError);

            await expect(store.assignUser('123', '456')).rejects.toThrow(mockError);
        });
    });

    describe('unassignUser', () => {
        it('should unassign user from card successfully', async () => {
            await store.unassignUser('456');

            // Verify API call
            expect(userApi.unassignUserFromCard).toHaveBeenCalledWith('456');
        });

        it('should handle unassign error', async () => {
            const mockError = new Error('Unassign failed');
            (userApi.unassignUserFromCard as jest.Mock).mockRejectedValue(mockError);

            await expect(store.unassignUser('456')).rejects.toThrow(mockError);
        });
    });

    describe('fetchTeamMembers', () => {
        it('should fetch and cache team members', async () => {
            const mockMembers = [mockUser];
            (userApi.fetchTeamMembers as jest.Mock).mockResolvedValue(mockMembers);

            get.mockReturnValue({
                teamMembers: {},
                loadingTeamMembers: {}
            });

            const result = await store.fetchTeamMembers('team-123');

            expect(set).toHaveBeenCalledWith({
                loadingTeamMembers: {
                    'team-123': true
                }
            });

            // Verify API call
            expect(userApi.fetchTeamMembers).toHaveBeenCalledWith('team-123');

            expect(set).toHaveBeenCalledWith({
                teamMembers: {
                    'team-123': mockMembers
                },
                loadingTeamMembers: {
                    'team-123': false
                }
            });

            expect(result).toEqual(mockMembers);
        });

        it('should return cached members if available', async () => {
            const mockMembers = [mockUser];
            
            // Mock state with cached members
            get.mockReturnValue({
                teamMembers: {
                    'team-123': mockMembers
                },
                loadingTeamMembers: {
                    'team-123': false
                }
            });

            const result = await store.fetchTeamMembers('team-123');

            // Verify API was not called
            expect(userApi.fetchTeamMembers).not.toHaveBeenCalled();

            // Verify cached members were returned
            expect(result).toEqual(mockMembers);
        });

        it('should handle fetch error', async () => {
            const mockError = new Error('Failed to fetch team members');
            (userApi.fetchTeamMembers as jest.Mock).mockRejectedValue(mockError);

            get.mockReturnValue({
                teamMembers: {},
                loadingTeamMembers: {}
            });

            await expect(store.fetchTeamMembers('team-123')).rejects.toThrow(mockError);

            // Verify loading state was reset
            expect(set).toHaveBeenCalledWith({
                loadingTeamMembers: {
                    'team-123': false
                }
            });
        });
    });
});