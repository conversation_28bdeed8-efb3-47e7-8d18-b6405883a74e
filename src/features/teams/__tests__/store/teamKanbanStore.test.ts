import { vi, describe, it, expect, beforeEach } from 'vitest';

// Mock the API modules with inline mock functions
vi.mock('../../api/boardApi', () => ({
    boardApi: {
        fetchColumns: vi.fn().mockResolvedValue([{
            id: '1',
            title: 'To Do',
            team_id: '123',
            order_index: 0,
            created_at: '2023-01-01T00:00:00Z',
            updated_at: '2023-01-01T00:00:00Z'
        }])
    }
}));

vi.mock('../../api/userApi', () => ({
    userApi: {
        fetchUsers: vi.fn().mockResolvedValue([{
            id: '1',
            name: '<PERSON>',
            email: '<EMAIL>',
            avatar: 'https://example.com/avatar.jpg'
        }]),
        assignUserToCard: vi.fn().mockResolvedValue(undefined),
        unassignUserFromCard: vi.fn().mockResolvedValue(undefined)
    }
}));

vi.mock('../../api/cardApi', () => ({
    cardApi: {
        fetchCards: vi.fn().mockResolvedValue({ cards: [], count: 0 }),
        deleteCard: vi.fn().mockResolvedValue(undefined),
        updateCard: vi.fn().mockResolvedValue({}),
        addCard: vi.fn().mockResolvedValue({}),
        moveCard: vi.fn().mockResolvedValue({})
    }
}));

// Mock the store slices
vi.mock('../../store/slices/board', () => ({
    createBoardSlice: vi.fn().mockReturnValue({
        columns: [],
        fetchBoard: vi.fn(),
        cleanup: vi.fn()
    })
}));

vi.mock('../../store/slices/card', () => ({
    createCardSlice: vi.fn().mockReturnValue({
        cards: [],
        fetchColumnCards: vi.fn(),
        updateCard: vi.fn(),
        deleteCard: vi.fn(),
        addCard: vi.fn(),
        moveCard: vi.fn()
    })
}));

vi.mock('../../store/slices/comment', () => ({
    createCommentSlice: vi.fn().mockReturnValue({
        comments: [],
        fetchComments: vi.fn(),
        addComment: vi.fn()
    })
}));

vi.mock('../../store/slices/user', () => ({
    createUserSlice: vi.fn().mockReturnValue({
        users: [],
        fetchTeamMembers: vi.fn()
    })
}));

vi.mock('../../store/slices/filters', () => ({
    createFilterSlice: vi.fn().mockReturnValue({
        filters: {},
        setAssigneeFilter: vi.fn(),
        setPriorityFilter: vi.fn()
    })
}));

// Import the API modules after mocking them
import { boardApi } from '../../api/boardApi';
import { userApi } from '../../api/userApi';
import { cardApi } from '../../api/cardApi';

describe('Team Kanban API Mocks', () => {
    const teamId = '123';
    const cardId = '456';
    const userId = '789';

    beforeEach(() => {
        // Reset all mocks
        vi.clearAllMocks();
    });

    it('should correctly mock boardApi.fetchColumns', async () => {
        const result = await boardApi.fetchColumns(teamId);
        expect(boardApi.fetchColumns).toHaveBeenCalledWith(teamId);
        expect(result).toHaveLength(1);
        expect(result[0].id).toBe('1');
        expect(result[0].title).toBe('To Do');
    });

    it('should correctly mock userApi.fetchUsers', async () => {
        const result = await userApi.fetchUsers();
        expect(userApi.fetchUsers).toHaveBeenCalled();
        expect(result).toHaveLength(1);
        expect(result[0].id).toBe('1');
        expect(result[0].name).toBe('John Doe');
    });

    it('should correctly mock userApi.assignUserToCard', async () => {
        await userApi.assignUserToCard(userId, cardId);
        expect(userApi.assignUserToCard).toHaveBeenCalledWith(userId, cardId);
    });

    it('should correctly mock cardApi.deleteCard', async () => {
        await cardApi.deleteCard(cardId);
        expect(cardApi.deleteCard).toHaveBeenCalledWith(cardId);
    });
}); 