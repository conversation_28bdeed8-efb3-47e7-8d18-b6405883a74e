import { describe, it, expect, vi, beforeEach } from 'vitest';
import { boardApi } from '../../api/boardApi';
import { supabase } from '@/lib/supabase';
import { TeamKanbanColumn } from '../../types';

// Mock Supabase client
vi.mock('@/lib/supabase', () => ({
    supabase: {
        from: vi.fn(() => ({
            select: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                    order: vi.fn().mockReturnValue({
                        then: vi.fn()
                    })
                })
            }),
            insert: vi.fn().mockReturnValue({
                select: vi.fn().mockReturnValue({
                    single: vi.fn()
                })
            }),
            update: vi.fn().mockReturnValue({
                eq: vi.fn().mockReturnValue({
                    select: vi.fn().mockReturnValue({
                        single: vi.fn()
                    })
                })
            }),
            delete: vi.fn().mockReturnValue({
                eq: vi.fn()
            }),
            upsert: vi.fn()
        }))
    }
}));

describe('boardApi', () => {
    const mockColumn: TeamKanbanColumn = {
        id: '123',
        team_id: '456',
        title: 'Test Column',
        order_index: 1000,
        created_at: '2024-02-24T12:00:00Z'
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('fetchColumns', () => {
        it('should fetch columns successfully', async () => {
            const mockColumns = [mockColumn];
            const mockOrder = vi.fn().mockResolvedValue({ data: mockColumns, error: null });
            const mockEq = vi.fn().mockReturnValue({ order: mockOrder });
            const mockSelect = vi.fn().mockReturnValue({ eq: mockEq });

            (supabase.from as any).mockReturnValue({ select: mockSelect });

            const result = await boardApi.fetchColumns('456');

            expect(result).toEqual(mockColumns);
            expect(supabase.from).toHaveBeenCalledWith('kanban_columns');
            expect(mockEq).toHaveBeenCalledWith('team_id', '456');
            expect(mockOrder).toHaveBeenCalledWith('order_index');
        });

        it('should throw error when fetch fails', async () => {
            const mockError = new Error('Failed to fetch columns');
            const mockOrder = vi.fn().mockResolvedValue({ data: null, error: mockError });
            const mockEq = vi.fn().mockReturnValue({ order: mockOrder });
            const mockSelect = vi.fn().mockReturnValue({ eq: mockEq });

            (supabase.from as any).mockReturnValue({ select: mockSelect });

            await expect(boardApi.fetchColumns('456')).rejects.toThrow('Failed to fetch columns');
        });
    });

    describe('addColumn', () => {
        it('should add column successfully', async () => {
            const mockSingle = vi.fn().mockResolvedValue({ data: mockColumn, error: null });
            const mockSelect = vi.fn().mockReturnValue({ single: mockSingle });
            const mockInsert = vi.fn().mockReturnValue({ select: mockSelect });

            (supabase.from as any).mockReturnValue({ insert: mockInsert });

            const result = await boardApi.addColumn('456', 'Test Column', 1000);

            expect(result).toEqual(mockColumn);
            expect(supabase.from).toHaveBeenCalledWith('kanban_columns');
            expect(mockInsert).toHaveBeenCalledWith({
                team_id: '456',
                title: 'Test Column',
                order_index: 1000
            });
        });

        it('should throw error when add fails', async () => {
            const mockError = new Error('Failed to add column');
            const mockSingle = vi.fn().mockResolvedValue({ data: null, error: mockError });
            const mockSelect = vi.fn().mockReturnValue({ single: mockSingle });
            const mockInsert = vi.fn().mockReturnValue({ select: mockSelect });

            (supabase.from as any).mockReturnValue({ insert: mockInsert });

            await expect(boardApi.addColumn('456', 'Test Column', 1000)).rejects.toThrow(mockError);
        });
    });

    describe('updateColumn', () => {
        it('should update column successfully', async () => {
            const mockSingle = vi.fn().mockResolvedValue({ data: mockColumn, error: null });
            const mockSelect = vi.fn().mockReturnValue({ single: mockSingle });
            const mockEq = vi.fn().mockReturnValue({ select: mockSelect });
            const mockUpdate = vi.fn().mockReturnValue({ eq: mockEq });

            (supabase.from as any).mockReturnValue({ update: mockUpdate });

            const result = await boardApi.updateColumn('123', 'Updated Column');

            expect(result).toEqual(mockColumn);
            expect(supabase.from).toHaveBeenCalledWith('kanban_columns');
            expect(mockUpdate).toHaveBeenCalledWith({ title: 'Updated Column' });
            expect(mockEq).toHaveBeenCalledWith('id', '123');
        });

        it('should throw error when update fails', async () => {
            const mockError = new Error('Failed to update column');
            const mockSingle = vi.fn().mockResolvedValue({ data: null, error: mockError });
            const mockSelect = vi.fn().mockReturnValue({ single: mockSingle });
            const mockUpdate = vi.fn().mockReturnValue({ select: mockSelect });

            (supabase.from as any).mockReturnValue({ update: mockUpdate });

            await expect(boardApi.updateColumn('123', 'Updated Column')).rejects.toThrow(mockError);
        });
    });

    describe('deleteColumn', () => {
        it('should delete column successfully', async () => {
            const mockEq = vi.fn().mockResolvedValue({ error: null });
            const mockDelete = vi.fn().mockReturnValue({ eq: mockEq });

            (supabase.from as any).mockReturnValue({ delete: mockDelete });

            await boardApi.deleteColumn('123');

            expect(supabase.from).toHaveBeenCalledWith('kanban_columns');
            expect(mockDelete).toHaveBeenCalled();
            expect(mockEq).toHaveBeenCalledWith('id', '123');
        });

        it('should throw error when delete fails', async () => {
            const mockError = new Error('Failed to delete column');
            const mockDelete = vi.fn().mockResolvedValue({ error: mockError });

            (supabase.from as any).mockReturnValue({ delete: mockDelete });

            await expect(boardApi.deleteColumn('123')).rejects.toThrow(mockError);
        });
    });

    describe('reorderColumns', () => {
        it('should reorder columns successfully', async () => {
            const updates = [
                { id: '123', order_index: 0 },
                { id: '456', order_index: 1 }
            ];

            // Mock the existing columns data that would be returned by the database
            const existingColumns = [
                { id: '123', team_id: '789', title: 'Column 1', order_index: 1, created_at: '2024-02-24T12:00:00Z' },
                { id: '456', team_id: '789', title: 'Column 2', order_index: 2, created_at: '2024-02-24T12:00:00Z' }
            ];

            // Set up mock chain for select → in
            const mockIn = vi.fn().mockResolvedValue({ data: existingColumns, error: null });
            const mockSelect = vi.fn().mockReturnValue({ in: mockIn });

            // Set up mock for upsert
            const mockUpsert = vi.fn().mockResolvedValue({ error: null });

            // Set up supabase.from() to provide both chains
            (supabase.from as any).mockImplementation((table: string) => {
                expect(table).toBe('kanban_columns');
                return {
                    select: mockSelect,
                    upsert: mockUpsert
                };
            });

            await boardApi.reorderColumns('789', updates);

            // Verify the select and in were called correctly
            expect(mockSelect).toHaveBeenCalledWith('*');
            expect(mockIn).toHaveBeenCalledWith('id', ['123', '456']);

            // Verify upsert was called with the expected data (existing columns with updated order)
            expect(mockUpsert).toHaveBeenCalledWith([
                { ...existingColumns[0], order_index: 0, team_id: '789' },
                { ...existingColumns[1], order_index: 1, team_id: '789' }
            ]);
        });

        it('should throw error when reorder fails', async () => {
            const mockError = new Error('Failed to reorder columns');

            // Set up mock for fetch
            const mockIn = vi.fn().mockResolvedValue({
                data: [
                    { id: '123', team_id: '789', title: 'Column 1', order_index: 1, created_at: '2024-02-24T12:00:00Z' }
                ],
                error: null
            });
            const mockSelect = vi.fn().mockReturnValue({ in: mockIn });

            // Set up mock for upsert that returns error
            const mockUpsert = vi.fn().mockResolvedValue({ error: mockError });

            (supabase.from as any).mockImplementation((_table: string) => {
                return {
                    select: mockSelect,
                    upsert: mockUpsert
                };
            });

            await expect(boardApi.reorderColumns('789', [{ id: '123', order_index: 0 }])).rejects.toThrow('Failed to reorder columns');
            expect(mockUpsert).toHaveBeenCalled();
        });
    });
}); 