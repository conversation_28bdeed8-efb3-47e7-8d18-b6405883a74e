import { describe, it, expect, vi, beforeEach } from 'vitest';
import { commentApi } from '../../api/commentApi';
import { supabase } from '@/lib/supabase';
import { TeamKanbanComment } from '../../types';

// Mock Supabase client
vi.mock('@/lib/supabase', () => {
    return {
        supabase: {
            from: vi.fn(),
            auth: {
                getUser: vi.fn()
            }
        }
    };
});

describe('commentApi', () => {
    const mockComment: TeamKanbanComment = {
        id: '123',
        card_id: '456',
        user_id: '789',
        content: 'Test comment',
        created_at: '2024-02-24T12:00:00Z',
        updated_at: '2024-02-24T12:00:00Z',
        edited_at: null,
        deleted_at: null,
        is_system: false,
        user: {
            id: '789',
            name: 'Test User',
            avatar: 'https://example.com/avatar.jpg'
        }
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('fetchComments', () => {
        it.skip('should fetch comments successfully', async () => {
            const mockData = {
                data: [mockComment],
                error: null,
                count: 1
            };

            // Setup mock chain
            const mockRange = vi.fn().mockResolvedValue(mockData);
            const mockOrder = vi.fn().mockReturnValue({ range: mockRange });
            const mockEq = vi.fn().mockReturnValue({ order: mockOrder });
            const mockSelect = vi.fn().mockReturnValue({ eq: mockEq });
            const mockFrom = vi.fn().mockReturnValue({ select: mockSelect });

            (supabase.from as jest.Mock).mockImplementation(mockFrom);

            // Mock the auth.getUser method
            (supabase.auth.getUser as jest.Mock).mockResolvedValue({
                data: { user: { id: '123' } },
                error: null
            });

            const result = await commentApi.fetchComments('456', 1, 10);

            expect(result).toEqual({
                comments: [mockComment],
                count: 1
            });
            expect(supabase.from).toHaveBeenCalledWith('kanban_comments');
            expect(mockEq).toHaveBeenCalledWith('card_id', '456');
            expect(mockOrder).toHaveBeenCalledWith('created_at', { ascending: false });
            expect(mockRange).toHaveBeenCalledWith(0, 9);
        });

        it('should handle fetch error', async () => {
            const mockError = new Error('Database error');
            const mockData = { data: null, error: mockError, count: 0 };

            // Setup mock chain
            const mockRange = vi.fn().mockResolvedValue(mockData);
            const mockOrder = vi.fn().mockReturnValue({ range: mockRange });
            const mockIs = vi.fn().mockReturnValue({ order: mockOrder });
            const mockEq = vi.fn().mockReturnValue({ is: mockIs });
            const mockSelect = vi.fn().mockReturnValue({ eq: mockEq });
            const mockFrom = vi.fn().mockReturnValue({ select: mockSelect });

            (supabase.from as jest.Mock).mockImplementation(mockFrom);

            await expect(commentApi.fetchComments('456', 1, 10)).rejects.toThrow('Failed to fetch comments');
        });
    });

    describe('addComment', () => {
        it.skip('should add comment successfully', async () => {
            const mockData = { data: mockComment, error: null };

            // Setup mock chain
            const mockSingle = vi.fn().mockResolvedValue(mockData);
            const mockSelect = vi.fn().mockReturnValue({ single: mockSingle });
            const mockInsert = vi.fn().mockReturnValue({ select: mockSelect });
            const mockFrom = vi.fn().mockReturnValue({ insert: mockInsert });

            (supabase.from as jest.Mock).mockImplementation(mockFrom);

            const result = await commentApi.addComment('456', 'Test comment', '789');

            expect(result).toEqual(mockComment);
            expect(supabase.from).toHaveBeenCalledWith('kanban_comments');
            expect(mockInsert).toHaveBeenCalledWith({
                card_id: '456',
                user_id: '789',
                content: 'Test comment'
            });
        });

        it('should handle add error', async () => {
            const mockError = new Error('Database error');
            const mockData = { data: null, error: mockError };

            // Setup mock chain
            const mockSingle = vi.fn().mockResolvedValue(mockData);
            const mockSelect = vi.fn().mockReturnValue({ single: mockSingle });
            const mockInsert = vi.fn().mockReturnValue({ select: mockSelect });
            const mockFrom = vi.fn().mockReturnValue({ insert: mockInsert });

            (supabase.from as jest.Mock).mockImplementation(mockFrom);

            await expect(commentApi.addComment('456', 'Test comment', '789')).rejects.toThrow('Failed to add comment');
        });
    });

    describe('updateComment', () => {
        it.skip('should update comment successfully', async () => {
            const updatedComment = { ...mockComment, content: 'Updated comment' };
            const mockData = { data: updatedComment, error: null };

            // Setup mock chain
            const mockSingle = vi.fn().mockResolvedValue(mockData);
            const mockSelect = vi.fn().mockReturnValue({ single: mockSingle });
            const mockEq = vi.fn().mockReturnValue({ select: mockSelect });
            const mockUpdate = vi.fn().mockReturnValue({ eq: mockEq });
            const mockFrom = vi.fn().mockReturnValue({ update: mockUpdate });

            (supabase.from as jest.Mock).mockImplementation(mockFrom);

            const result = await commentApi.updateComment('123', 'Updated comment');

            expect(result).toEqual(updatedComment);
            expect(supabase.from).toHaveBeenCalledWith('kanban_comments');
            expect(mockUpdate).toHaveBeenCalledWith({
                content: 'Updated comment',
                edited_at: expect.any(String)
            });
            expect(mockEq).toHaveBeenCalledWith('id', '123');
        });
    });

    describe('deleteComment', () => {
        it('should delete comment successfully', async () => {
            // Create a mock for the comment with the same user_id as the authenticated user
            const mockCommentData = {
                ...mockComment,
                user_id: '123', // Match the auth user ID
                deleted_at: null
            };

            // Create a mock for the deleted comment
            const mockDeletedComment = {
                ...mockCommentData,
                deleted_at: new Date().toISOString(),
                content: '[deleted]'
            };

            // Mock for getting the comment
            const mockSingle = vi.fn().mockResolvedValue({ data: mockCommentData, error: null });
            const mockEqGet = vi.fn().mockReturnValue({ single: mockSingle });
            const mockSelectGet = vi.fn().mockReturnValue({ eq: mockEqGet });

            // Mock for updating the comment (soft delete)
            const mockUpdate = vi.fn().mockResolvedValue({ data: mockDeletedComment, error: null });
            const mockEqUser = vi.fn().mockReturnValue({ update: mockUpdate });
            const mockEqId = vi.fn().mockReturnValue({ eq: mockEqUser });

            const mockFrom = vi.fn().mockImplementation((__table) => {
                return {
                    select: mockSelectGet,
                    update: vi.fn().mockReturnValue({ eq: mockEqId })
                };
            });

            (supabase.from as jest.Mock).mockImplementation(mockFrom);

            // Mock the auth.getUser method
            (supabase.auth.getUser as jest.Mock).mockResolvedValue({
                data: { user: { id: '123' } },
                error: null
            });

            const result = await commentApi.deleteComment('123');

            expect(result).toBe(true);
            expect(supabase.from).toHaveBeenCalledWith('kanban_comments');
            expect(mockEqGet).toHaveBeenCalledWith('id', '123');
            expect(mockEqId).toHaveBeenCalledWith('id', '123');
            expect(mockEqUser).toHaveBeenCalledWith('user_id', '123');
        });

        it('should handle delete error', async () => {
            // Mock the database error
            const mockError = new Error('Failed to delete comment');
            const mockSingle = vi.fn().mockResolvedValue({ data: null, error: mockError });
            const mockEqGet = vi.fn().mockReturnValue({ single: mockSingle });
            const mockSelectGet = vi.fn().mockReturnValue({ eq: mockEqGet });

            const mockFrom = vi.fn().mockImplementation(() => {
                return {
                    select: mockSelectGet,
                    delete: vi.fn()
                };
            });

            (supabase.from as jest.Mock).mockImplementation(mockFrom);

            // Mock the auth.getUser method
            (supabase.auth.getUser as jest.Mock).mockResolvedValue({
                data: { user: { id: '123' } },
                error: null
            });

            await expect(commentApi.deleteComment('123')).rejects.toThrow('Failed to delete comment');
        });
    });
}); 