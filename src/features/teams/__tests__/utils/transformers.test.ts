import { describe, it, expect, vi } from 'vitest';
import {
    calculateNewOrderIndex,
    calculateNewTimestamp,
    transformUser,
    transformComment,
    transformCard
} from '../../utils/transformers';

describe('calculateNewOrderIndex', () => {
    it('should return 1024 for empty array', () => {
        const result = calculateNewOrderIndex([], 0, 0);
        expect(result).toBe(1024);
    });

    it('should handle single item array', () => {
        const items = [{ order_index: 1024 }];

        // Moving before the item
        expect(calculateNewOrderIndex(items, 0, 0)).toBe(0);

        // Moving after the item
        expect(calculateNewOrderIndex(items, 0, 1)).toBe(2048);
    });

    it('should calculate index between two items', () => {
        const items = [
            { order_index: 1000 },
            { order_index: 2000 }
        ];
        const result = calculateNewOrderIndex(items, 1, 1);
        expect(result).toBe(1500);
    });

    it('should handle moving to start of list', () => {
        const items = [
            { order_index: 1000 },
            { order_index: 2000 }
        ];
        const result = calculateNewOrderIndex(items, 1, 0);
        expect(result).toBe(-24);
    });

    it('should handle moving to end of list', () => {
        const items = [
            { order_index: 1000 },
            { order_index: 2000 }
        ];
        const result = calculateNewOrderIndex(items, 0, 2);
        expect(result).toBe(3024);
    });
});

describe('calculateNewTimestamp', () => {
    it('should return current time for empty array', () => {
        const result = calculateNewTimestamp([], 0);
        expect(new Date(result).getTime()).toBeLessThanOrEqual(Date.now());
    });

    it('should handle moving to start', () => {
        const now = new Date();
        const items = [
            { position_updated_at: now.toISOString() }
        ];
        const result = calculateNewTimestamp(items, 0);
        expect(new Date(result).getTime()).toBeGreaterThan(now.getTime());
    });

    it('should handle moving to end', () => {
        const now = new Date();
        const items = [
            { position_updated_at: now.toISOString() }
        ];
        const result = calculateNewTimestamp(items, 1);
        expect(new Date(result).getTime()).toBeLessThan(now.getTime());
    });

    it('should handle moving between items', () => {
        const now = new Date();
        const items = [
            { position_updated_at: new Date(now.getTime() + 60000).toISOString() },
            { position_updated_at: new Date(now.getTime() - 60000).toISOString() }
        ];
        const result = calculateNewTimestamp(items, 1);
        const resultTime = new Date(result).getTime();
        expect(resultTime).toBeLessThan(new Date(items[0].position_updated_at).getTime());
        expect(resultTime).toBeGreaterThan(new Date(items[1].position_updated_at).getTime());
    });

    it('should handle invalid timestamps gracefully', () => {
        // Mock console.warn to prevent warning output in tests
        const originalWarn = console.warn;
        console.warn = vi.fn();
        
        const items = [
            { position_updated_at: 'invalid-date' },
            { position_updated_at: 'also-invalid' }
        ];
        
        try {
            const result = calculateNewTimestamp(items, 1);
            // Just verify we get something back without throwing
            expect(result).toBeTruthy();
            expect(typeof result).toBe('string');
        } finally {
            // Restore original console.warn
            console.warn = originalWarn;
        }
    });
});

describe('transformUser', () => {
    it('should transform user profile data correctly', () => {
        const profile = {
            id: '123',
            email: '<EMAIL>',
            full_name: 'Test User',
            avatar_url: 'https://example.com/avatar.jpg'
        };

        const result = transformUser(profile);

        expect(result).toEqual({
            id: '123',
            email: '<EMAIL>',
            name: 'Test User',
            avatar: 'https://example.com/avatar.jpg'
        });
    });

    it('should handle missing data gracefully', () => {
        const profile = {
            id: '123'
        };

        const result = transformUser(profile);

        expect(result).toEqual({
            id: '123',
            email: '',
            name: 'Unknown User',
            avatar: expect.stringContaining('dicebear.com')
        });
    });

    it('should use email as name if full_name is missing', () => {
        const profile = {
            id: '123',
            email: '<EMAIL>'
        };

        const result = transformUser(profile);

        expect(result).not.toBeNull();
        expect(result?.name).toBe('test');
    });
});

describe('transformComment', () => {
    it('should transform comment data correctly', () => {
        const comment = {
            id: '123',
            card_id: '456',
            user_id: '789',
            content: 'Test comment',
            created_at: '2024-02-24T12:00:00Z',
            updated_at: '2024-02-24T12:00:00Z',
            edited_at: null,
            deleted_at: null,
            is_system: false,
            profile: {
                id: '789',
                full_name: 'Test User',
                avatar_url: 'https://example.com/avatar.jpg'
            }
        };

        const result = transformComment(comment);

        expect(result).toEqual({
            id: '123',
            card_id: '456',
            user_id: '789',
            content: 'Test comment',
            created_at: '2024-02-24T12:00:00Z',
            updated_at: '2024-02-24T12:00:00Z',
            edited_at: null,
            deleted_at: null,
            is_system: false,
            user: {
                id: '789',
                name: 'Test User',
                avatar: 'https://example.com/avatar.jpg'
            }
        });
    });

    it('should handle missing profile data', () => {
        const comment = {
            id: '123',
            card_id: '456',
            user_id: '789',
            content: 'Test comment',
            created_at: '2024-02-24T12:00:00Z',
            updated_at: '2024-02-24T12:00:00Z',
            edited_at: null,
            deleted_at: null,
            is_system: false
        };

        const result = transformComment(comment);

        expect(result).toEqual({
            id: '123',
            card_id: '456',
            user_id: '789',
            content: 'Test comment',
            created_at: '2024-02-24T12:00:00Z',
            updated_at: '2024-02-24T12:00:00Z',
            edited_at: null,
            deleted_at: null,
            is_system: false,
            user: {
                id: '789',
                name: 'Unknown User',
                avatar: ''
            }
        });
    });
});

describe('transformCard', () => {
    it('should transform card data correctly', () => {
        const card = {
            id: '123',
            title: 'Test Card',
            description: 'Test description',
            order_index: 1000,
            column_id: '456',
            team_id: '789',
            assignee_id: '001',
            profiles: {
                id: '001',
                full_name: 'Test Assignee',
                avatar_url: 'https://example.com/avatar.jpg'
            },
            priority: 'P1' as 'P1' | 'P2' | 'P3',
            due_date: '2024-03-24T12:00:00Z',
            created_at: '2024-02-24T12:00:00Z',
            updated_at: '2024-02-24T12:00:00Z',
            position_updated_at: '2024-02-24T12:00:00Z',
            archived_at: null,
            deleted_at: null,
            subtasks: [
                {
                    id: '321',
                    card_id: '123',
                    title: 'Test Subtask',
                    is_completed: false,
                    order_index: 0,
                    created_at: '2024-02-24T12:00:00Z',
                    updated_at: '2024-02-24T12:00:00Z',
                    due_date: null
                }
            ]
        };

        const result = transformCard(card);

        expect(result).toMatchObject({
            id: '123',
            title: 'Test Card',
            description: 'Test description',
            order_index: 1000,
            column_id: '456',
            team_id: '789',
            assignee: null,
            priority: 'P1',
            due_date: '2024-03-24T12:00:00Z',
            created_at: '2024-02-24T12:00:00Z',
            updated_at: '2024-02-24T12:00:00Z',
            position_updated_at: '2024-02-24T12:00:00Z',
            archived_at: null,
            deleted_at: null,
            subtasks: [{
                id: '321',
                card_id: '123',
                title: 'Test Subtask',
                is_completed: false,
                order_index: 0,
                created_at: '2024-02-24T12:00:00Z',
                updated_at: '2024-02-24T12:00:00Z',
                due_date: null
            }],
            comments: []
        });
    });

    it('should handle missing assignee', () => {
        const card = {
            id: '123',
            title: 'Test Card',
            assignee: null,
            subtasks: [],
            column_id: 'col123',
            team_id: 'team123',
            created_at: '2024-02-24T12:00:00Z',
            updated_at: '2024-02-24T12:00:00Z',
            position_updated_at: '2024-02-24T12:00:00Z'
        };

        const result = transformCard(card);

        expect(result.assignee).toBeNull();
    });

    it('should handle missing subtasks', () => {
        const card = {
            id: '123',
            title: 'Test Card',
            assignee: null,
            column_id: 'col123',
            team_id: 'team123',
            created_at: '2024-02-24T12:00:00Z',
            updated_at: '2024-02-24T12:00:00Z',
            position_updated_at: '2024-02-24T12:00:00Z'
        };

        const result = transformCard(card);

        expect(result.subtasks).toEqual([]);
    });

    it.skip('should transform card data correctly', () => {
        // Test code
    });
}); 