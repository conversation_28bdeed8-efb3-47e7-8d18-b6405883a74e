import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Settings, RefreshCw } from 'lucide-react';
import { useTeamsStore } from '../store/teamsStore';
import { TeamCard } from '@/features/teams';
import { useToast } from "@/components/ui/use-toast";
import { supabase } from '@/lib/supabase';
import { Team } from '../types';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuCheckboxItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { TeamCardSkeleton } from './TeamCardSkeleton';

const Teams = () => {
    const {
        teams,
        loading,
        error: apiError,
        fetchTeams,
        showInactive,
        toggleShowInactive,
        canToggleInactive,
        isAdmin,
        showAllTeams,
        toggleShowAllTeams
    } = useTeamsStore();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [menuOpen, setMenuOpen] = React.useState(false);
    const [sessionError, setSessionError] = React.useState(false);

    const displayTeams = teams.sort((a: Team, b: Team) => {
        // Sort by status first (Active before Inactive)
        if (a.status !== b.status) {
            return a.status === 'Active' ? -1 : 1;
        }
        // Then sort by creation date within each status group
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    const handleMenuOpenChange = (open: boolean) => {
        if (!open && !document.activeElement?.getAttribute('role')?.includes('menuitem')) {
            setMenuOpen(false);
        } else if (open) {
            setMenuOpen(true);
        }
    };

    const handleRetry = async () => {
        try {
            await fetchTeams();
        } catch (_error) {
            toast({
                title: "Error loading teams",
                description: "There was a problem loading the team data. Please try again.",
                variant: "destructive",
            });
        }
    };

    useEffect(() => {
        const loadTeams = async () => {
            try {
                const { data: { session } = { session: null }, error: sessionError } = await supabase.auth.getSession();
                if (sessionError) {
                    console.error('Session error:', sessionError);
                    toast({
                        title: "Authentication Error",
                        description: "Please sign in to access team data.",
                        variant: "destructive",
                    });
                    return;
                }
                if (!session) {
                    setSessionError(true);
                    toast({
                        title: "Authentication Error",
                        description: "Please sign in to access team data.",
                        variant: "destructive",
                    });
                    return;
                }
                setSessionError(false);
                await fetchTeams();
            } catch (error) {
                toast({
                    title: "Error loading teams",
                    description: "There was a problem loading the team data. Please try again.",
                    variant: "destructive",
                });
                console.error('Team loading error:', error);
            }
        };

        loadTeams();
    }, [fetchTeams, toast]);

    const handleTeamClick = (team: Team) => {
        navigate(`/team/${team.id}`);
    };

    if (sessionError) {
        return (
            <div className="flex items-center justify-center h-full p-8">
                <Card className="p-6">
                    <CardContent>
                        <p className="text-destructive">Please sign in to access team data.</p>
                    </CardContent>
                </Card>
            </div>
        );
    }

    if (apiError) {
        return (
            <div className="space-y-6 p-8">
                <div className="flex flex-col gap-2">
                    <h2 className="text-2xl font-semibold tracking-tight">Teams</h2>
                    <p className="text-sm text-muted-foreground">
                        Manage and view your teams
                    </p>
                </div>
                <div className="flex flex-col gap-4">
                    <p className="text-destructive">{apiError}</p>
                    <Button
                        variant="outline"
                        onClick={handleRetry}
                        className="w-fit"
                    >
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Retry
                    </Button>
                </div>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="space-y-6 p-8">
                <div className="flex flex-col gap-2">
                    <h2 className="text-2xl font-semibold tracking-tight">Teams</h2>
                    <p className="text-sm text-muted-foreground">
                        Manage and view your teams
                    </p>
                </div>
                <div role="status" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Array.from({ length: 6 }).map((_, i) => (
                        <TeamCardSkeleton key={i} />
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6 p-8" data-testid="teams-container">
            <div className="flex justify-between items-center">
                <div className="flex flex-col gap-2">
                    <h2 className="text-2xl font-semibold tracking-tight">Teams</h2>
                    <p className="text-sm text-muted-foreground">
                        Manage and view your teams
                    </p>
                </div>
                {(canToggleInactive || isAdmin) && (
                    <DropdownMenu open={menuOpen} onOpenChange={handleMenuOpenChange}>
                        <DropdownMenuTrigger asChild>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="p-2 hover:bg-accent rounded-full transition-colors"
                                aria-label="settings"
                                data-testid="settings-button"
                            >
                                <Settings className="h-5 w-5" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            {canToggleInactive && (
                                <DropdownMenuCheckboxItem
                                    checked={showInactive}
                                    onCheckedChange={toggleShowInactive}
                                    data-testid="show-inactive-toggle"
                                >
                                    Show Inactive Teams
                                </DropdownMenuCheckboxItem>
                            )}
                            {isAdmin && (
                                <>
                                    {canToggleInactive && <DropdownMenuSeparator />}
                                    <DropdownMenuCheckboxItem
                                        checked={showAllTeams}
                                        onCheckedChange={toggleShowAllTeams}
                                        data-testid="show-all-teams-toggle"
                                    >
                                        Show All Teams
                                    </DropdownMenuCheckboxItem>
                                </>
                            )}
                        </DropdownMenuContent>
                    </DropdownMenu>
                )}
            </div>

            {loading ? (
                <div className="flex items-center justify-center h-32">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" role="status" />
                </div>
            ) : (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {displayTeams.map((team) => (
                        <TeamCard
                            key={team.id}
                            team={team}
                            onClick={handleTeamClick}
                        />
                    ))}
                </div>
            )}
        </div>
    );
};

export default Teams;