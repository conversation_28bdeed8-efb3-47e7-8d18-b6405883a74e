import { useParams, useNavigate } from 'react-router-dom';
import { useTeamsStore } from '../store/teamsStore';
import { TeamKanbanBoard } from './TeamKanban';
import { ArrowLeft } from 'lucide-react';
import { TeamHealthMetricsSummary } from '@/features/health-metrics';

const TeamDetail = () => {
    const { teamId } = useParams();
    const navigate = useNavigate();
    const { teams } = useTeamsStore();
    const team = teams.find(t => t.id === teamId);

    if (!team) {
        return (
            <div className="p-8 space-y-4">
                <div className="flex items-center space-x-4">
                    <button
                        onClick={() => navigate('/teams')}
                        className="p-2 hover:bg-accent rounded-full transition-colors"
                        aria-label="Go back to teams"
                    >
                        <ArrowLeft className="h-6 w-6" />
                    </button>
                    <h2 className="text-2xl font-bold">Team not found</h2>
                </div>
                <p className="text-muted-foreground">
                    The team you're looking for doesn't exist or you don't have access to it.
                </p>
            </div>
        );
    }

    return (
        <div className="p-8 space-y-6">
            <div className="flex items-center space-x-4">
                <button
                    onClick={() => navigate('/teams')}
                    className="p-2 hover:bg-accent rounded-full transition-colors"
                    aria-label="Go back to teams"
                >
                    <ArrowLeft className="h-6 w-6" />
                </button>
                <div>
                    <h2 className="text-2xl font-semibold tracking-tight">{team.name}</h2>
                    <p className="text-muted-foreground">
                        {team.description}
                    </p>
                </div>
            </div>

            {teamId && (
                <>
                    <TeamHealthMetricsSummary teamId={teamId} />
                    <TeamKanbanBoard teamId={teamId} />
                </>
            )}
        </div>
    );
};

export default TeamDetail;