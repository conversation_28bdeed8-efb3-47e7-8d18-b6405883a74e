import { useNavigate } from 'react-router-dom';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Target } from 'lucide-react';
import type { Team } from '../types';
import { cn } from '@/lib/utils';

interface TeamCardProps {
  team: Team;
  onClick?: (team: Team) => void;
}

export const TeamCard = ({ team, onClick }: TeamCardProps) => {
  const navigate = useNavigate();

  const handleClick = () => {
    if (onClick) {
      onClick(team);
    } else {
      navigate(`/team/${team.id}`);
    }
  };

  const isInactive = team.status === 'Inactive';

  return (
    <Card
      className={cn(
        "hover:shadow-md transition-shadow cursor-pointer",
        isInactive && "opacity-60"
      )}
      onClick={handleClick}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg font-semibold line-clamp-1">
            {team.name}
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Target className="h-4 w-4" />
          <p className="line-clamp-2">{team.description || 'No objective set'}</p>
        </div>
      </CardContent>
    </Card>
  );
};