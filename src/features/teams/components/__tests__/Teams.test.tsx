import { render, screen, act, waitFor, cleanup } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { BrowserRouter } from 'react-router-dom';
import Teams from '../Teams';
import { useTeamsStore } from '../../store/teamsStore';
import { supabase } from '@/lib/supabase';
import { Team, TeamStatus } from '../../types';
import { PostgrestQueryBuilder } from '@supabase/postgrest-js';

// Define the mock store type
interface TeamsState {
    teams: Team[];
    loading: boolean;
    error: string | null;
    showInactive: boolean;
    canToggleInactive: boolean;
    toggleShowInactive: () => void;
    fetchTeams: () => Promise<void>;
}

interface MockTeamsStore extends TeamsState {
    getState: () => TeamsState;
    setState: (newState: Partial<TeamsState>) => void;
}

// Mock the store
vi.mock('../../store/teamsStore', () => {
    const mockState: TeamsState = {
        teams: [],
        loading: false,
        error: null,
        showInactive: false,
        canToggleInactive: true,
        toggleShowInactive: vi.fn(),
        fetchTeams: vi.fn().mockResolvedValue(undefined)
    };

    const mockStore = {
        ...mockState,
        getState: () => mockState,
        setState: (newState: Partial<TeamsState>) => Object.assign(mockState, newState)
    };

    return {
        useTeamsStore: vi.fn(() => mockStore)
    };
});

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
    supabase: {
        auth: {
            getSession: vi.fn().mockResolvedValue({
                data: {
                    session: {
                        user: {
                            id: 'test-user-id'
                        }
                    }
                },
                error: null
            })
        },
        from: vi.fn().mockReturnValue({
            select: vi.fn().mockReturnValue({
                eq: vi.fn().mockResolvedValue({
                    data: { role: 'user' },
                    error: null
                }),
                single: vi.fn().mockResolvedValue({
                    data: { role: 'user' },
                    error: null
                })
            }),
            insert: vi.fn(),
            upsert: vi.fn(),
            update: vi.fn(),
            delete: vi.fn(),
            url: new URL('http://localhost'),
            headers: {}
        } as unknown as PostgrestQueryBuilder<any, any, any>)
    }
}));

const mockTeams: Team[] = [
    {
        id: '1',
        name: 'Active Team',
        description: 'Active team description',
        status: 'Active' as TeamStatus,
        lead: {
            id: 'lead-1',
            name: 'Team Lead',
            avatar: 'https://example.com/avatar.jpg'
        },
        teamMembers: [],
        progress: 0,
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        goals: []
    },
    {
        id: '2',
        name: 'Inactive Team',
        description: 'Inactive team description',
        status: 'Inactive' as TeamStatus,
        lead: {
            id: 'lead-2',
            name: 'Team Lead',
            avatar: 'https://example.com/avatar.jpg'
        },
        teamMembers: [],
        progress: 0,
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        goals: []
    }
];

const renderTeams = () => {
    return render(
        <BrowserRouter>
            <Teams />
        </BrowserRouter>
    );
};

describe('Teams Settings Menu', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        document.body.style.pointerEvents = 'auto';
        const mockState: TeamsState = {
            teams: mockTeams,
            loading: false,
            error: null,
            showInactive: false,
            canToggleInactive: true,
            toggleShowInactive: vi.fn(),
            fetchTeams: vi.fn().mockResolvedValue(undefined)
        };

        const mockStore = {
            ...mockState,
            getState: () => mockState,
            setState: (newState: Partial<TeamsState>) => Object.assign(mockState, newState)
        };

        vi.mocked(useTeamsStore).mockReturnValue(mockStore);
    });

    afterEach(() => {
        vi.clearAllMocks();
        document.body.style.pointerEvents = '';
        cleanup();
    });

    // UI Rendering Tests
    it('should render settings button with gear icon', async () => {
        const { getByTestId } = renderTeams();

        await waitFor(() => {
            const settingsButton = getByTestId('settings-button');
            expect(settingsButton).toBeInTheDocument();
            expect(settingsButton.querySelector('svg')).toBeInTheDocument(); // Gear icon
        });
    });

    it('should not show settings menu by default', async () => {
        const { queryByRole } = renderTeams();
        await waitFor(() => {
            const settingsMenu = queryByRole('menu');
            expect(settingsMenu).not.toBeInTheDocument();
        });
    });

    // Interaction Tests
    it('should open settings menu when clicking settings button', async () => {
        const user = userEvent.setup();
        const { getByTestId } = renderTeams();

        await waitFor(async () => {
            const settingsButton = getByTestId('settings-button');
            await user.click(settingsButton);
            const menuItem = getByTestId('show-inactive-toggle');
            expect(menuItem).toBeInTheDocument();
        });
    });

    it('should toggle show inactive teams when clicking menu item', async () => {
        const user = userEvent.setup();
        const mockToggleShowInactive = vi.fn();
        const mockState: TeamsState = {
            teams: mockTeams,
            loading: false,
            error: null,
            showInactive: false,
            canToggleInactive: true,
            toggleShowInactive: mockToggleShowInactive,
            fetchTeams: vi.fn().mockResolvedValue(undefined)
        };

        const mockStore = {
            ...mockState,
            getState: () => mockState,
            setState: (newState: Partial<TeamsState>) => Object.assign(mockState, newState)
        };

        vi.mocked(useTeamsStore).mockReturnValue(mockStore);
        const { getByTestId, getByRole } = renderTeams();

        // Ensure pointer events are enabled
        document.body.style.pointerEvents = 'auto';

        // Click the settings button and wait for the menu to be visible
        const settingsButton = getByTestId('settings-button');
        await user.click(settingsButton);

        // Wait for the menu to be visible and find the toggle
        await waitFor(() => {
            const menu = getByRole('menu');
            expect(menu).toBeInTheDocument();
        });

        // Find and click the toggle
        const menuItem = getByTestId('show-inactive-toggle');
        await user.click(menuItem);

        // Verify the toggle was called
        expect(mockToggleShowInactive).toHaveBeenCalledTimes(1);
    });

    it('should reflect current showInactive state in menu item', async () => {
        const user = userEvent.setup();
        const mockState: TeamsState = {
            teams: mockTeams,
            loading: false,
            error: null,
            showInactive: true,
            canToggleInactive: true,
            toggleShowInactive: vi.fn(),
            fetchTeams: vi.fn()
        };

        const mockStore = {
            ...mockState,
            getState: () => mockState,
            setState: (newState: Partial<TeamsState>) => Object.assign(mockState, newState)
        } as unknown as MockTeamsStore;

        vi.mocked(useTeamsStore).mockReturnValue(mockStore);
        const { getByTestId } = renderTeams();

        await waitFor(async () => {
            const settingsButton = getByTestId('settings-button');
            await user.click(settingsButton);

            const menuItem = getByTestId('show-inactive-toggle');
            expect(menuItem).toHaveAttribute('data-state', 'checked');
        });
    });

    // Accessibility Tests
    it('should be keyboard accessible', async () => {
        const user = userEvent.setup();
        const { getByTestId } = renderTeams();

        const mockToggle = vi.fn();
        const mockStore = {
            teams: mockTeams,
            loading: false,
            error: null,
            showInactive: false,
            canToggleInactive: true,
            toggleShowInactive: mockToggle,
            fetchTeams: vi.fn().mockResolvedValue(undefined),
            getState: vi.fn()
        };

        vi.mocked(useTeamsStore).mockReturnValue(mockStore);

        await waitFor(async () => {
            const settingsButton = getByTestId('settings-button');
            await act(async () => {
                settingsButton.focus();
                await user.keyboard('{Enter}');
            });

            const toggleMenuItem = getByTestId('show-inactive-toggle');
            await act(async () => {
                await user.keyboard('{ArrowDown}');
                expect(toggleMenuItem).toHaveFocus();
                await user.keyboard('{Enter}');
                expect(mockToggle).toHaveBeenCalled();
            });
        });
    });

    it('should maintain menu state when toggling teams visibility', async () => {
        const user = userEvent.setup();
        const { getByTestId } = renderTeams();

        const mockToggle = vi.fn();
        const mockStore = {
            teams: mockTeams,
            loading: false,
            error: null,
            showInactive: false,
            canToggleInactive: true,
            toggleShowInactive: mockToggle,
            fetchTeams: vi.fn().mockResolvedValue(undefined),
            getState: vi.fn()
        };

        vi.mocked(useTeamsStore).mockReturnValue(mockStore);

        await waitFor(async () => {
            const settingsButton = getByTestId('settings-button');
            await user.click(settingsButton);
            const menuItem = getByTestId('show-inactive-toggle');
            expect(menuItem).toBeInTheDocument();
        });
    });

    it('should handle rapid toggle clicks correctly', async () => {
        const user = userEvent.setup();
        const { getByTestId } = renderTeams();

        const mockToggle = vi.fn();
        const mockStore = {
            teams: mockTeams,
            loading: false,
            error: null,
            showInactive: false,
            canToggleInactive: true,
            toggleShowInactive: mockToggle,
            fetchTeams: vi.fn().mockResolvedValue(undefined),
            getState: vi.fn()
        };

        vi.mocked(useTeamsStore).mockReturnValue(mockStore);

        await waitFor(async () => {
            const settingsButton = getByTestId('settings-button');
            await user.click(settingsButton);
            const menuItem = getByTestId('show-inactive-toggle');
            await user.click(menuItem);
            await user.click(menuItem);
            expect(mockToggle).toHaveBeenCalledTimes(2);
        });
    });
});

describe('Role-based Access Control', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        document.body.style.pointerEvents = 'auto';
    });

    it('should display all teams for admin user', async () => {
        const mockState: TeamsState = {
            teams: mockTeams,
            loading: false,
            error: null,
            showInactive: true,
            canToggleInactive: true,
            toggleShowInactive: vi.fn(),
            fetchTeams: vi.fn().mockResolvedValue(undefined)
        };

        const mockStore = {
            ...mockState,
            getState: () => mockState,
            setState: (newState: Partial<TeamsState>) => Object.assign(mockState, newState)
        } as unknown as MockTeamsStore;

        vi.mocked(useTeamsStore).mockReturnValue(mockStore);
        const { getByTestId } = renderTeams();

        await waitFor(() => {
            const container = getByTestId('teams-container');
            expect(container).toBeInTheDocument();
            const teamHeadings = container.querySelectorAll('h3');
            expect(teamHeadings).toHaveLength(2);
            expect(teamHeadings[0]).toHaveTextContent('Active Team');
            expect(teamHeadings[1]).toHaveTextContent('Inactive Team');
        });
    });

    it('should only display member teams for non-admin user', async () => {
        const mockState: TeamsState = {
            teams: [mockTeams[0]], // Only active team
            loading: false,
            error: null,
            showInactive: false,
            canToggleInactive: false,
            toggleShowInactive: vi.fn(),
            fetchTeams: vi.fn().mockResolvedValue(undefined)
        };

        const mockStore = {
            ...mockState,
            getState: () => mockState,
            setState: (newState: Partial<TeamsState>) => Object.assign(mockState, newState)
        } as unknown as MockTeamsStore;

        vi.mocked(useTeamsStore).mockReturnValue(mockStore);
        const { getByTestId } = renderTeams();

        await waitFor(() => {
            const container = getByTestId('teams-container');
            expect(container).toBeInTheDocument();
            const teamHeadings = container.querySelectorAll('h3');
            expect(teamHeadings).toHaveLength(1);
            expect(teamHeadings[0]).toHaveTextContent('Active Team');
        });
    });
});

it('should show loading state while fetching teams', async () => {
    const mockState: TeamsState = {
        teams: [],
        loading: true,
        error: null,
        showInactive: false,
        canToggleInactive: true,
        toggleShowInactive: vi.fn(),
        fetchTeams: vi.fn()
    };

    const mockStore = {
        ...mockState,
        getState: () => mockState,
        setState: (newState: Partial<TeamsState>) => Object.assign(mockState, newState)
    } as unknown as MockTeamsStore;

    vi.mocked(useTeamsStore).mockReturnValue(mockStore);
    renderTeams();

    expect(screen.getByRole('status')).toBeInTheDocument();
});

it('should show error message when teams fetch fails', async () => {
    const mockState: TeamsState = {
        teams: [],
        loading: false,
        error: 'Failed to fetch teams',
        showInactive: false,
        canToggleInactive: true,
        toggleShowInactive: vi.fn(),
        fetchTeams: vi.fn()
    };

    const mockStore = {
        ...mockState,
        getState: () => mockState,
        setState: (newState: Partial<TeamsState>) => Object.assign(mockState, newState)
    } as unknown as MockTeamsStore;

    vi.mocked(useTeamsStore).mockReturnValue(mockStore);
    renderTeams();

    expect(screen.getByText(/Failed to fetch teams/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
});

it('should show authentication error when session is missing', async () => {
    const mockState: TeamsState = {
        teams: [],
        loading: false,
        error: null,
        showInactive: false,
        canToggleInactive: true,
        toggleShowInactive: vi.fn(),
        fetchTeams: vi.fn()
    };

    const mockStore = {
        ...mockState,
        getState: () => mockState,
        setState: (newState: Partial<TeamsState>) => Object.assign(mockState, newState)
    } as unknown as MockTeamsStore;

    vi.mocked(useTeamsStore).mockReturnValue(mockStore);
    vi.mocked(supabase.auth.getSession).mockResolvedValue({ data: { session: null }, error: null });

    renderTeams();

    await waitFor(() => {
        expect(screen.getByText(/Please sign in to access team data./i)).toBeInTheDocument();
    });
}); 