import React from 'react';
import { TeamKanbanBoard } from './components/TeamKanbanBoard';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { Button } from '@/components/ui/button';
import { RotateCcw } from 'lucide-react';
import { errorHandler, ErrorType } from '@/utils/errorHandler';

interface TeamKanbanProps {
    teamId: string;
}

/**
 * TeamKanban component with error boundary
 * 
 * This component wraps the TeamKanbanBoard with an error boundary
 * to gracefully handle any uncaught errors
 */
export const TeamKanban = ({ teamId }: TeamKanbanProps) => {
    return (
        <ErrorBoundary
            componentName="TeamKanban"
            fallbackRender={({ error, resetErrorBoundary }) => (
                <div className="h-[calc(100vh-12rem)] flex items-center justify-center">
                    <div className="max-w-md p-6 rounded-lg border bg-card text-center space-y-4">
                        <h3 className="text-lg font-semibold">Error Loading Kanban Board</h3>
                        <p className="text-sm text-muted-foreground">
                            {errorHandler.getUserFriendlyMessage(error, ErrorType.UNKNOWN, {
                                component: 'TeamKanban',
                            })}
                        </p>
                        <div className="flex justify-center mt-4">
                            <Button
                                variant="default"
                                onClick={resetErrorBoundary}
                                className="flex items-center gap-2"
                            >
                                <RotateCcw className="h-4 w-4" />
                                Retry
                            </Button>
                        </div>
                    </div>
                </div>
            )}
            onError={(error, info) => {
                // Log error to the errorHandler with additional context
                errorHandler.captureError(error, ErrorType.UNKNOWN, {
                    component: 'TeamKanban',
                    action: 'render',
                    context: {
                        teamId,
                        componentStack: info.componentStack,
                    },
                });
            }}
        >
            <TeamKanbanBoard teamId={teamId} />
        </ErrorBoundary>
    );
};

export default TeamKanban; 