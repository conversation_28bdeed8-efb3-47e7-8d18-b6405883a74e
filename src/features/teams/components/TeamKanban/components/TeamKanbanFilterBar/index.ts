import { TeamKanbanQuickFilters } from '../TeamKanbanQuickFilters';
import { withErrorBoundary } from '@/components/ui/error-boundary';
import { errorHandler, ErrorType } from '@/utils/errorHandler';

// Export the original component for testing and direct use
export { TeamKanbanQuickFilters };

// Export a wrapped version as the default export
export default withErrorBoundary(TeamKanbanQuickFilters, {
    componentName: 'TeamKanbanQuickFilters',
    onError: (error, info) => {
        errorHandler.captureError(error, ErrorType.UNKNOWN, {
            component: 'TeamKanbanQuickFilters',
            action: 'render',
            context: { componentStack: info.componentStack }
        });
    }
}); 