import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { TeamKanbanCard } from './TeamKanbanCard';
import { Plus, RotateCcw } from 'lucide-react';
import { TeamCardDetailModalWithErrorBoundary } from './TeamCardDetailModalWithErrorBoundary';
import type { TeamKanbanCard as TeamKanbanCardType } from '@/features/teams/types/kanban';
import type { TeamKanbanCard as TeamKanbanCardIndexType, PriorityLevel } from '@/features/teams/types';
import type { TeamKanbanStore } from '@/features/teams/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useKanbanStore, kanbanSelectors, useTeamKanbanStore } from '@/features/teams/store/teamKanbanStore';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/features/auth';
import { ShimmerCard } from '@/components/ui/shimmer';
import {
    DndContext,
    DragOverlay,
    useSensors,
    useSensor,
    DragStartEvent,
    DragEndEvent,
    MouseSensor,
    TouchSensor,
    closestCorners,
} from '@dnd-kit/core';
import { SortableContext, horizontalListSortingStrategy } from '@dnd-kit/sortable';
import { asKanbanCard } from '@/features/teams/utils/typeAdapters';
import { errorHandler, ErrorType } from '@/utils/errorHandler';
import { TeamKanbanQuickFilters } from './TeamKanbanQuickFilters';
import { logger } from '@/utils/logger';
import { TeamKanbanColumnWithErrorBoundary } from './TeamKanbanColumnWithErrorBoundary';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { SortableColumn } from './SortableColumn';
import { TeamKanbanColumn } from '@/features/teams/types/kanban';

interface TeamKanbanBoardProps {
    teamId: string;
}

// Simple UUID validation function
const isValidUUID = (str: string): boolean => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
};

// Type conversion utility for handling inconsistent TeamKanbanCard types in the codebase
// This is a temporary solution until types can be properly unified
const convertCardType = (card: TeamKanbanCardType): TeamKanbanCardIndexType => {
    // This explicit conversion ensures type safety while acknowledging the structural differences
    return card as unknown as TeamKanbanCardIndexType;
};

// Helper to safely attempt initialization with the store
const safelyInitializeStore = async (store: TeamKanbanStore, teamId: string): Promise<void> => {
    // First, try the standard initialization methods
    if ('initializeStore' in store && typeof store.initializeStore === 'function') {
        await store.initializeStore(teamId);
        return;
    }

    // Fall back to individual initialization methods
    if ('fetchBoard' in store && typeof store.fetchBoard === 'function') {
        await store.fetchBoard(teamId);

        // Also try to fetch team members if available
        if ('fetchTeamMembers' in store && typeof store.fetchTeamMembers === 'function') {
            try {
                await store.fetchTeamMembers(teamId);
            } catch (_error) {
                // Silently handle team members fetch error and continue
            }
        }
        return;
    }

    throw new Error('No initialization method available in the store');
};

export const TeamKanbanBoard = React.memo(({ teamId }: TeamKanbanBoardProps) => {
    const { user } = useAuthStore();
    const [error, setError] = useState<Error | null>(null);

    // Use the store with selectors
    const columns = useKanbanStore(kanbanSelectors.selectColumns);
    const columnData = useKanbanStore(state => state.columnData);
    const teamMembers = useKanbanStore(state => state.teamMembers[teamId] || []);
    const loadingTeamMembers = useKanbanStore(state => state.loadingTeamMembers[teamId] || false);

    // Fallback: If no team members are found, include the current user as an option
    const availableAssignees = useMemo(() => {
        if (teamMembers.length > 0) {
            return teamMembers;
        }

        // If no team members, at least include the current user
        if (user) {
            return [{
                id: user.id,
                name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'Current User',
                email: user.email || '',
                avatar: user.user_metadata?.avatar_url || `https://api.dicebear.com/7.x/initials/svg?seed=${user.email}`
            }];
        }

        return [];
    }, [teamMembers, user]);

    // Access store functions directly
    const addColumn = useKanbanStore(state => state.addColumn);
    const updateColumn = useKanbanStore(state => state.updateColumn);
    const deleteColumn = useKanbanStore(state => state.deleteColumn);
    const toggleColumnArchived = useKanbanStore(state => state.toggleColumnArchived);
    const moveCard = useKanbanStore(state => state.moveCard);
    const addCard = useKanbanStore(state => state.addCard) as (
        columnId: string,
        teamId: string,
        title: string,
        description?: string,
        assigneeId?: string,
        priority?: PriorityLevel
    ) => Promise<void>;

    // Local state for component-specific UI features (not filter state)
    const [selectedCard, setSelectedCard] = useState<TeamKanbanCardType | null>(null);
    const [isNewColumnModalOpen, setIsNewColumnModalOpen] = useState(false);
    const [newColumnTitle, setNewColumnTitle] = useState('');
    const [initializing, setInitializing] = useState(true);
    const [userRole, setUserRole] = useState<string | null>(null);
    const initializationRef = useRef<{ [key: string]: boolean }>({});
    const [activeCard, setActiveCard] = useState<TeamKanbanCardType | null>(null);
    const [isTeamCreator, setIsTeamCreator] = useState(false);
    const [activeColumn, setActiveColumn] = useState<TeamKanbanColumn | null>(null);

    // Add reorderColumns function from the store
    const reorderColumns = useKanbanStore(state => state.reorderColumns);

    // Improve DnD sensor configuration
    const sensors = useSensors(
        useSensor(MouseSensor, {
            activationConstraint: {
                distance: 10, // Keep a small threshold for drag start
            },
        }),
        useSensor(TouchSensor, {
            activationConstraint: {
                delay: 250, // Reasonable delay for touch devices
                tolerance: 5, // Small tolerance helps with accidental drags
            },
        })
    );

    // Simplified initialization logic
    useEffect(() => {
        let mounted = true;

        const initializeBoard = async () => {
            if (initializationRef.current[teamId]) return;

            try {
                if (!user?.id) throw new Error('User not authenticated');

                // Fetch role first
                const { data: roleData, error: roleError } = await supabase
                    .from('profiles')
                    .select('role')
                    .eq('id', user.id)
                    .single();

                if (roleError) throw roleError;
                if (!mounted) return;

                setUserRole(roleData?.role || null);

                // Check if user is team creator
                const { data: teamData, error: teamError } = await supabase
                    .from('teams')
                    .select('created_by')
                    .eq('id', teamId)
                    .single();

                if (!teamError && teamData) {
                    setIsTeamCreator(teamData.created_by === user.id);
                }

                // Get store reference with a more robust approach
                const store = useTeamKanbanStore.getState();

                // Enhanced error handling for store initialization
                try {
                    await safelyInitializeStore(store, teamId);
                    initializationRef.current[teamId] = true;
                } catch (err) {
                    logger.error('Board initialization failed:', err);

                    // Try one more time with direct method access
                    try {
                        logger.info('Attempting alternative initialization approach...');

                        // Try using a fresh store instance
                        const freshStore = useTeamKanbanStore.getState();

                        if (typeof freshStore.fetchBoard === 'function') {
                            await freshStore.fetchBoard(teamId);
                            logger.info('Alternative initialization succeeded');
                            initializationRef.current[teamId] = true;
                        } else {
                            throw new Error('Alternative initialization failed - methods still not available');
                        }
                    } catch (retryErr) {
                        logger.error('All initialization attempts failed:', retryErr);
                        setError(retryErr instanceof Error ? retryErr : new Error(String(retryErr)));
                        initializationRef.current[teamId] = false;
                    }
                }
            } catch (err) {
                logger.error('Error initializing board:', err);
                if (mounted) {
                    setError(err instanceof Error ? err : new Error(String(err)));
                    initializationRef.current[teamId] = false;
                }
            } finally {
                if (mounted) setInitializing(false);
            }
        };

        // Small delay to ensure component is fully mounted
        const initializationTimeout = setTimeout(initializeBoard, 100);

        return () => {
            mounted = false;
            clearTimeout(initializationTimeout);
        };
    }, [teamId, user?.id]);

    // Memoize all handlers to prevent unnecessary re-renders
    const handleCardClick = useCallback((card: TeamKanbanCardType) => {
        setSelectedCard(card);
    }, []);

    const handleAddCard = useCallback((columnId: string, title: string, assigneeId?: string, priority?: PriorityLevel) => {
        addCard(columnId, teamId, title, undefined, assigneeId, priority);
    }, [addCard, teamId]);

    const handleDeleteColumn = useCallback((columnId: string) => {
        deleteColumn(columnId);
    }, [deleteColumn]);

    const handleUpdateColumnTitle = useCallback((columnId: string, title: string) => {
        updateColumn(columnId, title);
    }, [updateColumn]);

    const handleAddColumn = useCallback(async () => {
        if (!newColumnTitle.trim()) return;
        await addColumn(teamId, newColumnTitle);
        setNewColumnTitle('');
        setIsNewColumnModalOpen(false);
    }, [addColumn, teamId, newColumnTitle]);

    // Improved DnD handlers
    const handleDragStart = useCallback((event: DragStartEvent) => {
        const { active } = event;
        const activeId = active.id.toString();

        // Handle column drag
        if (activeId.startsWith('column-')) {
            const columnId = activeId.replace('column-', '');
            const column = columns.find(col => col.id === columnId);
            if (column) {
                setActiveColumn(column);
                return;
            }
        }

        // Handle card drag (existing code)
        for (const columnId of Array.from(columnData.keys())) {
            const columnCards = columnData.get(columnId)?.cards || [];
            const draggedCard = columnCards.find(card => card.id === activeId);
            if (draggedCard) {
                setActiveCard(asKanbanCard(draggedCard));
                break;
            }
        }
    }, [columnData, columns]);

    const handleDragEnd = useCallback(async (event: DragEndEvent) => {
        setActiveCard(null);
        setActiveColumn(null);

        if (!event.active || !event.over) return;

        const { active, over } = event;
        const activeId = active.id.toString();

        // Handle column reordering
        if (activeId.startsWith('column-')) {
            const sourceId = activeId.replace('column-', '');
            const targetId = over.id.toString().replace('column-', '');

            if (sourceId !== targetId) {
                // Get current column order
                const currentOrder = [...columns].sort((a, b) => a.order_index - b.order_index);
                const currentIds = currentOrder.map(col => col.id);

                // Create new order by moving source to target position
                const sourceIndex = currentIds.indexOf(sourceId);
                const targetIndex = currentIds.indexOf(targetId);

                if (sourceIndex !== -1 && targetIndex !== -1) {
                    try {
                        // Remove source from the array
                        const newOrder = [...currentIds];
                        newOrder.splice(sourceIndex, 1);

                        // Insert at the target position
                        newOrder.splice(targetIndex, 0, sourceId);

                        // Call the API with the new order
                        await reorderColumns(teamId, newOrder);
                    } catch (err) {
                        logger.error('Failed to reorder columns:', err);
                    }
                }
            }
            return;
        }

        // Continue with existing card drag logic
        // Look through all columns to find the card
        for (const columnId of Array.from(columnData.keys())) {
            const columnCards = columnData.get(columnId)?.cards || [];
            const draggedCard = columnCards.find(card => card.id === activeId);
            if (draggedCard) {
                // Extract the real column ID from the over ID
                let targetColumnId: string;

                // Check first if we have an actualColumnId in the data
                if (event.over.data.current?.actualColumnId) {
                    targetColumnId = event.over.data.current.actualColumnId;
                }
                // If dropping directly on a column
                else if (typeof event.over.id === 'string' && event.over.id.startsWith('column-')) {
                    targetColumnId = event.over.id.replace('column-', '');
                }
                // If dropping on a card within a column
                else if (event.over.data.current?.sortable?.containerId) {
                    // Extract the real column ID from the container ID
                    const containerId = event.over.data.current.sortable.containerId as string;
                    targetColumnId = containerId.replace('column-', '');

                    // Use the actualColumnId from parent column if available
                    const columnElement = document.querySelector(`[data-column-id="${targetColumnId}"]`);
                    if (columnElement && (columnElement as HTMLElement).dataset.actualColumnId) {
                        const actualId = (columnElement as HTMLElement).dataset.actualColumnId;
                        if (actualId) {
                            targetColumnId = actualId;
                        }
                    }
                }
                // If no valid target is found, use the current column
                else {
                    targetColumnId = draggedCard.column_id;
                }

                // Validate that targetColumnId is a valid UUID
                const isValidColumn = isValidUUID(targetColumnId) && columns.some(col => col.id === targetColumnId);
                if (!targetColumnId || !isValidColumn) {
                    logger.error('Target column not found or invalid', {
                        targetColumnId,
                        overID: event.over.id,
                        containerId: event.over.data.current?.sortable?.containerId,
                        columns: columns.map(c => c.id),
                        isValid: isValidColumn
                    });
                    return;
                }

                // Use simpler target index calculation
                let targetIndex = 0;

                if (typeof event.over.id === 'string' && event.over.id.startsWith('column-')) {
                    // Dropping directly on a column - add to the end
                    targetIndex = (columnData.get(targetColumnId)?.cards.length || 0);
                } else {
                    // Dropping on a card - use its index
                    const sortableData = event.over.data.current?.sortable;
                    if (sortableData) {
                        targetIndex = sortableData.index;

                        // Check if dropping below the target card's center
                        const overRect = event.over.rect;
                        const dragRect = active.rect.current;

                        if (dragRect.translated && overRect) {
                            const dragCenter = dragRect.translated.top + (dragRect.translated.height ?? 0) / 2;
                            const overCenter = overRect.top + overRect.height / 2;

                            if (dragCenter > overCenter) {
                                targetIndex += 1;
                            }
                        }
                    }
                }

                // Ensure target index is valid
                const targetColumnDataFromState = columnData.get(targetColumnId);
                const filteredCards = targetColumnDataFromState?.cards
                    .filter(c => c && !c.archived_at) || [];
                const maxIndex = filteredCards.length;
                targetIndex = Math.max(0, Math.min(targetIndex, maxIndex));

                // Execute the move using position_updated_at based ordering
                await moveCard(activeId, targetColumnId, targetIndex);
            }
        }
    }, [columnData, moveCard, columns, reorderColumns, teamId]);

    // Determine if the user can manage columns based on role or creator status
    const canManageColumns = useMemo(() =>
        ['admin', 'manager'].includes(userRole || '') || isTeamCreator,
        [userRole, isTeamCreator]
    );

    // Sortable columns with IDs for SortableContext
    const columnIds = useMemo(() =>
        columns.map(column => `column-${column.id}`),
        [columns]
    );

    // Optimized column rendering using useMemo with minimal dependencies
    const memoizedColumns = useMemo(() =>
        columns
            // No filtering needed as columns don't have archived status at column level
            .sort((a, b) => a.order_index - b.order_index)
            .map((column, index) => {
                return (
                    <SortableColumn
                        key={column.id}
                        id={column.id}
                        index={index}
                        title={column.title}
                        canManageColumn={canManageColumns}
                        onCardClick={handleCardClick}
                        onAddCard={(title, assigneeId, priority) => handleAddCard(column.id, title, assigneeId, priority)}
                        teamMembers={availableAssignees}
                        loadingTeamMembers={loadingTeamMembers}
                        onDeleteColumn={() => handleDeleteColumn(column.id)}
                        onUpdateTitle={(title) => handleUpdateColumnTitle(column.id, title)}
                        onToggleArchived={() => {
                            // Call the store function directly with explicit column ID
                            toggleColumnArchived(column.id);
                        }}
                    />
                );
            }),
        [columns, handleCardClick, handleAddCard, handleDeleteColumn, handleUpdateColumnTitle, toggleColumnArchived, canManageColumns, availableAssignees, loadingTeamMembers]
    );

    if (!user) {
        return (
            <div className="h-[calc(100vh-12rem)] flex items-center justify-center">
                <p>Please log in to access the board</p>
            </div>
        );
    }

    if (initializing) {
        return (
            <div className="h-[calc(100vh-12rem)] p-8">
                <div className="flex gap-6">
                    {[1, 2, 3].map((i) => (
                        <div key={i} className="w-80">
                            <ShimmerCard className="h-[calc(100vh-16rem)]" />
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="h-[calc(100vh-12rem)] flex items-center justify-center">
                <div className="text-center">
                    <p className="text-destructive mb-4">Error initializing board: {String(error)}</p>
                    <div className="flex flex-col gap-4 items-center">
                        <Button onClick={async () => {
                            setInitializing(true);

                            // Enhanced logging and debugging
                            const store = useTeamKanbanStore.getState();

                            try {
                                // Try direct method calls as a fallback if safelyInitializeStore fails
                                if (typeof store.fetchBoard === 'function') {
                                    await store.fetchBoard(teamId);
                                    logger.info('Retry successful with direct fetchBoard call');
                                    setError(null);
                                    setInitializing(false);
                                    return;
                                }

                                // If we can't find fetchBoard, try the helper function as usual
                                await safelyInitializeStore(store, teamId);
                                setError(null);
                            } catch (err) {
                                logger.error('Retry initialization failed:', err);
                                setError(new Error(String(err)));
                            } finally {
                                setInitializing(false);
                            }
                        }}>
                            <RotateCcw className="h-4 w-4 mr-2" />
                            Retry
                        </Button>

                        <div className="text-xs text-muted-foreground mt-2">
                            If the problem persists, try refreshing the page
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="h-[calc(100vh-12rem)] flex flex-col overflow-hidden">
            <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold">Execution Board</h2>
                <div className="flex space-x-2">
                    <TeamKanbanQuickFilters teamId={teamId} />
                </div>
            </div>

            <div className="flex-1 overflow-x-auto overflow-y-hidden">
                <DndContext
                    sensors={sensors}
                    collisionDetection={closestCorners}
                    onDragStart={handleDragStart}
                    onDragEnd={handleDragEnd}
                >
                    <div className="flex gap-1 px-1 h-full">
                        <SortableContext
                            items={columnIds}
                            strategy={horizontalListSortingStrategy}
                        >
                            {memoizedColumns}
                        </SortableContext>

                        {canManageColumns && (
                            <Button
                                variant="outline"
                                className="h-full w-[350px] border-dashed ml-1"
                                onClick={() => setIsNewColumnModalOpen(true)}
                            >
                                <Plus className="h-4 w-4 mr-2" />
                                Add Column
                            </Button>
                        )}
                    </div>
                    <DragOverlay>
                        {activeCard ? (
                            <div className="opacity-100">
                                <ErrorBoundary
                                    fallbackRender={() => (
                                        <div className="p-4 text-destructive">Error loading board</div>
                                    )}
                                    onError={(error: Error) => {
                                        errorHandler.captureError(error, ErrorType.UNKNOWN, {
                                            component: 'TeamKanbanBoard',
                                            teamId
                                        });
                                    }}
                                >
                                    <TeamKanbanCard
                                        item={activeCard}
                                        onClick={() => { }}
                                    />
                                </ErrorBoundary>
                            </div>
                        ) : activeColumn ? (
                            <div className="opacity-100">
                                <TeamKanbanColumnWithErrorBoundary
                                    id={activeColumn.id}
                                    title={activeColumn.title}
                                    onCardClick={() => { }}
                                    onAddCard={() => { }}
                                    onDeleteColumn={() => { }}
                                    onUpdateTitle={() => { }}
                                    canManageColumn={canManageColumns}
                                />
                            </div>
                        ) : null}
                    </DragOverlay>
                </DndContext>
            </div>

            <Dialog open={isNewColumnModalOpen} onOpenChange={setIsNewColumnModalOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Add New Column</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                        <Input
                            placeholder="Column title..."
                            value={newColumnTitle}
                            onChange={(e) => setNewColumnTitle(e.target.value)}
                        />
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsNewColumnModalOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={handleAddColumn}>Add Column</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {selectedCard && (
                <TeamCardDetailModalWithErrorBoundary
                    card={convertCardType(selectedCard)}
                    onClose={() => setSelectedCard(null)}
                />
            )}
        </div>
    );
}, (prevProps, nextProps) => prevProps.teamId === nextProps.teamId);