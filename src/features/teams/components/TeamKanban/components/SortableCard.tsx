import React, { useMemo } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { UnifiedKanbanCard } from '@/features/teams/types/unified';
import { cn } from '@/lib/utils';
import type { TeamKanbanCard as TeamKanbanCardType } from '@/features/teams/types/kanban';
import { TeamKanbanCard } from './TeamKanbanCard';

interface SortableCardProps {
    item: TeamKanbanCardType | UnifiedKanbanCard;
    onClick: () => void;
}

export const SortableCard = React.memo(({ item, onClick }: SortableCardProps) => {
    // Memoize the item ID to avoid re-renders when only non-ID properties change
    const itemId = useMemo(() => item.id, [item.id]);

    // Use useSortable with optimized options
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging
    } = useSortable({
        id: itemId,
        data: {
            id: itemId,
            type: 'card',
            item
        }
    });

    // Memoize style calculation to avoid recalculation on every render
    const style = useMemo(() => ({
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.4 : 1,
        zIndex: isDragging ? 10 : 'auto',
        position: 'relative' as const,
    }), [transform, transition, isDragging]);

    // Use a callback-based approach for event handling to prevent closure issues
    const handleCardClick = (e: React.MouseEvent) => {
        // Don't trigger card click when dragging
        if (isDragging) {
            return;
        }

        // Check for interactive elements - MUST match the class in TeamKanbanCard
        if (e.target instanceof HTMLElement) {
            // Don't propagate clicks from interactive elements (checkbox button)
            // This needs to match the className in the checkbox button
            const isInteractive = e.target.closest('.interactive-element') ||
                e.target.closest('[data-testid="card-checkbox-button"]');
            if (isInteractive) {
                e.stopPropagation(); // Additional protection
                return;
            }
        }

        onClick();
    };

    return (
        <div
            ref={setNodeRef}
            style={style}
            data-drag-handle
            {...attributes}
            {...listeners}
            onClick={handleCardClick}
            className={cn(
                'touch-none select-none mb-4',
                isDragging && 'opacity-0'
            )}
            data-testid="sortable-card"
        >
            <div
                className={cn(
                    isDragging && 'cursor-grabbing',
                    !isDragging && 'cursor-grab hover:bg-muted/50'
                )}
                // Extra protection against event propagation
                onClick={(e) => {
                    // Only handle clicks that aren't on interactive elements
                    if (e.target instanceof HTMLElement) {
                        const isInteractive = e.target.closest('.interactive-element') ||
                            e.target.closest('[data-testid="card-checkbox-button"]');
                        if (isInteractive) {
                            e.stopPropagation();
                        }
                    }
                }}
            >
                <TeamKanbanCard item={item} onClick={onClick} />
            </div>
        </div>
    );
}, (prevProps, nextProps) => {
    // Custom comparison function for memo
    // Only re-render if the ID changes or if specific important properties change
    return (
        prevProps.item.id === nextProps.item.id &&
        prevProps.item.title === nextProps.item.title &&
        prevProps.item.archived_at === nextProps.item.archived_at &&
        prevProps.item.priority === nextProps.item.priority &&
        prevProps.item.due_date === nextProps.item.due_date &&
        JSON.stringify(prevProps.item.assignee) === JSON.stringify(nextProps.item.assignee) &&
        JSON.stringify(prevProps.item.subtasks) === JSON.stringify(nextProps.item.subtasks)
    );
}); 