import React from 'react';
import { TeamKanbanCard as KanbanCardType } from '@/features/teams/types/kanban';
import { TeamKanbanCard as IndexCardType } from '@/features/teams/types';
import { asKanbanCard } from '@/features/teams/utils/typeAdapters';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

/**
 * Props for the TeamCardPreview component
 * This component demonstrates using type adapters to handle different card types
 * @interface TeamCardPreviewProps
 */
interface TeamCardPreviewProps {
    /**
     * The card data - can be any of the supported card types
     * Will be converted to UnifiedKanbanCard internally
     */
    card: KanbanCardType | IndexCardType;

    /**
     * Optional click handler
     */
    onClick?: () => void;

    /**
     * Optional className for styling
     */
    className?: string;
}

/**
 * A component that renders a preview of a team kanban card
 * Demonstrates the use of type adapters to handle different card types
 */
export const TeamCardPreview: React.FC<TeamCardPreviewProps> = ({
    card,
    onClick,
    className
}) => {
    // Convert any card type to the unified format for internal processing
    // Note: This is a placeholder for when asUnifiedCard is fully implemented
    // For now, we'll convert to KanbanCard type which we know works
    const unifiedCard = asKanbanCard(card);

    // Now we can safely work with the card in a consistent format
    const { title, priority, due_date, assignee, subtasks = [] } = unifiedCard;

    // Calculate task completion
    const completedTasks = subtasks.filter(task => task.is_completed).length;
    const totalTasks = subtasks.length;
    const progressPercentage = totalTasks > 0
        ? Math.round((completedTasks / totalTasks) * 100)
        : 0;

    // Priority styling
    const priorityStyles = {
        P1: 'bg-red-500 text-white',
        P2: 'bg-yellow-500 text-black',
        P3: 'bg-blue-500 text-white'
    };

    return (
        <Card
            className={cn("w-full max-w-md hover:shadow-md transition-shadow", className)}
            onClick={onClick}
        >
            <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                    <CardTitle className="text-base font-medium">{title}</CardTitle>
                    {priority && (
                        <Badge className={cn("ml-2", priorityStyles[priority])}>{priority}</Badge>
                    )}
                </div>
            </CardHeader>

            <CardContent>
                <div className="grid gap-2">
                    {/* Due date */}
                    {due_date && (
                        <div className="text-xs text-muted-foreground">
                            Due: {format(new Date(due_date), 'MMM d, yyyy')}
                        </div>
                    )}

                    {/* Task progress */}
                    {totalTasks > 0 && (
                        <div className="space-y-1">
                            <div className="flex justify-between text-xs">
                                <span>Progress</span>
                                <span>{completedTasks}/{totalTasks}</span>
                            </div>
                            <div className="h-1.5 w-full bg-muted rounded-full overflow-hidden">
                                <div
                                    className="h-full bg-primary transition-all duration-300"
                                    style={{ width: `${progressPercentage}%` }}
                                />
                            </div>
                        </div>
                    )}

                    {/* Assignee */}
                    {assignee && (
                        <div className="flex items-center justify-end mt-2">
                            <span className="text-xs text-muted-foreground mr-2">Assigned to:</span>
                            <Avatar className="h-6 w-6">
                                <AvatarImage src={assignee.avatar} />
                                <AvatarFallback>
                                    {assignee.name.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
                            </Avatar>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
};

export default TeamCardPreview; 