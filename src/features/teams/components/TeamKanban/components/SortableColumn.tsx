import React, { useMemo } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { cn } from '@/lib/utils';
import { TeamKanbanColumnWithErrorBoundary } from './TeamKanbanColumnWithErrorBoundary';
import { GripVertical } from 'lucide-react';
import type { TeamKanbanCard } from '@/features/teams/types/kanban';
import type { User } from '@/features/teams/types';
import { PriorityLevel } from '@/features/teams/types';

interface SortableColumnProps {
    id: string;
    title: string;
    index: number;
    canManageColumn: boolean;
    onCardClick: (card: TeamKanbanCard) => void;
    onAddCard: (title: string, assigneeId?: string, priority?: PriorityLevel) => void;
    onDeleteColumn: () => void;
    onUpdateTitle: (title: string) => void;
    onToggleArchived: () => void;
    teamMembers?: User[];
    loadingTeamMembers?: boolean;
}

export const SortableColumn = React.memo(({
    id,
    title,
    index,
    canManageColumn,
    onCardClick,
    onAddCard,
    onDeleteColumn,
    onUpdateTitle,
    onToggleArchived,
    teamMembers,
    loadingTeamMembers
}: SortableColumnProps) => {
    // Use sortable hook for columns with optimized options
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging
    } = useSortable({
        id: `column-${id}`,
        data: {
            id,
            type: 'column',
            index
        }
    });

    // Memoize style calculation
    const style = useMemo(() => ({
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.4 : 1,
        zIndex: isDragging ? 10 : 'auto',
        height: '100%'
    }), [transform, transition, isDragging]);

    return (
        <div
            ref={setNodeRef}
            style={style}
            className={cn(
                'touch-none select-none',
                isDragging && 'opacity-50'
            )}
            data-column-sortable
            data-column-id={id}
        >
            <div
                className={cn(
                    'h-full',
                    isDragging && 'border-2 border-primary',
                )}
                data-drag-container
            >
                <TeamKanbanColumnWithErrorBoundary
                    id={id}
                    title={title}
                    onCardClick={onCardClick}
                    onAddCard={onAddCard}
                    onDeleteColumn={onDeleteColumn}
                    onUpdateTitle={onUpdateTitle}
                    onToggleArchived={onToggleArchived}
                    canManageColumn={canManageColumn}
                    teamMembers={teamMembers}
                    loadingTeamMembers={loadingTeamMembers}
                    dragHandleProps={canManageColumn ? { ...attributes, ...listeners } : undefined}
                    dragHandleElement={canManageColumn ?
                        <GripVertical className="h-5 w-5 mr-2 text-muted-foreground cursor-grab" /> : undefined}
                />
            </div>
        </div>
    );
});