import React, { useCallback, useState, useMemo, useEffect, useRef } from 'react';
import type { TeamKanbanCard as TeamKanbanCardType } from '@/features/teams/types/kanban';
import { PriorityLevel } from '@/features/teams/types/kanban';
import { UnifiedKanbanCard } from '@/features/teams/types/unified';
import { asKanbanCard } from '@/features/teams/utils/typeAdapters';
import { Card, CardHeader } from '@/components/ui/card';
import { formatDistanceToNow, format, differenceInDays, startOfDay } from 'date-fns';
import { useKanbanStore, kanbanSelectors } from '@/features/teams/store/teamKanbanStore';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';
import {
    AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent,
    AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle
} from "@/components/ui/alert-dialog";
import { logger } from "@/utils/logger";

/**
 * Props for the TeamKanbanCard component
 * @interface TeamKanbanCardProps
 */
interface TeamKanbanCardProps {
    /**
     * The card item to display - can be either a kanban card type or a unified card type
     * Will be internally converted to kanban type
     */
    item: TeamKanbanCardType | UnifiedKanbanCard;
    /**
     * Click handler for the card
     */
    onClick: () => void;
}

// Memoized due date color function
const getDueDateColor = (dueDate: Date) => {
    const daysLeft = differenceInDays(startOfDay(dueDate), startOfDay(new Date()));
    if (daysLeft <= 0) return 'text-red-500 font-medium';
    if (daysLeft <= 2) return 'text-yellow-500 font-medium';
    return '';
};

export const TeamKanbanCardComponent = React.memo(({ item, onClick }: TeamKanbanCardProps) => {
    // Convert item to KanbanCardType for internal use
    const card = asKanbanCard(item);

    // Use the store with selectors
    const users = useKanbanStore(kanbanSelectors.selectUsers);
    const deleteCard = useKanbanStore(state => state.deleteCard);
    const updateCard = useKanbanStore(state => state.updateCard);
    const fetchColumnCards = useKanbanStore(state => state.fetchColumnCards);
    // Remove unused filters variable
    // const filters = useKanbanStore(kanbanSelectors.selectFilters);
    // Get isColumnArchived state for the specific column
    const isColumnArchived = useKanbanStore(state =>
        card.column_id ? state.showArchivedColumns.has(card.column_id) : false
    );

    // Create ref for the checkbox button
    const checkboxRef = useRef<HTMLButtonElement>(null);

    // Local UI state for immediate feedback
    const [showDeleteAlert, setShowDeleteAlert] = useState(false);
    const [localIsCompleted, setLocalIsCompleted] = useState(!!card.archived_at);

    // Define the toggleComplete function before it's used in useEffect
    const toggleComplete = useCallback(() => {
        try {
            // Immediately update local state for UI responsiveness
            const newCompletedState = !localIsCompleted;
            setLocalIsCompleted(newCompletedState);

            // Update in the database in the background
            updateCard(card.id, {
                archived_at: newCompletedState ? new Date().toISOString() : null,
            })
                .then(() => {
                    // Still refresh the backend data, but don't rely on it for UI updates
                    if (card.column_id) {
                        // Pass the isColumnArchived state to ensure correct data is fetched
                        fetchColumnCards(card.column_id, 1, undefined, isColumnArchived);
                    }
                })
                .catch(error => {
                    // If there's an error, revert the local state change
                    setLocalIsCompleted(!newCompletedState);
                    logger.error('Failed to toggle card completion status', { error });
                });
        } catch (error) {
            logger.error('Error in toggleComplete function', { error });
        }
    }, [card.id, card.column_id, localIsCompleted, updateCard, fetchColumnCards, isColumnArchived]);

    // Calculate derived data
    const completedSubtasks = card.subtasks.filter(subtask => subtask.is_completed).length;
    const totalSubtasks = card.subtasks.length;
    const progress = totalSubtasks > 0 ? (completedSubtasks / totalSubtasks) * 100 : 0;

    // Get assignee details - safely handle null/undefined
    const assignee = useMemo(() => {
        return users?.find(u => u.id === card.assignee?.id) ?? null;
    }, [users, card.assignee?.id]);

    // Handle general click - don't click if the checkbox was clicked
    const handleClick = useCallback((e: React.MouseEvent) => {
        // Only proceed if the click was directly on the card, not on a checkbox
        if (e.defaultPrevented) return;
        if (e.target === checkboxRef.current) {
            e.stopPropagation();
            return;
        }
        onClick();
    }, [onClick]);

    // Handle delete with confirmation dialog
    const handleDelete = useCallback(() => {
        try {
            deleteCard(card.id)
                .catch(error => {
                    logger.error('Failed to delete card:', error);
                });
        } catch (error) {
            logger.error('Error in handleDelete function', { error });
        }
    }, [deleteCard, card.id]);

    // Handle checkbox button click - prevents propagation
    const handleCheckboxButtonClick = useCallback((e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        toggleComplete();
    }, [toggleComplete]);

    // Update local state when card changes in props to keep in sync
    useEffect(() => {
        setLocalIsCompleted(!!card.archived_at);
    }, [card.archived_at]);

    // Set up an effect to add a direct DOM click listener to ensure click capture
    useEffect(() => {
        const checkboxElement = checkboxRef.current;
        if (checkboxElement) {
            const handleDirectClick = (e: Event) => {
                e.stopPropagation();
                toggleComplete();
            };

            checkboxElement.addEventListener('click', handleDirectClick);

            return () => {
                checkboxElement.removeEventListener('click', handleDirectClick);
            };
        }
    }, [toggleComplete]); // Remove checkboxRef.current from deps and rely on toggleComplete

    // Early return after all hooks have been called
    if (!item) return null;

    // Derive values from props
    const currentPriority = card.priority;

    return (
        <>
            <div className="px-2 py-1.5 w-full">
                <Card
                    className={cn(
                        "border-none shadow-md hover:shadow-lg transition-shadow duration-200",
                        "cursor-pointer overflow-hidden",
                        localIsCompleted && "opacity-70"
                    )}
                    onClick={handleClick}
                >
                    <CardHeader className="p-3 space-y-2">
                        <div className="flex items-start gap-2">
                            <div className="flex-shrink-0 mt-[2px]">
                                <button
                                    ref={checkboxRef}
                                    className={cn(
                                        "w-5 h-5 rounded-full border-2 flex items-center justify-center",
                                        "hover:bg-muted transition-colors",
                                        localIsCompleted ? 'border-primary bg-primary' : 'border-muted-foreground/50'
                                    )}
                                    onClick={handleCheckboxButtonClick}
                                    aria-label={localIsCompleted ? "Mark as incomplete" : "Mark as complete"}
                                >
                                    {localIsCompleted && <Check className="w-3 h-3 text-primary-foreground" />}
                                </button>
                            </div>

                            <div className="flex-1 min-h-[2.5rem] flex items-start mt-[2px]">
                                <h4 className={cn(
                                    "font-medium text-sm line-clamp-2",
                                    localIsCompleted && "line-through text-muted-foreground"
                                )}>
                                    <span className={cn(
                                        "font-semibold mr-1",
                                        currentPriority === PriorityLevel.P1 && "text-red-500",
                                        currentPriority === PriorityLevel.P2 && "text-yellow-500",
                                        currentPriority === PriorityLevel.P3 && "text-gray-500"
                                    )}>
                                        {currentPriority}:
                                    </span>
                                    {card.title}
                                </h4>
                            </div>
                            {assignee && (
                                <Avatar className="w-6 h-6 flex-shrink-0 mt-[2px]">
                                    <AvatarImage src={assignee.avatar} />
                                    <AvatarFallback className="bg-background">
                                        {assignee.name.split(' ').map(n => n[0]).join('')}
                                    </AvatarFallback>
                                </Avatar>
                            )}
                        </div>
                        <div className="flex justify-between items-center text-xs text-muted-foreground min-h-[1.5rem]">
                            <span>
                                {localIsCompleted
                                    ? `Closed ${formatDistanceToNow(new Date(card.archived_at || new Date()))} ago`
                                    : `Open since ${formatDistanceToNow(new Date(card.created_at))} ago`
                                }
                            </span>
                            {card.due_date && (
                                <span className={cn(
                                    getDueDateColor(new Date(card.due_date)),
                                    localIsCompleted && "text-muted-foreground font-normal"
                                )}>
                                    Due {format(new Date(card.due_date), 'MMM d')}
                                </span>
                            )}
                        </div>

                        {/* Progress bar or placeholder to maintain consistent spacing */}
                        <div className="mt-3">
                            <div className="space-y-1.5">
                                <div className="flex justify-between text-xs text-muted-foreground">
                                    <span>Progress</span>
                                    <span>{completedSubtasks}/{totalSubtasks}</span>
                                </div>
                                <div className="h-1.5 bg-muted rounded-full overflow-hidden">
                                    <div
                                        className="h-full bg-primary transition-all duration-300"
                                        style={{ width: `${progress}%` }}
                                    />
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                </Card>
            </div>

            <AlertDialog open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Delete Card</AlertDialogTitle>
                        <AlertDialogDescription>
                            Are you sure you want to delete this card? This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDelete}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}, (prevProps, nextProps) => {
    // Custom comparison for memoization
    // Ensure we re-render when important properties change
    const prevCard = asKanbanCard(prevProps.item);
    const nextCard = asKanbanCard(nextProps.item);
    
    return (
        prevCard.id === nextCard.id &&
        prevCard.title === nextCard.title &&
        prevCard.priority === nextCard.priority &&
        prevCard.due_date === nextCard.due_date &&
        prevCard.archived_at === nextCard.archived_at &&
        JSON.stringify(prevCard.assignee?.id) === JSON.stringify(nextCard.assignee?.id) &&
        JSON.stringify(prevCard.subtasks) === JSON.stringify(nextCard.subtasks)
    );
});

export { TeamKanbanCardComponent as TeamKanbanCard }; 