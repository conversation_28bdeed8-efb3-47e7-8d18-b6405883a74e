import { useState, useEffect } from 'react';
import { TeamKanbanCard } from '@/features/teams/types';
import { useKanbanStore, useTeamKanbanStore } from '@/features/teams/store/teamKanbanStore';
import { logger } from '@/utils/logger';

export const useCardDetails = (passedCard: TeamKanbanCard) => {
  // Get card data from store using the passed card's ID and column ID
  const storeCard = useKanbanStore(state => {
    const columnData = state.columnData.get(passedCard.column_id);
    return columnData?.cards.find(c => c.id === passedCard.id);
  });

  // Use store card data if available, fallback to passed card
  const card = storeCard || passedCard;

  // Keep track of edited card
  const [editedCard, setEditedCard] = useState<TeamKanbanCard>(card);

  // Get showArchivedColumns to determine if we should show archived cards
  const showArchivedColumns = useKanbanStore(state => state.showArchivedColumns);
  const isColumnArchived = showArchivedColumns.has(card.column_id);

  // Card operations from store
  const updateCard = useKanbanStore(state => state.updateCard);
  const deleteCard = useKanbanStore(state => state.deleteCard);
  const fetchColumnCards = useKanbanStore(state => state.fetchColumnCards);

  // Update editedCard when store card changes
  useEffect(() => {
    if (storeCard) {
      setEditedCard(storeCard);
    }
  }, [storeCard]);

  // Function to update card with logging
  const updateCardWithLogging = async (cardId: string, updates: Partial<TeamKanbanCard>): Promise<TeamKanbanCard> => {
    try {
      // Make a clean copy without functions/circular refs
      const cleanUpdates = JSON.parse(JSON.stringify(updates));

      // Update the card in the store
      const result = await updateCard(cardId, cleanUpdates);

      // Refresh columns if needed
      if (updates.column_id && updates.column_id !== result.column_id) {
        // If the column changed, force a refresh of both columns
        await fetchColumnCards(result.column_id, 1, undefined, isColumnArchived);
        await fetchColumnCards(updates.column_id, 1, undefined, isColumnArchived);
      }

      return result;
    } catch (error) {
      logger.error('Card update failed:', error);
      throw error;
    }
  };

  // Toggle card completion (archive/unarchive)
  const handleToggleComplete = async () => {
    try {
      const updates: Partial<TeamKanbanCard> = {
        archived_at: editedCard.archived_at ? null : new Date().toISOString()
      };
      await updateCardWithLogging(editedCard.id, updates);
      setEditedCard(prev => ({ ...prev, ...updates }));
    } catch (error) {
      logger.error('Failed to toggle card completion:', error);
    }
  };

  // Update card details
  const handleUpdateCard = async (updates?: Partial<TeamKanbanCard>) => {
    try {
      let cardUpdate: Partial<TeamKanbanCard>;

      if (updates) {
        // If specific updates are provided, use only those
        cardUpdate = updates;
      } else {
        // If no updates are provided, it's likely a title or description update
        // Only include the fields that are commonly edited in the UI without parameters
        cardUpdate = {
          title: editedCard.title,
          description: editedCard.description
        };
      }

      // Perform the update
      const updatedCard = await updateCardWithLogging(editedCard.id, cardUpdate);

      // Update local state with response from server
      if (updatedCard) {
        // Preserve the current assignee if it exists in our local state
        // This prevents the assignee from being reset when the server response
        // might not include the complete assignee object
        const preservedAssignee = editedCard.assignee;

        setEditedCard(prev => ({
          ...prev,
          ...updatedCard,
          // Keep the current assignee if it exists
          assignee: preservedAssignee || updatedCard.assignee
        }));
      }
    } catch (error) {
      logger.error('Failed to update card:', error);
      throw error;
    }
  };

  // Update card due date
  const handleUpdateDueDate = async (date: Date | undefined) => {
    try {
      const updates = {
        due_date: date?.toISOString() || null
      };
      await updateCardWithLogging(editedCard.id, updates);
      setEditedCard(prev => ({
        ...prev,
        due_date: date?.toISOString() || null
      }));
      // Close the popover after selection
      const button = document.querySelector('[data-state="open"]');
      if (button) {
        (button as HTMLButtonElement).click();
      }
    } catch (error) {
      logger.error('Failed to update due date:', error);
    }
  };

  // Update card assignees
  const handleUpdateAssignees = async (selectedUsers: { id: string; name: string; avatar: string }[]) => {
    try {
      const newAssignee = selectedUsers.length > 0 ? {
        id: selectedUsers[0].id,
        name: selectedUsers[0].name,
        avatar: selectedUsers[0].avatar
      } : null;

      // First update the local state
      setEditedCard(prev => ({
        ...prev,
        assignee: newAssignee
      }));

      // Then update the server state
      await updateCardWithLogging(editedCard.id, { assignee: newAssignee });

      // Close the popover after successful update
      const button = document.querySelector('[data-state="open"]');
      if (button) {
        (button as HTMLButtonElement).click();
      }
    } catch (error) {
      logger.error('Failed to update assignee:', error);
      // Revert local state on error
      setEditedCard(prev => ({
        ...prev,
        assignee: editedCard.assignee
      }));
    }
  };

  // Function to handle modal close - selectively update just the current card
  const handleModalClose = () => {
    // Get the latest card data from store to ensure subtask progress is updated
    const currentStoreCard = useTeamKanbanStore.getState().cards.find((c: TeamKanbanCard) => c.id === card.id);

    if (currentStoreCard) {
      // Update the card in the store to ensure consistent state across UI
      const columnData = useTeamKanbanStore.getState().columnData.get(card.column_id);
      if (columnData) {
        const newColumnData = new Map(useTeamKanbanStore.getState().columnData);
        newColumnData.set(card.column_id, {
          ...columnData,
          cards: columnData.cards.map((c: TeamKanbanCard) => c.id === card.id ? currentStoreCard : c)
        });

        // Only update the specific card, not the entire column
        useTeamKanbanStore.setState({
          columnData: newColumnData,
          cards: useTeamKanbanStore.getState().cards.map((c: TeamKanbanCard) =>
            c.id === card.id ? currentStoreCard : c
          )
        });
      }
    }
  };

  // Delete card with confirmation
  const handleDeleteCard = async () => {
    try {
      await deleteCard(editedCard.id);
      // Refresh the column to update the UI
      await fetchColumnCards(editedCard.column_id, 1, undefined, isColumnArchived);
      return true;
    } catch (error) {
      logger.error('Failed to delete card:', error);
      return false;
    }
  };

  return {
    card,
    editedCard,
    setEditedCard,
    isColumnArchived,
    handleToggleComplete,
    handleUpdateCard,
    handleUpdateDueDate,
    handleUpdateAssignees,
    handleModalClose,
    handleDeleteCard,
    updateCardWithLogging
  };
};