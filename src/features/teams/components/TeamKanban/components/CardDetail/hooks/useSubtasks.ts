import { useState, useEffect, useMemo } from 'react';
import { useKanbanStore, useTeamKanbanStore } from '@/features/teams/store/teamKanbanStore';
import { TeamKanbanSubtask } from '@/features/teams/types';
import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';

export const calculateNewOrderIndex = (items: TeamKanbanSubtask[], targetIndex: number): number => {
  if (items.length === 0) return 1000;

  if (targetIndex === 0) {
    return Math.floor(items[0].order_index / 2);
  }

  if (targetIndex >= items.length) {
    return items[items.length - 1].order_index + 1000;
  }

  const prevItem = items[targetIndex - 1];
  const nextItem = items[targetIndex];
  return prevItem.order_index + Math.floor((nextItem.order_index - prevItem.order_index) / 2);
};

export const useSubtasks = (cardId: string) => {
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [isSubmittingSubtask, setIsSubmittingSubtask] = useState(false);
  const [newSubtaskTitle, setNewSubtaskTitle] = useState('');
  
  const subtasks = useKanbanStore(state => state.subtasks.filter(st => st.card_id === cardId));
  
  const addSubtask = useKanbanStore(state => state.addSubtask);
  const updateSubtask = useKanbanStore(state => state.updateSubtask);
  const deleteSubtask = useKanbanStore(state => state.deleteSubtask);
  const users = useKanbanStore(state => state.users);

  const subtaskProgress = useMemo(() => {
    const completedCount = subtasks.filter(subtask => subtask.is_completed).length;
    const totalCount = subtasks.length;
    const percentComplete = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

    return {
      completed: completedCount,
      total: totalCount,
      percent: percentComplete
    };
  }, [subtasks]);

  const orderedSubtasks = useMemo(() => {
    return [...subtasks].sort((a: TeamKanbanSubtask, b: TeamKanbanSubtask) => {
      if (a.is_completed !== b.is_completed) {
        return a.is_completed ? 1 : -1;
      }
      return a.order_index - b.order_index;
    });
  }, [subtasks]);

  const fetchCardSubtasks = async (cardId: string): Promise<TeamKanbanSubtask[]> => {
    logger.debug('Fetching subtasks for card:', cardId);
    try {
      const { data, error } = await supabase
        .from('kanban_subtasks')
        .select(`
          *,
          assignee:profiles!kanban_subtasks_assignee_id_fkey(*)
        `)
        .eq('card_id', cardId)
        .order('order_index');

      if (error) {
        logger.error('Error fetching subtasks:', error);
        throw error;
      }

      logger.debug(`Fetched ${data?.length || 0} subtasks for card ${cardId}`);

      const subtasks = data?.map(subtask => ({
        ...subtask,
        assignee: subtask.assignee ? {
          id: subtask.assignee.id,
          name: subtask.assignee.full_name || '',
          avatar: subtask.assignee.avatar_url || ''
        } : null
      })) || [];

      useTeamKanbanStore.setState(state => ({
        subtasks: [
          ...state.subtasks.filter(st => st.card_id !== cardId),
          ...subtasks
        ]
      }));

      return subtasks;
    } catch (error) {
      logger.error('Error fetching subtasks:', error);
      return [];
    }
  };

  useEffect(() => {
    const loadSubtasks = async () => {
      await fetchCardSubtasks(cardId);
      setInitialLoadComplete(true);
    };
    loadSubtasks();
  }, [cardId]);

  const handleAddSubtask = async () => {
    if (!newSubtaskTitle.trim() || isSubmittingSubtask) return;

    try {
      setIsSubmittingSubtask(true);
      await addSubtaskWithLogging(cardId, newSubtaskTitle);

      setNewSubtaskTitle(' ');

      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          const subtaskInput = document.querySelector<HTMLInputElement>('.subtask-input');
          if (subtaskInput) {
            subtaskInput.focus();
            subtaskInput.select();
          }
        });
      });
    } catch (error) {
      logger.error('Failed to add subtask:', error);
    } finally {
      setIsSubmittingSubtask(false);
    }
  };

  const addSubtaskWithLogging = async (cardId: string, title: string): Promise<void> => {
    try {
      logger.debug('Adding subtask:', { cardId, title });
      await addSubtask(cardId, title);
      logger.debug('Subtask added successfully');
    } catch (error) {
      logger.error('Error adding subtask:', error);
      throw error;
    }
  };

  const handleToggleSubtask = async (subtask: TeamKanbanSubtask) => {
    try {
      const currentSubtask = subtasks.find(s => s.id === subtask.id);
      if (!currentSubtask) {
        throw new Error('Subtask not found');
      }

      await updateSubtask(subtask.id, {
        is_completed: !currentSubtask.is_completed,
        assignee: currentSubtask.assignee
      });
    } catch (error) {
      logger.error('Failed to toggle subtask:', error);
    }
  };

  const handleUpdateSubtaskDate = async (subtaskId: string, date: Date | undefined): Promise<void> => {
    try {
      const currentSubtask = subtasks.find((s) => s.id === subtaskId);
      if (!currentSubtask) {
        throw new Error('Subtask not found');
      }

      const previousSubtask = subtasks.find((s) => s.id === subtaskId);
      if (!previousSubtask) {
        throw new Error('Previous subtask not found');
      }

      const existingAssignee = currentSubtask.assignee;

      await updateSubtask(subtaskId, {
        due_date: date ? date.toISOString() : null,
        assignee: existingAssignee
      });

      const updatedSubtask = subtasks.find(s => s.id === subtaskId);
      if (!updatedSubtask) {
        throw new Error('Updated subtask not found');
      }
      
      // Pass the comment handling to the consumer via a callback or skip for now
      
      /* 
      // Instead of adding comments directly here, this should be handled
      // in a parent component or through a context provider
      if (!previousSubtask.due_date && date) {
        // Due date was added
      } else if (previousSubtask.due_date && !date) {
        // Due date was removed
      } else if (previousSubtask.due_date && date) {
        // Due date was updated
      }
      */
    } catch (error) {
      logger.error('Failed to update subtask date:', error);
    }
  };

  const handleUpdateSubtaskAssignee = async (subtaskId: string, userId: string | undefined): Promise<void> => {
    try {
      const currentSubtask = subtasks.find(s => s.id === subtaskId);
      if (!currentSubtask) {
        throw new Error('Subtask not found');
      }

      const user = users.find(u => u.id === userId);

      logger.debug('Updating subtask assignee:', {
        subtaskId,
        newUserId: userId,
        currentAssigneeId: currentSubtask.assignee_id,
        userFound: !!user,
        isRemoving: userId === undefined
      });

      await updateSubtask(subtaskId, {
        assignee_id: userId ?? undefined,
        assignee: user ? {
          id: user.id,
          name: user.name,
          avatar: user.avatar
        } : null
      });

    } catch (error) {
      logger.error('Failed to update subtask assignee:', error);
    }
  };

  const handleUpdateSubtaskTitle = async (subtaskId: string, title: string) => {
    try {
      const currentSubtask = subtasks.find(s => s.id === subtaskId);
      if (!currentSubtask) {
        throw new Error('Subtask not found');
      }

      await updateSubtask(subtaskId, { 
        title,
        assignee: currentSubtask.assignee
      });
    } catch (error) {
      logger.error('Failed to update subtask title:', error);
    }
  };

  const handleDeleteSubtask = async (subtaskId: string) => {
    try {
      logger.debug('Deleting subtask:', subtaskId);
      const currentSubtask = subtasks.find(s => s.id === subtaskId);
      if (!currentSubtask) {
        throw new Error('Subtask not found');
      }

      await deleteSubtask(subtaskId);
      logger.debug('Subtask deleted successfully');
    } catch (error) {
      logger.error('Failed to delete subtask:', error);
    }
  };

  const handleReorderSubtasks = async (activeId: string, overId: string) => {
    try {
      const incompleteTasks = subtasks
        .filter(t => !t.is_completed)
        .sort((a, b) => a.order_index - b.order_index);

      const activeTask = incompleteTasks.find(t => t.id === activeId);
      if (!activeTask) return;

      const oldIndex = incompleteTasks.findIndex(t => t.id === activeId);
      const newIndex = incompleteTasks.findIndex(t => t.id === overId);

      if (oldIndex === -1 || newIndex === -1) return;

      const newItems = [...incompleteTasks];
      newItems.splice(oldIndex, 1);
      newItems.splice(newIndex, 0, activeTask);

      const newOrderIndex = calculateNewOrderIndex(
        newItems.filter((_, i) => i !== newIndex),
        newIndex
      );

      const currentSubtask = subtasks.find(s => s.id === activeId);
      if (!currentSubtask) {
        throw new Error('Subtask not found');
      }

      const MIN_GAP = 100;
      const needsReorder = newItems.some((item, i) =>
        i > 0 && (item.order_index - newItems[i - 1].order_index) < MIN_GAP
      );

      if (needsReorder) {
        logger.debug('Rebalancing subtask order indices');

        for (let i = 0; i < newItems.length; i++) {
          const item = newItems[i];
          const newOrder = (i + 1) * 1000;

          if (item.order_index === newOrder) continue;

          await updateSubtask(item.id, { 
            order_index: newOrder 
          });
        }
      } else {
        await updateSubtask(activeId, { 
          order_index: newOrderIndex 
        });
      }
    } catch (error) {
      logger.error('Failed to reorder subtasks:', error);
    }
  };

  return {
    subtasks: orderedSubtasks,
    subtaskProgress,
    newSubtaskTitle,
    setNewSubtaskTitle,
    isSubmittingSubtask,
    initialLoadComplete,
    handleAddSubtask,
    handleToggleSubtask,
    handleUpdateSubtaskDate,
    handleUpdateSubtaskAssignee,
    handleUpdateSubtaskTitle,
    handleDeleteSubtask,
    handleReorderSubtasks,
  };
};