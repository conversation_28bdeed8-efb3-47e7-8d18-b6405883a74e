import { useState, useEffect, useRef } from 'react';
import { useKanbanStore, kanbanSelectors } from '@/features/teams/store/teamKanbanStore';
import { logger } from '@/utils/logger';
import { errorHandler } from '@/utils/errorHandler';
import { useAuthStore } from '@/features/auth';

// Define the comment type based on the store structure and export it for use in components
export interface KanbanComment {
  id: string;
  content: string;
  created_at: string;
  edited_at: string | null;
  deleted_at: string | null;
  is_system: boolean;
  user: {
    id: string;
    name: string;
    avatar: string;
  };
}

export const useComments = (cardId: string) => {
  const { user: currentUser } = useAuthStore();
  const [newComment, setNewComment] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editedCommentContent, setEditedCommentContent] = useState('');
  const [openDropdown, setOpenDropdown] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Get comments from the store using selectors
  const commentPages = useKanbanStore(kanbanSelectors.selectCardComments(cardId));
  const hasMoreComments = useKanbanStore(kanbanSelectors.selectHasMoreComments(cardId));
  const loadingComments = useKanbanStore(kanbanSelectors.selectCommentLoadingState(cardId));
  
  // Keep a local copy of hasMoreComments to avoid store dependency issues
  const [localHasMoreComments, setLocalHasMoreComments] = useState(hasMoreComments);

  // Store actions
  const addComment = useKanbanStore(state => state.addComment);
  const fetchComments = useKanbanStore(state => state.fetchComments);
  const updateComment = useKanbanStore(state => state.updateComment);
  const deleteComment = useKanbanStore(state => state.deleteComment);

  // Initial comments load
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Fetch newest 20 comments (API returns newest first, store reverses them)
        const { count } = await fetchComments(cardId, 1, 20);
        
        // Update hasMore based on total comment count
        setLocalHasMoreComments(count > 20);
        setInitialLoadComplete(true);

        // Scroll to the bottom to show the newest comments
        requestAnimationFrame(() => {
          setTimeout(() => {
            const viewport = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');
            if (viewport) {
              viewport.scrollTop = viewport.scrollHeight;
            }
          }, 150);
        });
      } catch (error) {
        logger.error('Error loading initial comments:', error);
        setInitialLoadComplete(true); // Set to true anyway to avoid loading state
      }
    };
    loadInitialData();
  }, [cardId, fetchComments]);

  // Function to handle loading more comments
  const handleLoadMore = async () => {
    try {
      // Use the scroll viewport directly
      const viewport = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');
      if (!viewport) return;

      // Get the current scroll height before loading more
      const scrollHeightBefore = viewport.scrollHeight;
      const scrollPositionBefore = viewport.scrollTop;

      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      const { count } = await fetchComments(cardId, nextPage, 20); // Keep consistent with initial load

      // Update hasMore based on the actual count vs what we've loaded
      const totalLoaded = nextPage * 20;
      setLocalHasMoreComments(count > totalLoaded);

      // After loading older comments at the top, we need to adjust scroll position 
      // to maintain the same view (keep looking at the same comments)
      requestAnimationFrame(() => {
        setTimeout(() => {
          if (viewport) {
            const newScrollHeight = viewport.scrollHeight;
            const heightDifference = newScrollHeight - scrollHeightBefore;
            // Adjust scroll position to account for new content at the top
            viewport.scrollTop = scrollPositionBefore + heightDifference;
          }
        }, 50);
      });
    } catch (error) {
      logger.error('Error loading more comments:', error);
    }
  };

  // Add a new comment
  const handleAddComment = async () => {
    if (!newComment.trim() || isSubmittingComment) return;

    try {
      setIsSubmittingComment(true);
      logger.debug(`Adding comment to card: ${cardId}`, newComment);
      const result = await addComment(cardId, newComment);
      logger.debug('Comment added successfully:', result);
      setNewComment('');

      // Handle scrolling and focus in separate animation frames
      requestAnimationFrame(() => {
        // First handle scrolling
        const viewport = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');
        if (viewport) {
          viewport.scrollTop = viewport.scrollHeight;
        }

        // Then focus in the next frame after scroll is complete
        requestAnimationFrame(() => {
          const commentInput = document.querySelector<HTMLInputElement>('.comment-input');
          if (commentInput) {
            commentInput.focus();
          }
        });
      });
    } catch (error) {
      logger.error('Failed to add comment:', error);
    } finally {
      setIsSubmittingComment(false);
    }
  };

  // Edit an existing comment
  const handleEditComment = async (commentId: string, newContent: string) => {
    if (!newContent.trim()) return;

    try {
      logger.debug('Updating comment:', { commentId, newContent });
      await updateComment(commentId, newContent);
      logger.debug('Comment updated successfully');

      setEditingCommentId(null);
      setEditedCommentContent('');
    } catch (error) {
      logger.error('Failed to edit comment:', error);
    }
  };

  // Delete a comment
  const handleDeleteComment = async (commentId: string) => {
    try {
      // Get the comment object first to log details
      const commentToDelete = commentPages.find(c => c.id === commentId);
      logger.debug('Attempting to delete comment:', {
        commentId,
        userId: currentUser?.id,
        commentUserId: commentToDelete?.user?.id,
        isMatch: commentToDelete?.user?.id === currentUser?.id
      });

      await deleteComment(commentId);
      logger.debug('Comment deleted successfully');
    } catch (error: unknown) {
      // Convert to proper error type for handling
      const err = error instanceof Error ? error : new Error(String(error));

      errorHandler.handleApiError(err, {
        component: 'CardDetailComments',
        action: 'deleteComment',
        context: {
          commentId,
          userId: currentUser?.id
        }
      });

      // Log more detailed error info if available
      if (typeof error === 'object' && error !== null) {
        const errorObj = error as Record<string, unknown>;
        if ('code' in errorObj && 'message' in errorObj) {
          logger.error(`Error details - Code: ${String(errorObj.code)}, Message: ${String(errorObj.message)}`);
          if ('details' in errorObj) {
            logger.error('Additional error details:', errorObj.details);
          }
        }
      }
    }
  };

  return {
    commentPages,
    loadingComments,
    localHasMoreComments,
    newComment,
    setNewComment,
    initialLoadComplete,
    editingCommentId,
    setEditingCommentId,
    editedCommentContent,
    setEditedCommentContent,
    openDropdown,
    setOpenDropdown,
    isSubmittingComment,
    scrollAreaRef,
    handleLoadMore,
    handleAddComment,
    handleEditComment,
    handleDeleteComment,
    currentUser
  };
}; 