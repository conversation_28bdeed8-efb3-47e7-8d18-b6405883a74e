import { PriorityLevel } from '@/features/teams/types';

// Fix the issue with Boolean indexing in the priorityColors object
export const priorityColors: Record<PriorityLevel, string> = {
  [PriorityLevel.P1]: 'bg-red-500 text-white',
  [PriorityLevel.P2]: 'bg-yellow-500 text-black',
  [PriorityLevel.P3]: 'bg-gray-500 text-white'
};

// Function to get the priority name from the priority level
export const getPriorityName = (priority: PriorityLevel): string => {
  switch (priority) {
    case PriorityLevel.P1:
      return "High Priority";
    case PriorityLevel.P2:
      return "Medium Priority";
    case PriorityLevel.P3:
      return "Low Priority";
    default:
      return "Unknown Priority";
  }
}; 