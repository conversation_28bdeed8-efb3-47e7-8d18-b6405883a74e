// URL regex for detecting links in comments
export const URL_REGEX = /(https?:\/\/[^\s]+)/g;

/**
 * Prepares comment content by splitting it into text and link parts
 * This is a utility to prepare the parts array - actual rendering happens in the component
 */
export const prepareCommentParts = (content: string): { type: 'text' | 'link', content: string }[] => {
  if (!content) return [];
  
  const parts = content.split(URL_REGEX);
  const result: { type: 'text' | 'link', content: string }[] = [];
  
  parts.forEach((part) => {
    if (part.match(URL_REGEX)) {
      result.push({ type: 'link', content: part });
    } else if (part) {
      result.push({ type: 'text', content: part });
    }
  });
  
  return result;
};

/**
 * Determines the CSS class for due date based on whether it's past due
 */
export const getDueDateColor = (dueDate: string): string => {
  if (!dueDate) return "";
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const dueDateObj = new Date(dueDate);
  dueDateObj.setHours(0, 0, 0, 0);

  if (dueDateObj <= today) {
    return "text-destructive font-medium";
  }
  return "";
}; 