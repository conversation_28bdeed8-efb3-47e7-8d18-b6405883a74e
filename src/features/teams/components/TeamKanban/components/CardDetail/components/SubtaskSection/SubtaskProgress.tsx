import React from 'react';
import { Progress } from '@/components/ui/progress';

interface SubtaskProgressProps {
  completed: number;
  total: number;
  percent: number;
}

export const SubtaskProgress: React.FC<SubtaskProgressProps> = ({
  completed,
  total,
  percent
}) => {
  if (total === 0) return null;

  return (
    <div className="space-y-1.5">
      <div className="flex items-center justify-between">
        <span className="text-xs text-muted-foreground">
          Tasks: {completed}/{total} completed
        </span>
        <span className="text-xs font-medium">
          {Math.round(percent)}%
        </span>
      </div>
      <Progress value={percent} className="h-1.5" />
    </div>
  );
}; 