import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from 'lucide-react';
import { DayPicker } from 'react-day-picker';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';

interface DatePickerProps {
  date: string | null;
  onChange: (date: Date | undefined) => Promise<void>;
  className?: string;
}

export const DatePicker: React.FC<DatePickerProps> = ({
  date,
  onChange,
  className
}) => {
  const [open, setOpen] = useState(false);

  const handleDateSelect = async (date: Date | undefined) => {
    try {
      await onChange(date);
      setOpen(false);
    } catch (error) {
      console.error('Error updating date:', error);
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-8 px-2",
                  !date && "w-8 p-0",
                  className
                )}
              >
                {date ? (
                  <span className="flex items-center gap-2 text-xs">
                    <Calendar className="w-4 h-4" />
                    {format(new Date(date), 'MMM d')}
                  </span>
                ) : (
                  <Calendar className="w-4 h-4" />
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="flex w-auto flex-col space-y-2 p-2" align="start">
              <div className="rounded-md border">
                <DayPicker
                  mode="single"
                  selected={date ? new Date(date) : undefined}
                  onSelect={handleDateSelect}
                  required={false}
                  modifiers={{
                    today: new Date(),
                    selected: date ? new Date(date) : undefined
                  }}
                  modifiersClassNames={{
                    selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                    today: "border border-border bg-accent text-accent-foreground font-medium"
                  }}
                  className="p-3"
                />
              </div>
              <div className="grid gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-left font-normal"
                  onClick={() => void handleDateSelect(new Date())}
                >
                  Today
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-left font-normal"
                  onClick={() => {
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    void handleDateSelect(tomorrow);
                  }}
                >
                  Tomorrow
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-left font-normal"
                  onClick={() => {
                    const nextWeek = new Date();
                    nextWeek.setDate(nextWeek.getDate() + 7);
                    void handleDateSelect(nextWeek);
                  }}
                >
                  Next week
                </Button>
              </div>
              <div className="flex items-center mt-2">
                <Button 
                  type="button" 
                  variant="ghost" 
                  className="px-2 py-1 h-auto text-destructive-foreground font-normal hover:bg-destructive/5"
                  onClick={() => handleDateSelect(undefined)}
                  disabled={!date}
                >
                  Remove
                </Button>
              </div>
            </PopoverContent>
          </Popover>
        </TooltipTrigger>
        <TooltipContent>
          {date ? format(new Date(date), 'MMM d, yyyy') : 'Set due date'}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
