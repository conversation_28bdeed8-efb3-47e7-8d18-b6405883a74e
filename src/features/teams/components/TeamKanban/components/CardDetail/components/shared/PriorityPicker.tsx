import React from 'react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { PriorityLevel } from '@/features/teams/types';
import { priorityColors, getPriorityName } from '../../utils/priorityUtils';

interface PriorityPickerProps {
  priority: PriorityLevel;
  onChange: (priority: PriorityLevel) => void;
  className?: string;
}

export const PriorityPicker: React.FC<PriorityPickerProps> = ({
  priority,
  onChange,
  className
}) => {
  const [open, setOpen] = React.useState(false);

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger
        className={cn(
          "px-2 py-0.5 rounded-full text-xs font-medium cursor-pointer",
          priorityColors[priority],
          className
        )}
      >
        {priority}
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        {Object.values(PriorityLevel).map((priorityLevel) => (
          <DropdownMenuItem
            key={priorityLevel}
            onClick={() => {
              onChange(priorityLevel);
              setOpen(false);
            }}
            className={cn(
              "text-xs font-medium",
              priority === priorityLevel && "bg-accent"
            )}
          >
            <span className={cn(
              "w-12 text-center py-0.5 rounded-full mr-2",
              priorityColors[priorityLevel]
            )}>
              {priorityLevel}
            </span>
            {getPriorityName(priorityLevel)}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}; 