import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Users, CheckCircle2 } from 'lucide-react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Command, CommandInput, CommandEmpty, CommandGroup, CommandItem } from '@/components/ui/command';
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';
import { User } from '@/features/teams/types';

interface AssigneePickerProps {
  assignee: { id: string; name: string; avatar: string } | null;
  teamMembers: User[];
  loadingTeamMembers: boolean;
  onUpdateAssignees: (selectedUsers: User[]) => void;
  className?: string;
}

export const AssigneePicker: React.FC<AssigneePickerProps> = ({
  assignee,
  teamMembers,
  loadingTeamMembers,
  onUpdateAssignees,
  className
}) => {
  // Track the popover state
  const [open, setOpen] = React.useState(false);
  const [searchValue] = useState('');
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={`h-8 w-8 p-0 ${className || ''}`}
              >
                {assignee ? (
                  <Avatar className="w-6 h-6">
                    <AvatarImage src={assignee.avatar} />
                    <AvatarFallback className="bg-background">
                      {assignee.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                ) : (
                  <Users className="w-4 h-4" />
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0" align="start">
              <Command>
                <CommandInput placeholder="Search team members..." className="h-9" />
                {teamMembers.length === 0 ? (
                  <CommandEmpty>
                    {loadingTeamMembers ? 'Loading team members...' : 'No team members found.'}
                  </CommandEmpty>
                ) : (
                  <CommandGroup>
                    {teamMembers
                      .filter(user => 
                        searchValue.trim() === '' || 
                        user.name.toLowerCase().includes(searchValue.toLowerCase())
                      )
                      .map((user) => (
                        <CommandItem 
                          key={user.id}
                          className="flex items-center gap-2 px-2 py-1.5 cursor-pointer"
                          onSelect={async () => {
                            // Update assignees first
                            await onUpdateAssignees([user]);
                            // Then close the popover
                            setOpen(false);
                          }}
                        >
                          <Avatar className="w-6 h-6 flex-shrink-0">
                            <AvatarImage src={user.avatar} />
                            <AvatarFallback className="bg-background">
                              {user.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <span className="truncate">{user.name}</span>
                          {assignee?.id === user.id && (
                            <CheckCircle2 className="w-4 h-4 ml-auto text-primary" />
                          )}
                        </CommandItem>
                      ))}
                  </CommandGroup>
                )}
              </Command>
              {assignee && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start text-left font-normal text-destructive px-2 py-1.5 h-9"
                  onClick={() => {
                    onUpdateAssignees([]);
                    setOpen(false);
                  }}
                >
                  Remove assignee
                </Button>
              )}
            </PopoverContent>
          </Popover>
        </TooltipTrigger>
        <TooltipContent>
          {assignee
            ? `Assigned to ${assignee.name}`
            : 'Assign members'
          }
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}; 