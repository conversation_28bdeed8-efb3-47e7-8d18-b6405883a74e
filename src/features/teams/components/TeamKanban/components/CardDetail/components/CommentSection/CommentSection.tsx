import React, { useEffect } from 'react';
import { ArrowDown, Loader2, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { KanbanComment } from '../../hooks/useComments';
import { CommentItem } from './CommentItem';
import { CommentInput } from './CommentInput';

interface CommentSectionProps {
  scrollAreaRef: React.RefObject<HTMLDivElement>;
  commentPages: KanbanComment[];
  loadingComments: boolean;
  localHasMoreComments: boolean;
  initialLoadComplete: boolean;
  handleLoadMore: () => Promise<void>;
  newComment: string;
  setNewComment: (comment: string) => void;
  isSubmittingComment: boolean;
  handleAddComment: () => Promise<void>;
  editingCommentId: string | null;
  setEditingCommentId: (id: string | null) => void;
  editedCommentContent: string;
  setEditedCommentContent: (content: string) => void;
  openDropdown: string;
  setOpenDropdown: (id: string) => void;
  handleEditComment: (commentId: string, newContent: string) => Promise<void>;
  handleDeleteComment: (commentId: string) => Promise<void>;
  currentUserId?: string;
  showSystemComments?: boolean;
}

export const CommentSection: React.FC<CommentSectionProps> = ({
  scrollAreaRef,
  commentPages,
  loadingComments,
  localHasMoreComments,
  initialLoadComplete,
  handleLoadMore,
  newComment,
  setNewComment,
  isSubmittingComment,
  handleAddComment,
  editingCommentId,
  setEditingCommentId,
  editedCommentContent,
  setEditedCommentContent,
  openDropdown,
  setOpenDropdown,
  handleEditComment,
  handleDeleteComment,
  currentUserId,
  showSystemComments = false
}) => {
  // Helper function to check if two comments are from the same sender
  const isSameSender = (current: KanbanComment | undefined, previous: KanbanComment | undefined) => {
    if (!current || !previous) return false;
    if (current.is_system && previous.is_system) return true;
    return !current.is_system && !previous.is_system && current.user.id === previous.user.id;
  };

  // Filter comments based on showSystemComments setting
  const filteredComments = showSystemComments 
    ? commentPages 
    : commentPages.filter(comment => !comment.is_system);

  // Count how many system comments are hidden
  const hiddenSystemCommentsCount = !showSystemComments 
    ? commentPages.filter(comment => comment.is_system).length 
    : 0;

  // Auto-load more comments if filtering resulted in an empty list but more comments exist
  useEffect(() => {
    if (
      filteredComments.length === 0 && 
      commentPages.length > 0 && 
      !showSystemComments && 
      localHasMoreComments && 
      !loadingComments && 
      initialLoadComplete
    ) {
      handleLoadMore();
    }
  }, [
    filteredComments.length, 
    commentPages.length,
    showSystemComments, 
    localHasMoreComments, 
    loadingComments, 
    initialLoadComplete,
    handleLoadMore
  ]);

  const CommentLoadingSkeleton = () => (
    <div className="space-y-3">
      {[1, 2, 3, 4].map((i) => (
        <div key={i} className="flex items-start gap-1.5">
          <Skeleton className="w-5 h-5 rounded-full flex-shrink-0" />
          <div className="space-y-1.5 flex-1">
            <Skeleton className="h-3 w-24" />
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-2.5 w-16" />
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="space-y-4">
      <ScrollArea className="h-[40vh] max-h-[40vh] overflow-auto pr-2" type="always" ref={scrollAreaRef}>
        <div className="mr-2">
          {/* "Load More" button */}
          {localHasMoreComments && initialLoadComplete && (
            <div className="flex justify-center pb-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLoadMore}
                disabled={loadingComments}
                className="text-xs gap-1"
              >
                {loadingComments ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <>
                    <ArrowDown className="h-3 w-3" />
                    Load older comments
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Loading indicator for initial load */}
          {!initialLoadComplete ? (
            <div className="py-2">
              <CommentLoadingSkeleton />
            </div>
          ) : filteredComments.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-[200px] text-center p-4 gap-2">
              {hiddenSystemCommentsCount > 0 ? (
                <>
                  <Info className="h-4 w-4 text-muted-foreground" />
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">
                      {hiddenSystemCommentsCount} system activity {hiddenSystemCommentsCount === 1 ? 'entry is' : 'entries are'} hidden
                    </p>
                    <p className="text-xs text-muted-foreground/70">
                      Turn on "Show Activity" to view them
                    </p>
                  </div>
                </>
              ) : (
                <p className="text-sm text-muted-foreground">No comments yet</p>
              )}
            </div>
          ) : (
            <div className="space-y-1">
              {/* Show notice about hidden system comments if any */}
              {!showSystemComments && hiddenSystemCommentsCount > 0 && (
                <div className="flex items-center gap-1.5 px-2 py-1.5 mb-2 text-xs text-muted-foreground bg-muted/30 rounded-md">
                  <Info className="h-3.5 w-3.5" />
                  <span>
                    {hiddenSystemCommentsCount} system {hiddenSystemCommentsCount === 1 ? 'entry' : 'entries'} hidden
                  </span>
                </div>
              )}
              
              {/* Comments list */}
              {filteredComments.map((comment, index) => {
                const previousComment = index > 0 ? filteredComments[index - 1] : undefined;
                const nextComment = index < filteredComments.length - 1 ? filteredComments[index + 1] : undefined;
                const isFirstInGroup = !isSameSender(comment, previousComment);
                const isLastInGroup = !isSameSender(nextComment, comment);

                return (
                  <CommentItem
                    key={comment.id}
                    comment={comment}
                    isEditing={editingCommentId === comment.id}
                    editedContent={editedCommentContent}
                    currentUserId={currentUserId}
                    onEditStart={() => {
                      setEditingCommentId(comment.id);
                      setEditedCommentContent(comment.content);
                    }}
                    onEditCancel={() => {
                      setEditingCommentId(null);
                      setEditedCommentContent('');
                    }}
                    onEditSave={() => {
                      handleEditComment(comment.id, editedCommentContent);
                    }}
                    onEditChange={setEditedCommentContent}
                    onDelete={() => handleDeleteComment(comment.id)}
                    openDropdown={openDropdown}
                    setOpenDropdown={setOpenDropdown}
                    isFirstInGroup={isFirstInGroup}
                    isLastInGroup={isLastInGroup}
                  />
                );
              })}
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Comment Input Area */}
      <div className="border-t pt-4">
        <CommentInput
          newComment={newComment}
          setNewComment={setNewComment}
          handleAddComment={handleAddComment}
          isSubmittingComment={isSubmittingComment}
          placeholder={filteredComments.length === 0 ? "Be the first to comment..." : "Write a message..."}
        />
      </div>
    </div>
  );
};