import React from 'react';
import { Trash2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { TeamKanbanCard, User } from '@/features/teams/types';
import { DatePicker } from './shared/DatePicker';
import { AssigneePicker } from './shared/AssigneePicker';
import { Progress } from '@/components/ui/progress';
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';

interface CardControlsProps {
  editedCard: TeamKanbanCard;
  teamMembers: User[];
  loadingTeamMembers: boolean;
  subtaskProgress: { completed: number; total: number; percent: number };
  handleUpdateDueDate: (date: Date | undefined) => Promise<void>;
  handleUpdateAssignees: (selectedUsers: User[]) => Promise<void>;
  setShowDeleteAlert: (show: boolean) => void;
}

export const CardControls: React.FC<CardControlsProps> = ({
  editedCard,
  teamMembers,
  loadingTeamMembers,
  subtaskProgress,
  handleUpdateDueDate,
  handleUpdateAssignees,
  setShowDeleteAlert
}) => {
  return (
    <div className="flex items-center gap-4 pt-2">
      <div className="flex items-center gap-1 group">
        <DatePicker 
          date={editedCard.due_date} 
          onChange={handleUpdateDueDate} 
        />

        <AssigneePicker
          assignee={editedCard.assignee}
          teamMembers={teamMembers}
          loadingTeamMembers={loadingTeamMembers}
          onUpdateAssignees={handleUpdateAssignees}
        />

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-destructive hover:text-destructive"
                onClick={() => setShowDeleteAlert(true)}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              Delete card
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <div className="flex items-center gap-2 ml-auto">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">
                  {subtaskProgress.completed}/{subtaskProgress.total}
                </span>
                <Progress value={subtaskProgress.percent} className="w-16 h-2" />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              {`${subtaskProgress.completed} of ${subtaskProgress.total} tasks completed`}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
}; 