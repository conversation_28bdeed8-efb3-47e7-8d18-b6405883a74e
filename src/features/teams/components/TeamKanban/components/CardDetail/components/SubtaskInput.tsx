import { useState, KeyboardEvent } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface SubtaskInputProps {
  onAddSubtask: (title: string) => Promise<void>;
  disabled?: boolean;
}

export const SubtaskInput: React.FC<SubtaskInputProps> = ({ 
  onAddSubtask,
  disabled = false
}) => {
  const [title, setTitle] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!title.trim() || isSubmitting) return;
    
    try {
      setIsSubmitting(true);
      await onAddSubtask(title.trim());
      setTitle('');
    } catch (error) {
      console.error('Error adding subtask:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      void handleSubmit();
    }
  };

  return (
    <div className="flex items-center gap-2 mt-2">
      <Input
        value={title}
        onChange={(e) => setTitle(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Add a subtask..."
        disabled={disabled || isSubmitting}
        className="flex-1"
      />
      <Button 
        onClick={handleSubmit} 
        disabled={!title.trim() || disabled || isSubmitting}
        size="sm"
        variant="ghost"
      >
        <Plus className="h-4 w-4" />
      </Button>
    </div>
  );
}; 