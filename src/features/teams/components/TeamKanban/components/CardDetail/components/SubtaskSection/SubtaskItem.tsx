import React, { useState, useRef } from 'react';
import { Check, Trash2, GripVertical } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { TeamKanbanSubtask, User } from '@/features/teams/types';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { DatePicker } from '../shared/DatePicker';
import { AssigneePicker } from '../shared/AssigneePicker';
import { getDueDateColor } from '../../utils/commentUtils';

interface SubtaskItemProps {
  subtask: TeamKanbanSubtask;
  users: User[];
  handleToggleSubtask: (subtask: TeamKanbanSubtask) => Promise<void>;
  handleUpdateSubtaskAssignee: (subtaskId: string, userId: string | undefined) => Promise<void>;
  handleUpdateSubtaskDate: (subtaskId: string, date: Date | undefined) => Promise<void>;
  handleDeleteSubtask: (subtaskId: string) => Promise<void>;
  handleUpdateSubtaskTitle: (subtaskId: string, title: string) => Promise<void>;
}

export const SubtaskItem: React.FC<SubtaskItemProps> = ({
  subtask,
  users,
  handleToggleSubtask,
  handleUpdateSubtaskAssignee,
  handleUpdateSubtaskDate,
  handleDeleteSubtask,
  handleUpdateSubtaskTitle
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTitle, setEditedTitle] = useState(subtask.title);
  const inputRef = useRef<HTMLInputElement>(null);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: subtask.id, disabled: subtask.is_completed || isEditing });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const handleEditComplete = async () => {
    if (editedTitle.trim() !== subtask.title) {
      await handleUpdateSubtaskTitle(subtask.id, editedTitle);
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleEditComplete();
    } else if (e.key === 'Escape') {
      setEditedTitle(subtask.title);
      setIsEditing(false);
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "flex items-center gap-2 p-2 bg-muted/50 rounded-lg group hover:bg-muted",
        subtask.is_completed && "opacity-75",
        !subtask.is_completed && !isEditing && "cursor-grab active:cursor-grabbing"
      )}
    >
      {!subtask.is_completed && !isEditing && (
        <div {...attributes} {...listeners} className="touch-none">
          <GripVertical className="w-4 h-4 text-muted-foreground/40 hover:text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
        </div>
      )}
      {(subtask.is_completed || isEditing) && (
        <div className="w-4" />
      )}
      
      <button
        type="button"
        onClick={() => handleToggleSubtask(subtask)}
        className={cn(
          "w-5 h-5 rounded-full border-2 flex items-center justify-center",
          "hover:bg-muted transition-colors",
          subtask.is_completed ? 'border-primary bg-primary' : 'border-muted-foreground/50'
        )}
        aria-label={subtask.is_completed ? "Mark as incomplete" : "Mark as complete"}
      >
        {subtask.is_completed && <Check className="w-3 h-3 text-primary-foreground" />}
      </button>

      {isEditing ? (
        <Input
          ref={inputRef}
          value={editedTitle}
          onChange={(e) => setEditedTitle(e.target.value)}
          onBlur={handleEditComplete}
          onKeyDown={handleKeyDown}
          className="h-7 flex-1"
          autoFocus
        />
      ) : (
        <span 
          className={cn(
            "flex-1 text-sm select-none",
            subtask.is_completed && "line-through text-muted-foreground",
            !subtask.is_completed && "cursor-text"
          )}
          onClick={() => {
            if (!subtask.is_completed) {
              setIsEditing(true);
              setTimeout(() => inputRef.current?.focus(), 0);
            }
          }}
        >
          {subtask.title}
        </span>
      )}

      <div className="flex items-center gap-1 ml-auto">
        {/* Always show assignee if present */}
        {subtask.assignee && (
          <div className={cn("h-7", subtask.is_completed && "pointer-events-none opacity-50")}>
            <AssigneePicker
              assignee={subtask.assignee}
              teamMembers={users}
              loadingTeamMembers={false}
              onUpdateAssignees={(users) => {
                if (!subtask.is_completed) {
                  const userId = users.length > 0 ? users[0].id : undefined;
                  handleUpdateSubtaskAssignee(subtask.id, userId);
                }
              }}
              className="h-7"
            />
          </div>
        )}
        
        {/* Always show due date if present */}
        {subtask.due_date && (
          <div className={cn("h-7", subtask.is_completed && "pointer-events-none opacity-50")}>
            <DatePicker
              date={subtask.due_date}
              onChange={async (date) => {
                if (!subtask.is_completed) {
                  await handleUpdateSubtaskDate(subtask.id, date);
                }
              }}
              className={cn("h-7", getDueDateColor(subtask.due_date || ''))}
            />
          </div>
        )}
        
        {/* Only show these controls for incomplete tasks */}
        {!subtask.is_completed && (
          <>
            {!subtask.assignee && (
              <AssigneePicker
                assignee={null}
                teamMembers={users}
                loadingTeamMembers={false}
                onUpdateAssignees={(users) => {
                  const userId = users.length > 0 ? users[0].id : undefined;
                  handleUpdateSubtaskAssignee(subtask.id, userId);
                }}
                className="h-7 opacity-0 group-hover:opacity-100"
              />
            )}
            
            {!subtask.due_date && (
              <DatePicker
                date={null}
                onChange={async (date) => {
                  await handleUpdateSubtaskDate(subtask.id, date);
                }}
                className="h-7 opacity-0 group-hover:opacity-100"
              />
            )}
            
            <button
              onClick={() => handleDeleteSubtask(subtask.id)}
              className="h-7 px-2 text-destructive hover:text-destructive opacity-0 group-hover:opacity-100"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </>
        )}
      </div>
    </div>
  );
};