import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash2, Bo<PERSON> } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { KanbanComment } from '../../hooks/useComments';
import { prepareCommentParts } from '../../utils/commentUtils';
import { Markdown } from '@/components/ui/markdown';
import { LinkPreview } from '@/components/ui/link-preview';
import { cn } from '@/lib/utils';

interface CommentItemProps {
  comment: KanbanComment;
  isEditing: boolean;
  editedContent: string;
  currentUserId?: string;
  onEditStart: () => void;
  onEditCancel: () => void;
  onEditSave: () => void;
  onEditChange: (value: string) => void;
  onDelete: () => void;
  openDropdown: string;
  setOpenDropdown: (id: string) => void;
  isFirstInGroup: boolean;
  isLastInGroup: boolean;
}

export const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  isEditing,
  editedContent,
  currentUserId,
  onEditStart,
  onEditCancel,
  onEditSave,
  onEditChange,
  onDelete,
  openDropdown,
  setOpenDropdown,
  isFirstInGroup,
  isLastInGroup
}) => {
  const isOwner = currentUserId === comment.user.id;
  const isDropdownOpen = openDropdown === comment.id;
  const timeAgo = formatDistanceToNow(new Date(comment.created_at), { includeSeconds: true });
  const isSystemComment = comment.is_system;

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onEditSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onEditCancel();
    }
  };

  return (
    <div
      key={comment.id}
      data-comment
      data-system={isSystemComment}
      className={cn(
        "group flex gap-1.5",
        isSystemComment && "opacity-90",
        !isFirstInGroup && "mt-1"
      )}
    >
      {isFirstInGroup && (
        <Avatar className="w-5 h-5 mt-0.5 flex-shrink-0">
          {isSystemComment ? (
            <div className="w-full h-full flex items-center justify-center bg-muted">
              <Bot className="w-3 h-3 text-muted-foreground" />
            </div>
          ) : (
            <>
              <AvatarImage src={comment.user.avatar} />
              <AvatarFallback className="bg-background text-[10px]">
                {comment.user.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </>
          )}
        </Avatar>
      )}
      {!isFirstInGroup && <div className="w-5 flex-shrink-0" />}
      <div className="flex-1 space-y-0.5 max-w-full">
        {isFirstInGroup && (
          <span className="text-xs font-medium block mb-0.5 truncate">
            {comment.user.name}
            {isSystemComment && (
              <span className="ml-2 text-xs text-muted-foreground font-normal">
                (Automated)
              </span>
            )}
          </span>
        )}
        {comment.deleted_at ? (
          <div className={cn(
            "relative rounded-lg py-1.5 px-2 break-words",
            isOwner
              ? "bg-[#E3F2FD]/50 text-blue-900/50"
              : "bg-[#F5F5F5]/50 text-gray-900/50",
            !isFirstInGroup && "rounded-t-md",
            !isLastInGroup && "rounded-b-md"
          )}>
            <span className="text-sm italic">This comment was deleted</span>
          </div>
        ) : (
          <div className={cn(
            "relative rounded-lg py-1.5 px-2 break-words group/comment",
            isSystemComment
              ? "bg-muted/50 text-muted-foreground"
              : isOwner
                ? "bg-[#E3F2FD] text-blue-900"
                : "bg-[#F5F5F5] text-gray-900",
            !isFirstInGroup && "rounded-t-md",
            !isLastInGroup && "rounded-b-md"
          )}>
            {isEditing ? (
              <div className="space-y-2">
                <Input
                  value={editedContent}
                  onChange={(e) => onEditChange(e.target.value)}
                  className="h-8"
                  autoFocus
                  onKeyDown={handleKeyDown}
                />
                <div className="flex justify-end gap-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={onEditCancel}
                    className="h-7"
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={onEditSave}
                    disabled={!editedContent.trim() || editedContent === comment.content}
                    className="h-7"
                  >
                    Save
                  </Button>
                </div>
              </div>
            ) : (
              <>
                <div className="text-sm break-words">
                  {prepareCommentParts(comment.content).map((part, index) => (
                    part.type === 'link' ? (
                      <LinkPreview key={index} url={part.content} />
                    ) : (
                      <Markdown key={index}>{part.content}</Markdown>
                    )
                  ))}
                </div>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-xs opacity-70">
                    {timeAgo} ago
                    {comment.edited_at && (
                      <span className="ml-1">(edited)</span>
                    )}
                  </span>
                  {isOwner && !isSystemComment && (
                    <DropdownMenu open={isDropdownOpen} onOpenChange={(open) => setOpenDropdown(open ? comment.id : '')}>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 opacity-0 group-hover/comment:opacity-100 transition-opacity"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[140px]">
                        <DropdownMenuItem
                          onClick={onEditStart}
                          className="gap-2"
                        >
                          <Pencil className="h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={onDelete}
                          className="gap-2 text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};