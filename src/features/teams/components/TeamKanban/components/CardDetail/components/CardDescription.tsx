import React, { useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { TeamKanbanCard } from '@/features/teams/types';

interface CardDescriptionProps {
  editedCard: TeamKanbanCard;
  handleUpdateCard: () => Promise<void>;
  setEditedCard: React.Dispatch<React.SetStateAction<TeamKanbanCard>>;
}

export const CardDescription: React.FC<CardDescriptionProps> = ({
  editedCard,
  handleUpdateCard,
  setEditedCard
}) => {
  const [isTextareaExpanded, setIsTextareaExpanded] = useState(false);

  return (
    <Textarea
      value={editedCard.description || ''}
      onChange={(e) => {
        setEditedCard({ ...editedCard, description: e.target.value });
      }}
      onBlur={() => {
        setIsTextareaExpanded(false);
        handleUpdateCard();
      }}
      onClick={() => setIsTextareaExpanded(true)}
      placeholder="Add a more detailed description..."
      className={cn(
        "min-h-[60px] max-h-[180px] resize-none bg-background border-none px-0",
        "focus-visible:ring-0 hover:bg-muted/40 text-sm overflow-y-auto",
        "transition-[height] duration-200 ease-out"
      )}
      style={{
        height: isTextareaExpanded ? '180px' : '60px'
      }}
    />
  );
}; 