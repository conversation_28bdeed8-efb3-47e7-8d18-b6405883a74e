import React from 'react';
import { TeamKanbanSubtask, User } from '@/features/teams/types';
import { SubtaskItem } from './SubtaskItem';
import { DndContext, closestCenter, DragEndEvent, MouseSensor, TouchSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { restrictToParentElement } from '@dnd-kit/modifiers';

interface SubtaskSectionProps {
  subtasks: TeamKanbanSubtask[];
  subtaskProgress: { completed: number; total: number; percent: number };
  newSubtaskTitle: string;
  setNewSubtaskTitle: (title: string) => void;
  isSubmittingSubtask: boolean;
  handleAddSubtask: () => Promise<void>;
  handleToggleSubtask: (subtask: TeamKanbanSubtask) => Promise<void>;
  handleUpdateSubtaskAssignee: (subtaskId: string, userId: string | undefined) => Promise<void>;
  handleUpdateSubtaskDate: (subtaskId: string, date: Date | undefined) => Promise<void>;
  handleDeleteSubtask: (subtaskId: string) => Promise<void>;
  handleUpdateSubtaskTitle: (subtaskId: string, title: string) => Promise<void>;
  handleReorderSubtasks: (activeId: string, overId: string) => Promise<void>;
  users: User[];
}

export const SubtaskSection: React.FC<SubtaskSectionProps> = ({
  subtasks,
  subtaskProgress: _subtaskProgress,
  newSubtaskTitle: _newSubtaskTitle,
  setNewSubtaskTitle: _setNewSubtaskTitle,
  isSubmittingSubtask: _isSubmittingSubtask,
  handleAddSubtask: _handleAddSubtask,
  handleToggleSubtask,
  handleUpdateSubtaskAssignee,
  handleUpdateSubtaskDate,
  handleDeleteSubtask,
  handleUpdateSubtaskTitle,
  handleReorderSubtasks,
  users
}) => {
  // Configure sensors for drag and drop
  const mouseSensor = useSensor(MouseSensor, {
    // Lower activationConstraint threshold for easier dragging
    activationConstraint: {
      distance: 5,
    },
  });
  
  const touchSensor = useSensor(TouchSensor, {
    // Lower activationConstraint threshold for easier dragging on touch devices
    activationConstraint: {
      delay: 250,
      tolerance: 5,
    },
  });
  
  const sensors = useSensors(mouseSensor, touchSensor);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      handleReorderSubtasks(String(active.id), String(over.id));
    }
  };

  return (
    <div className="space-y-3">      
      <DndContext 
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        modifiers={[restrictToParentElement]}
      >
        <SortableContext
          items={subtasks.map(s => s.id)}
          strategy={verticalListSortingStrategy}
        >
          <div className="space-y-1">
            {subtasks.map((subtask) => (
              <SubtaskItem
                key={subtask.id}
                subtask={subtask}
                users={users}
                handleToggleSubtask={handleToggleSubtask}
                handleUpdateSubtaskAssignee={handleUpdateSubtaskAssignee}
                handleUpdateSubtaskDate={handleUpdateSubtaskDate}
                handleDeleteSubtask={handleDeleteSubtask}
                handleUpdateSubtaskTitle={handleUpdateSubtaskTitle}
              />
            ))}
          </div>
        </SortableContext>
      </DndContext>
    </div>
  );
}; 