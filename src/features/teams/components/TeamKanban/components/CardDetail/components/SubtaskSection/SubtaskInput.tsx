import React from 'react';
import { Plus, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface SubtaskInputProps {
  newSubtaskTitle: string;
  setNewSubtaskTitle: (title: string) => void;
  handleAddSubtask: () => Promise<void>;
  isSubmittingSubtask: boolean;
}

export const SubtaskInput: React.FC<SubtaskInputProps> = ({
  newSubtaskTitle,
  setNewSubtaskTitle,
  handleAddSubtask,
  isSubmittingSubtask
}) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isSubmittingSubtask && newSubtaskTitle.trim()) {
      e.preventDefault();
      handleAddSubtask();
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Input
        value={newSubtaskTitle}
        onChange={(e) => setNewSubtaskTitle(e.target.value)}
        placeholder="Add a subtask..."
        className="h-8 subtask-input"
        onKeyDown={handleKeyDown}
        disabled={isSubmittingSubtask}
      />
      <Button
        type="button"
        size="sm"
        variant="ghost"
        className="h-8 w-8 p-0"
        disabled={!newSubtaskTitle.trim() || isSubmittingSubtask}
        onClick={handleAddSubtask}
        aria-label="Add subtask"
      >
        {isSubmittingSubtask ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Plus className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
}; 