import React from 'react';
import { Check } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { TeamKanbanCard, PriorityLevel } from '@/features/teams/types';
import { PriorityPicker } from './shared/PriorityPicker';

interface CardHeaderProps {
  editedCard: TeamKanbanCard;
  handleToggleComplete: () => Promise<void>;
  handleUpdateCard: (updates?: Partial<TeamKanbanCard>) => Promise<void>;
  setEditedCard: React.Dispatch<React.SetStateAction<TeamKanbanCard>>;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  editedCard,
  handleToggleComplete,
  handleUpdateCard,
  setEditedCard
}) => {
  const handlePriorityChange = async (priority: PriorityLevel) => {
    const previousPriority = editedCard.priority;
    
    try {
      // Optimistic update
      setEditedCard(prev => ({ ...prev, priority }));

      // Send update to store
      await handleUpdateCard({ priority });
    } catch (error) {
      // Revert optimistic update on error
      setEditedCard(prev => ({ ...prev, priority: previousPriority }));
      throw error;
    }
  };

  return (
    <div className="flex items-center gap-3">
      <button
        type="button"
        onClick={handleToggleComplete}
        className={cn(
          "w-5 h-5 rounded-full border-2 flex items-center justify-center",
          "hover:bg-muted transition-colors",
          editedCard.archived_at ? 'border-primary bg-primary' : 'border-muted-foreground/50'
        )}
        aria-label={editedCard.archived_at ? "Mark as incomplete" : "Mark as complete"}
      >
        {editedCard.archived_at && <Check className="w-3 h-3 text-primary-foreground" />}
      </button>
      
      <PriorityPicker 
        priority={editedCard.priority}
        onChange={handlePriorityChange}
      />

      <Input
        value={editedCard.title}
        onChange={(e) => {
          setEditedCard({ ...editedCard, title: e.target.value });
        }}
        onBlur={() => handleUpdateCard()}
        className="text-xl font-semibold bg-background border-none px-0 focus-visible:ring-0 hover:bg-muted/40 transition-colors"
        placeholder="Enter card title..."
        aria-label="Card title"
      />
    </div>
  );
};