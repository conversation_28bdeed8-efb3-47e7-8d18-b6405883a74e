import React from 'react';
import { Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface CommentInputProps {
  newComment: string;
  setNewComment: (comment: string) => void;
  handleAddComment: () => Promise<void>;
  isSubmittingComment: boolean;
  placeholder?: string;
}

export const CommentInput: React.FC<CommentInputProps> = ({
  newComment,
  setNewComment,
  handleAddComment,
  isSubmittingComment,
  placeholder = "Be the first to comment..."
}) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !isSubmittingComment && newComment.trim()) {
      e.preventDefault();
      handleAddComment();
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Input
        value={newComment}
        onChange={(e) => setNewComment(e.target.value)}
        placeholder={placeholder}
        className="h-8 comment-input"
        onKeyDown={handleKeyDown}
        disabled={isSubmittingComment}
      />
      <Button
        size="sm"
        onClick={handleAddComment}
        disabled={!newComment.trim() || isSubmittingComment}
        className="h-8 px-3"
      >
        {isSubmittingComment ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          "Send"
        )}
      </Button>
    </div>
  );
}; 