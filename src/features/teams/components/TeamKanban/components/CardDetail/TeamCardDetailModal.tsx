import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from '@/components/ui/alert-dialog';
import { TeamKanbanCard } from '@/features/teams/types';
import { useKanbanStore, kanbanSelectors } from '@/features/teams/store/teamKanbanStore';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CheckCircle2, Plus, X, Loader2, Activity } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';

import { <PERSON><PERSON>eader } from './components/CardHeader';
import { CardDescription } from './components/CardDescription';
import { CardControls } from './components/CardControls';
import { SubtaskSection } from './components/SubtaskSection/SubtaskSection';
import { CommentSection } from './components/CommentSection/CommentSection';

import { useCardDetails } from './hooks/useCardDetails';
import { useSubtasks } from './hooks/useSubtasks';
import { useComments } from './hooks/useComments';

interface TeamCardDetailModalProps {
  card: TeamKanbanCard;
  onClose: () => void;
}

export const TeamCardDetailModal: React.FC<TeamCardDetailModalProps> = ({
  card: passedCard,
  onClose,
}) => {
  const {
    card,
    editedCard,
    setEditedCard,
    handleToggleComplete,
    handleUpdateCard,
    handleUpdateDueDate,
    handleUpdateAssignees,
    handleModalClose,
    handleDeleteCard
  } = useCardDetails(passedCard);

  const {
    subtasks,
    subtaskProgress,
    newSubtaskTitle,
    setNewSubtaskTitle,
    isSubmittingSubtask,
    initialLoadComplete: subtasksLoaded,
    handleAddSubtask,
    handleToggleSubtask,
    handleUpdateSubtaskDate,
    handleUpdateSubtaskAssignee,
    handleUpdateSubtaskTitle,
    handleDeleteSubtask,
    handleReorderSubtasks,
  } = useSubtasks(card.id);

  const {
    commentPages,
    loadingComments,
    localHasMoreComments,
    newComment,
    setNewComment,
    initialLoadComplete: commentsLoaded,
    editingCommentId,
    setEditingCommentId,
    editedCommentContent,
    setEditedCommentContent,
    openDropdown,
    setOpenDropdown,
    isSubmittingComment,
    scrollAreaRef,
    handleLoadMore,
    handleAddComment,
    handleEditComment,
    handleDeleteComment,
    currentUser
  } = useComments(card.id);

  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [showSystemComments, setShowSystemComments] = useState(false);

  // Get the column to get team_id
  const column = useKanbanStore(state => state.columns.find(col => col.id === card.column_id));
  const teamId = column?.team_id || '';

  // Get team members using the team_id from the column
  const teamMembers = useKanbanStore(teamId ? kanbanSelectors.selectTeamMembers(teamId) : () => []);
  const loadingTeamMembers = useKanbanStore(teamId ? kanbanSelectors.selectTeamMembersLoading(teamId) : () => false);

  const closeWithConfirmation = () => {
    handleModalClose();
    onClose();
  };

  const handleConfirmDelete = async () => {
    const success = await handleDeleteCard();
    if (success) {
      onClose();
    }
    setShowDeleteAlert(false);
  };

  const SubtaskLoadingSkeleton = () => (
    <div className="space-y-3">
      {[1, 2, 3].map((i) => (
        <div key={i} className="flex items-center gap-2">
          <Skeleton className="h-4 w-4 rounded-full" />
          <div className="flex-1">
            <Skeleton className="h-8 w-full" />
          </div>
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      ))}
    </div>
  );

  return (
    <>
      <Dialog open onOpenChange={closeWithConfirmation}>
        <DialogContent className="max-w-4xl min-w-[50vw] max-h-[95vh] min-h-[70vh] p-0 overflow-hidden w-[96vw] lg:w-[80vw]">
          <DialogHeader className="sr-only">
            <DialogTitle>Card Details</DialogTitle>
            <DialogDescription>View and edit card details, subtasks, and comments</DialogDescription>
          </DialogHeader>
          
          <div className="flex flex-col h-full">
            <div className="p-4 border-b space-y-2">
              <CardHeader
                editedCard={editedCard}
                handleToggleComplete={handleToggleComplete}
                handleUpdateCard={handleUpdateCard}
                setEditedCard={setEditedCard}
              />
              
              <CardDescription
                editedCard={editedCard}
                handleUpdateCard={handleUpdateCard}
                setEditedCard={setEditedCard}
              />
              
              <CardControls 
                editedCard={editedCard}
                teamMembers={teamMembers}
                loadingTeamMembers={loadingTeamMembers}
                subtaskProgress={subtaskProgress}
                handleUpdateDueDate={handleUpdateDueDate}
                handleUpdateAssignees={handleUpdateAssignees}
                setShowDeleteAlert={setShowDeleteAlert}
              />
            </div>
            
            <ScrollArea className="flex-1 h-full min-h-[50vh]">
              <div className="p-6">
                {editedCard.archived_at && (
                  <Alert className="mb-3">
                    <AlertDescription>
                      This card is archived. Uncheck the checkbox to unarchive it.
                    </AlertDescription>
                  </Alert>
                )}
                
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
                  <div className="lg:col-span-3 space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <h3 className="text-sm font-medium">Subtasks</h3>
                        <span className="text-xs text-muted-foreground">
                          ({subtaskProgress.completed}/{subtaskProgress.total})
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => setNewSubtaskTitle(' ')}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>

                    <ScrollArea className="h-[40vh] max-h-[40vh] overflow-auto" type="always">
                      <div className="pr-4">
                        {!subtasksLoaded ? (
                          <div className="py-2">
                            <SubtaskLoadingSkeleton />
                          </div>
                        ) : subtasks.length === 0 && newSubtaskTitle === '' ? (
                          <div className="flex flex-col items-center justify-center py-8 px-4 border-2 border-dashed rounded-lg bg-muted/40 min-h-[200px]">
                            <div className="flex flex-col items-center gap-2 text-center">
                              <CheckCircle2 className="w-8 h-8 text-muted-foreground/50" />
                              <div className="space-y-1">
                                <h3 className="font-medium text-muted-foreground">No subtasks yet</h3>
                                <p className="text-sm text-muted-foreground/70">Break down this task into smaller steps</p>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <>
                            {newSubtaskTitle !== '' && (
                              <div className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg mb-2">
                                <Input
                                  value={newSubtaskTitle}
                                  onChange={(e) => setNewSubtaskTitle(e.target.value)}
                                  placeholder="Enter subtask title..."
                                  className="h-8 subtask-input"
                                  disabled={isSubmittingSubtask}
                                  autoFocus
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter' && !isSubmittingSubtask && newSubtaskTitle.trim()) {
                                      e.preventDefault();
                                      handleAddSubtask();
                                    } else if (e.key === 'Escape') {
                                      setNewSubtaskTitle('');
                                    }
                                  }}
                                />
                                <Button
                                  size="sm"
                                  onClick={handleAddSubtask}
                                  disabled={!newSubtaskTitle.trim() || isSubmittingSubtask}
                                  className="h-8"
                                >
                                  {isSubmittingSubtask ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    'Add'
                                  )}
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setNewSubtaskTitle('')}
                                  disabled={isSubmittingSubtask}
                                  className="h-8 w-8 p-0"
                                >
                                  <X className="w-4 h-4" />
                                </Button>
                              </div>
                            )}
                            <SubtaskSection
                              subtasks={subtasks}
                              subtaskProgress={subtaskProgress}
                              newSubtaskTitle={newSubtaskTitle}
                              setNewSubtaskTitle={setNewSubtaskTitle}
                              isSubmittingSubtask={isSubmittingSubtask}
                              handleAddSubtask={handleAddSubtask}
                              handleToggleSubtask={handleToggleSubtask}
                              handleUpdateSubtaskAssignee={handleUpdateSubtaskAssignee}
                              handleUpdateSubtaskDate={handleUpdateSubtaskDate}
                              handleDeleteSubtask={handleDeleteSubtask}
                              handleUpdateSubtaskTitle={handleUpdateSubtaskTitle}
                              handleReorderSubtasks={handleReorderSubtasks}
                              users={teamMembers}
                            />
                          </>
                        )}
                      </div>
                    </ScrollArea>
                  </div>

                  <div className="lg:col-span-2 space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">Comments</h3>
                      <Button
                        variant={showSystemComments ? "secondary" : "ghost"}
                        size="sm"
                        className={`h-8 px-2 text-xs flex items-center gap-1 ${showSystemComments ? 'text-foreground' : 'text-muted-foreground hover:text-foreground'}`}
                        onClick={() => setShowSystemComments(prev => !prev)}
                      >
                        <Activity className="w-3.5 h-3.5" />
                        <span>{showSystemComments ? "Hide Activity" : "Show Activity"}</span>
                      </Button>
                    </div>
                    
                    <CommentSection
                      scrollAreaRef={scrollAreaRef}
                      commentPages={commentPages}
                      loadingComments={loadingComments}
                      localHasMoreComments={localHasMoreComments}
                      initialLoadComplete={commentsLoaded}
                      handleLoadMore={handleLoadMore}
                      newComment={newComment}
                      setNewComment={setNewComment}
                      isSubmittingComment={isSubmittingComment}
                      handleAddComment={handleAddComment}
                      editingCommentId={editingCommentId}
                      setEditingCommentId={setEditingCommentId}
                      editedCommentContent={editedCommentContent}
                      setEditedCommentContent={setEditedCommentContent}
                      openDropdown={openDropdown}
                      setOpenDropdown={setOpenDropdown}
                      handleEditComment={handleEditComment}
                      handleDeleteComment={handleDeleteComment}
                      currentUserId={currentUser?.id}
                      showSystemComments={showSystemComments}
                    />
                  </div>
                </div>
              </div>
            </ScrollArea>
          </div>
        </DialogContent>
      </Dialog>
      
      <AlertDialog open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to delete this card?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the card
              and all of its subtasks.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete} className="bg-destructive hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};