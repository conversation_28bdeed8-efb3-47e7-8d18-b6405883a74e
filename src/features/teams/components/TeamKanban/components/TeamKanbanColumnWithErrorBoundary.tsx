import React from 'react';
import { TeamKanbanColumn } from './TeamKanbanColumn';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { errorHandler, ErrorType } from '@/utils/errorHandler';
import type { TeamKanbanCard as TeamKanbanCardType } from '@/features/teams/types/kanban';
import type { User } from '@/features/teams/types';
import { PriorityLevel } from '@/features/teams/types';

// Define the props interface directly, mirroring the one in TeamKanbanColumn.tsx
interface TeamKanbanColumnProps {
    id: string;
    title: string;
    onCardClick: (card: TeamKanbanCardType) => void;
    onAddCard: (title: string, assigneeId?: string, priority?: PriorityLevel) => void;
    onDeleteColumn: () => void;
    onUpdateTitle: (title: string) => void;
    canManageColumn?: boolean;
    onToggleArchived?: () => void;
    dragHandleProps?: React.HTMLAttributes<HTMLDivElement>;
    dragHandleElement?: React.ReactNode;
    teamMembers?: User[];
    loadingTeamMembers?: boolean;
}

/**
 * TeamKanbanColumn with error boundary protection
 * This component wraps the TeamKanbanColumn with an error boundary to isolate failures
 */
export const TeamKanbanColumnWithErrorBoundary = (props: TeamKanbanColumnProps) => {
    return (
        <ErrorBoundary
            componentName="TeamKanbanColumn"
            onError={(error, info) => {
                errorHandler.captureError(error, ErrorType.UNKNOWN, {
                    component: 'TeamKanbanColumn',
                    action: 'render',
                    context: {
                        columnId: props.id,
                        componentStack: info.componentStack
                    }
                });
            }}
            // Custom fallback for column errors to show a more specific message
            fallbackRender={({ resetErrorBoundary }) => (
                <div className="w-[350px] shrink-0 h-full flex flex-col">
                    <div className="bg-card rounded-lg border shadow-sm h-full flex flex-col">
                        <div className="px-3 py-2 flex items-center gap-2 bg-muted/50 rounded-t-lg border-b">
                            <h3 className="text-sm font-medium truncate px-2 py-1 rounded">
                                {props.title || 'Column'}
                            </h3>
                        </div>
                        <div className="p-4 text-center flex-1 flex flex-col items-center justify-center">
                            <p className="text-sm text-muted-foreground mb-4">
                                This column encountered an error and couldn't be displayed.
                            </p>
                            <button
                                onClick={resetErrorBoundary}
                                className="inline-flex items-center justify-center rounded-md bg-primary px-3 py-2 text-sm font-medium text-primary-foreground shadow-sm hover:bg-primary/90"
                            >
                                Retry
                            </button>
                        </div>
                    </div>
                </div>
            )}
        >
            <TeamKanbanColumn {...props} />
        </ErrorBoundary>
    );
};