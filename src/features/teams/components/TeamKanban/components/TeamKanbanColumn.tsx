// This file uses React components from dnd-kit which require TypeScript configuration adjustments
// The following line enables importing React directly despite the TypeScript errors
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import React, { useEffect, useRef, useMemo, useCallback, useState } from 'react';
import { SortableCard } from './SortableCard';
import type { TeamKanbanCard as TeamKanbanCardType } from '@/features/teams/types/kanban';
import type { User } from '@/features/teams/types';
import { PriorityLevel } from '@/features/teams/types';
import { Plus, MoreVertical, Archive, ArrowDownUp, CalendarDays, Trash2, Loader2, Flag } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { useKanbanStore, kanbanSelectors } from '@/features/teams/store/teamKanbanStore';
import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { asKanbanCard, normalizeCardSubtasks } from '@/features/teams/utils/typeAdapters';
import { filterSelectors } from '@/features/teams/store/slices/filters';
import { AssigneePicker } from './CardDetail/components/shared/AssigneePicker';
import { PriorityPicker } from './CardDetail/components/shared/PriorityPicker';

// Define sort types
type SortType = 'order' | 'priority' | 'due_date';

interface TeamKanbanColumnProps {
    id: string;
    title: string;
    onCardClick: (card: TeamKanbanCardType) => void;
    onAddCard: (title: string, assigneeId?: string, priority?: PriorityLevel) => void;
    onDeleteColumn: () => void;
    onUpdateTitle: (title: string) => void;
    canManageColumn?: boolean;
    onToggleArchived?: () => void;
    dragHandleProps?: React.HTMLAttributes<HTMLDivElement>;
    dragHandleElement?: React.ReactNode;
    teamMembers?: User[];
    loadingTeamMembers?: boolean;
}

// Update CardDropIndicator component to show a ghost card
interface CardDropIndicatorProps {
    isVisible: boolean;
    style?: React.CSSProperties;
}

const CardDropIndicator = ({ isVisible, style }: CardDropIndicatorProps) => {
    if (!isVisible) return null;

    return (
        <div
            className={cn(
                "absolute left-0 right-0",
                "bg-primary/10 border-2 border-primary/20 rounded-lg",
                "h-[80px]"
            )}
            style={{
                ...style,
                pointerEvents: 'none',
                zIndex: 1,
            }}
        />
    );
};

// Update grid config to more reasonable values
const GRID_CONFIG = {
    sizes: {
        sm: {
            card: 80,
            progressBar: 20,
            gap: 12,
            padding: 12
        },
        md: {
            card: 96,
            progressBar: 24,
            gap: 12,
            padding: 16
        },
        lg: {
            card: 112,
            progressBar: 28,
            gap: 12,
            padding: 16
        }
    },
    breakpoints: {
        sm: 640,
        md: 768,
        lg: 1024
    }
};

// Get grid configuration based on viewport width
const getGridConfig = () => {
    const width = typeof window !== 'undefined' ? window.innerWidth : 0;
    if (width >= GRID_CONFIG.breakpoints.lg) return GRID_CONFIG.sizes.lg;
    if (width >= GRID_CONFIG.breakpoints.md) return GRID_CONFIG.sizes.md;
    return GRID_CONFIG.sizes.sm;
};

interface GridDimensions {
    cardHeight: number;
    progressBarHeight: number;
    gap: number;
    padding: number;
}

interface GridPosition {
    index: number;
    gridY: number;
    dimensions: GridDimensions;
}

// Update the calculateGridPositions function to ensure consistent spacing
const calculateGridPositions = (
    cards: TeamKanbanCardType[],
    dimensions: GridDimensions
): Map<string, GridPosition> => {
    const positions = new Map<string, GridPosition>();

    // Early return for empty cards
    if (cards.length === 0) return positions;

    let currentY = dimensions.padding || 0;

    cards.forEach((card, index) => {
        // Always use the same total height for all cards for consistent spacing
        // This ensures cards without progress bars still have consistent spacing
        const totalHeight = dimensions.cardHeight + dimensions.progressBarHeight;

        positions.set(card.id, {
            index,
            gridY: currentY,
            dimensions
        });

        // Use standard spacing between cards
        currentY += totalHeight + dimensions.gap;
    });

    return positions;
};

// The findDropPosition function has been moved to ../utils/dropPositionUtils.ts

export const TeamKanbanColumn = React.memo(({
    id,
    title: initialTitle,
    onCardClick,
    onAddCard,
    onDeleteColumn,
    onUpdateTitle,
    canManageColumn = true,
    onToggleArchived,
    dragHandleProps,
    dragHandleElement,
    teamMembers = [],
    loadingTeamMembers = false
}: TeamKanbanColumnProps) => {

    const [isEditing, setIsEditing] = useState(false);
    const [title, setTitle] = useState(initialTitle);
    const [isAddingCard, setIsAddingCard] = useState(false);
    const [newCardTitle, setNewCardTitle] = useState('');
    const [selectedAssignee, setSelectedAssignee] = useState<User | null>(null);
    const [selectedPriority, setSelectedPriority] = useState<PriorityLevel>(PriorityLevel.P3);
    const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
    const [sortType, setSortType] = useState<SortType>(() => {
        // Initialize from localStorage or default to 'order'
        const saved = localStorage.getItem(`kanban-column-${id}-sort`);
        return (saved as SortType) || 'order';
    });
    const columnRef = useRef<HTMLDivElement | null>(null);
    const lastCardRef = useRef<HTMLDivElement | null>(null);
    const observerRef = useRef<IntersectionObserver | null>(null);
    const scrollContainerRef = useRef<HTMLDivElement | null>(null);
    const [scrollPosition, setScrollPosition] = useState<number>(0);

    // Use the new store access pattern with selectors
    const columnData = useKanbanStore(state => state.columnData.get(id));
    const fetchColumnCards = useKanbanStore(state => state.fetchColumnCards);
    const isColumnArchived = useKanbanStore(kanbanSelectors.selectIsColumnArchived(id));
    const hasMoreCards = useKanbanStore(kanbanSelectors.selectHasMoreCards(id));
    const loadingCards = useKanbanStore(kanbanSelectors.selectCardLoadingState(id));

    // Use filter selectors directly from store with memoization
    const filteredColumnCards = useKanbanStore(
        useMemo(() => filterSelectors.selectFilteredColumnCards(id), [id])
    );

    const handleTitleSubmit = useCallback(() => {
        if (title.trim() !== initialTitle) {
            onUpdateTitle(title.trim());
        }
        setIsEditing(false);
    }, [title, initialTitle, onUpdateTitle]);

    const handleAddCard = useCallback(() => {
        if (newCardTitle.trim()) {
            onAddCard(
                newCardTitle.trim(),
                selectedAssignee?.id,
                selectedPriority
            );
            // Reset form
            setNewCardTitle('');
            setSelectedAssignee(null);
            setSelectedPriority(PriorityLevel.P3);
            setShowAdvancedOptions(false);
            setTimeout(() => {
                const inputElement = document.querySelector('.kanban-column__body form input');
                if (inputElement instanceof HTMLInputElement) {
                    inputElement.focus();
                }
            }, 50);
        }
    }, [newCardTitle, selectedAssignee, selectedPriority, onAddCard]);

    // Set up intersection observer for infinite scrolling
    useEffect(() => {
        if (!lastCardRef.current || !hasMoreCards || loadingCards) return;

        // Save the current scroll position before fetching more cards
        const saveScrollPosition = () => {
            if (scrollContainerRef.current) {
                setScrollPosition(scrollContainerRef.current.scrollTop);
            }
        };

        const observer = new IntersectionObserver(entries => {
            if (entries[0].isIntersecting && !loadingCards && hasMoreCards) {
                saveScrollPosition();
                const currentPage = columnData?.currentPage || 0;
                fetchColumnCards(id, currentPage + 1, undefined, isColumnArchived);
            }
        }, {
            threshold: 0.2, // Increased threshold for earlier detection
            rootMargin: '100px' // Load sooner when approaching the bottom
        });

        observer.observe(lastCardRef.current);
        observerRef.current = observer;

        return () => {
            observer.disconnect();
        };
    }, [id, hasMoreCards, loadingCards, columnData?.currentPage, fetchColumnCards, isColumnArchived]);

    // Separate memoized function for type conversion
    const normalizedFilteredCards = useMemo(() => {
        return filteredColumnCards.map(card => normalizeCardSubtasks(asKanbanCard(card)));
    }, [filteredColumnCards]);

    // Create a sorting function based on the sort type
    const sortCards = useMemo(() => (cards: TeamKanbanCardType[]) => {
        if (!cards || !Array.isArray(cards)) return [];

        return [...cards].sort((a, b) => {
            if (sortType === 'order') {
                // Sort by position_updated_at (newest first)
                const aTime = new Date(a.position_updated_at).getTime();
                const bTime = new Date(b.position_updated_at).getTime();
                return bTime - aTime; // Descending order - newer items first
            }
            if (sortType === 'priority') {
                const priorityOrder: Record<string, number> = { P1: 0, P2: 1, P3: 2 };
                const aPriority = a.priority || 'P3';
                const bPriority = b.priority || 'P3';
                const orderDiff = priorityOrder[aPriority] - priorityOrder[bPriority];
                // If same priority, sort by position_updated_at (newest first)
                return orderDiff === 0 ?
                    new Date(b.position_updated_at).getTime() - new Date(a.position_updated_at).getTime() :
                    orderDiff;
            } else {
                // Sort by due date, putting cards without due dates at the end
                if (!a.due_date && !b.due_date) {
                    // If no due dates, sort by position_updated_at
                    return new Date(b.position_updated_at).getTime() - new Date(a.position_updated_at).getTime();
                }
                if (!a.due_date) return 1;
                if (!b.due_date) return -1;
                return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
            }
        });
    }, [sortType]);

    // Simplified function to only handle sorting of pre-filtered, pre-normalized cards
    const sortedCardsForRendering = useMemo(() => {
        if (!columnData?.cards) return [];
        return sortCards(normalizedFilteredCards);
    }, [columnData?.cards, normalizedFilteredCards, sortCards]);

    // Add new state for drop indicator
    const [dropIndicatorY, setDropIndicatorY] = useState<number | null>(null);

    // Memoize the grid dimensions to prevent unnecessary recalculations
    const [gridDimensions, setGridDimensions] = useState<GridDimensions>(() => ({
        cardHeight: GRID_CONFIG.sizes.md.card,
        progressBarHeight: GRID_CONFIG.sizes.md.progressBar,
        gap: GRID_CONFIG.sizes.md.gap,
        padding: GRID_CONFIG.sizes.md.padding
    }));

    // Optimize resize handler with useCallback
    const handleResize = useCallback(() => {
        const config = getGridConfig();
        setGridDimensions(prev => {
            // Only update state if dimensions actually changed
            if (
                prev.cardHeight === config.card &&
                prev.progressBarHeight === config.progressBar &&
                prev.gap === config.gap &&
                prev.padding === config.padding
            ) {
                return prev; // No change, return existing state
            }

            return {
                cardHeight: config.card,
                progressBarHeight: config.progressBar,
                gap: config.gap,
                padding: config.padding
            };
        });
    }, []);

    // Update grid dimensions on window resize - with debounce
    useEffect(() => {
        // Initial call
        handleResize();

        // Debounce resize for better performance
        let timeoutId: NodeJS.Timeout;
        const debouncedResize = () => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(handleResize, 100);
        };

        window.addEventListener('resize', debouncedResize);
        return () => {
            clearTimeout(timeoutId);
            window.removeEventListener('resize', debouncedResize);
        };
    }, [handleResize]);

    // Memoize the grid position calculation function
    const calculatePositions = useCallback((cards: TeamKanbanCardType[], dimensions: GridDimensions) => {
        return calculateGridPositions(cards, dimensions);
    }, []);

    // Calculate grid positions - optimize with useMemo instead of useState + useEffect
    const gridPositions = useMemo(() => {
        return calculatePositions(sortedCardsForRendering, gridDimensions);
    }, [calculatePositions, sortedCardsForRendering, gridDimensions]);

    // Single useDroppable hook with all needed functionality
    const { setNodeRef, isOver, active } = useDroppable({
        id: `column-${id}`,
        data: {
            type: 'column',
            columnId: id,
            actualColumnId: id,
            gridPositions,
            gridSize: GRID_CONFIG.sizes.md,
            gridGap: GRID_CONFIG.sizes.md.gap,
            containerPadding: GRID_CONFIG.sizes.md.padding
        }
    });

    // Update drag over handling
    useEffect(() => {
        // Skip calculations completely if not needed
        if (!isOver || !active || !columnRef.current) {
            if (dropIndicatorY !== null) {
                setDropIndicatorY(null);
            }
            return;
        }

        // Use requestAnimationFrame to optimize DOM calculations
        const rafId = requestAnimationFrame(() => {
            if (!columnRef.current) return;

            const columnRect = columnRef.current.getBoundingClientRect();
            const mouseY = active.rect.current.translated?.top ?? active.rect.current.initial?.top ?? 0;

            // Get current card elements - more efficient than querying all and filtering
            const cardElements = columnRef.current.querySelectorAll('[data-card-id]');

            // Skip if dragging over the source card
            const draggedId = active.data.current?.id;
            const filteredCards = Array.from(cardElements).filter(el =>
                el.getAttribute('data-card-id') !== draggedId
            );

            // Optimize for empty column case
            if (filteredCards.length === 0) {
                columnRef.current.dataset.dropTargetIndex = "0";
                setDropIndicatorY(gridDimensions.padding);
                return;
            }

            // More efficient drop position calculation with early returns
            const relativeMouseY = mouseY - columnRect.top;
            let targetIndex = filteredCards.length;
            let targetY = 0;

            for (let i = 0; i < filteredCards.length; i++) {
                const cardElement = filteredCards[i] as HTMLElement;
                const cardRect = cardElement.getBoundingClientRect();
                const cardY = cardRect.top - columnRect.top;
                const cardMiddleY = cardY + (cardRect.height / 2);

                if (relativeMouseY < cardMiddleY) {
                    targetIndex = i;
                    targetY = cardY - (gridDimensions.gap / 2);
                    break; // Early return when we find the position
                }

                // If we're at the last card and still haven't found a position,
                // position indicator after the last card
                if (i === filteredCards.length - 1) {
                    targetY = (cardRect.top - columnRect.top) + cardRect.height + (gridDimensions.gap / 2);
                }
            }

            // Update column data attributes for drop handling
            columnRef.current.dataset.dropTargetIndex = targetIndex.toString();
            setDropIndicatorY(targetY);
        });

        return () => cancelAnimationFrame(rafId);
    }, [isOver, active, gridDimensions, dropIndicatorY]);

    // Persist sort type to localStorage
    useEffect(() => {
        localStorage.setItem(`kanban-column-${id}-sort`, sortType);
    }, [sortType, id]);

    // Restore scroll position after loading completes
    useEffect(() => {
        if (!loadingCards && scrollPosition > 0 && scrollContainerRef.current) {
            // Use requestAnimationFrame to ensure DOM is updated
            requestAnimationFrame(() => {
                if (scrollContainerRef.current) {
                    scrollContainerRef.current.scrollTop = scrollPosition;
                }
            });
        }
    }, [loadingCards, scrollPosition]);

    const toggleArchived = useCallback(() => {
        if (!onToggleArchived) return;

        // Call the onToggleArchived prop to update store state
        // This will internally handle fetching the cards with the new archived state
        if (onToggleArchived) {
            onToggleArchived();
        }
    }, [onToggleArchived, isColumnArchived, id]);

    return (
        <div
            ref={(node) => {
                // Apply both refs
                columnRef.current = node;
                setNodeRef(node);
            }}
            className="w-[350px] shrink-0 h-full flex flex-col"
            data-column-id={id}
            data-actual-column-id={id}
        >
            <div className="bg-card rounded-lg border shadow-sm h-full flex flex-col">
                <div className="px-3 py-2 flex items-center gap-2 bg-muted/50 rounded-t-lg border-b sticky top-0 z-10">
                    {isEditing ? (
                        <form
                            onSubmit={(e) => {
                                e.preventDefault();
                                handleTitleSubmit();
                            }}
                            className="flex-1"
                        >
                            <Input
                                value={title}
                                onChange={(e) => setTitle(e.target.value)}
                                onBlur={handleTitleSubmit}
                                className="h-7 px-2 text-sm font-medium bg-background"
                                autoFocus
                            />
                        </form>
                    ) : (
                        <div className="flex items-center flex-1">
                            {dragHandleElement && (
                                <div {...dragHandleProps}>
                                    {dragHandleElement}
                                </div>
                            )}
                            <h3
                                className={cn(
                                    "text-sm font-medium truncate px-2 py-1 rounded",
                                    canManageColumn && "hover:bg-accent/50 cursor-pointer"
                                )}
                                onDoubleClick={() => canManageColumn && setIsEditing(true)}
                            >
                                {title}
                            </h3>
                            {isColumnArchived && (
                                <div className="flex items-center ml-2 px-1.5 py-0.5 bg-yellow-100 dark:bg-yellow-950 text-yellow-800 dark:text-yellow-300 rounded text-xs font-medium">
                                    <Archive className="h-3 w-3 mr-1" />
                                    Archived View
                                </div>
                            )}
                        </div>
                    )}

                    <div className="flex items-center gap-1">
                        {!isColumnArchived && (
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-7 w-7"
                                onClick={() => setIsAddingCard(true)}
                            >
                                <Plus className="h-4 w-4" />
                            </Button>
                        )}

                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-7 w-7">
                                    <MoreVertical className="h-4 w-4" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                                <DropdownMenuItem
                                    onClick={() => setSortType('order')}
                                    className={cn("gap-2", sortType === 'order' && "bg-accent")}
                                >
                                    <ArrowDownUp className="h-4 w-4" />
                                    Sort by Order
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    onClick={() => setSortType('priority')}
                                    className={cn("gap-2", sortType === 'priority' && "bg-accent")}
                                >
                                    <Flag className="h-4 w-4" />
                                    Sort by Priority
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    onClick={() => setSortType('due_date')}
                                    className={cn("gap-2", sortType === 'due_date' && "bg-accent")}
                                >
                                    <CalendarDays className="h-4 w-4" />
                                    Sort by Due Date
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                    onClick={() => {
                                        // Only toggle column-specific archived view
                                        toggleArchived();
                                    }}
                                    className="gap-2"
                                >
                                    <Archive className="h-4 w-4" />
                                    {isColumnArchived ? 'Show Active Cards' : 'Show Archived Cards'}
                                </DropdownMenuItem>
                                {canManageColumn && (
                                    <>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                            onClick={onDeleteColumn}
                                            className="text-destructive gap-2"
                                        >
                                            <Trash2 className="h-4 w-4" />
                                            Delete Column
                                        </DropdownMenuItem>
                                    </>
                                )}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                </div>

                <div className="kanban-column__body flex-1 overflow-y-auto" ref={scrollContainerRef}>
                    {loadingCards && columnData?.cards.length === 0 ? (
                        <div className="kanban-column__loading flex items-center justify-center p-8">
                            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                        </div>
                    ) : sortedCardsForRendering.length === 0 ? (
                        <div className="kanban-column__empty p-8 text-center">
                            <p className="text-muted-foreground">
                                No cards in this column
                            </p>
                        </div>
                    ) : (
                        <>
                            <div className="px-2 pt-2">
                                <SortableContext
                                    items={sortedCardsForRendering.map(card => card.id)}
                                    strategy={verticalListSortingStrategy}
                                    id={`column-${id}`}
                                >
                                    {sortedCardsForRendering.map((card, index) => {
                                        const position = gridPositions.get(card.id);
                                        if (!position) return null;

                                        // Use consistent height for all cards regardless of subtasks
                                        const cardHeight = position.dimensions.cardHeight + position.dimensions.progressBarHeight;

                                        // Set ref for the last card for infinite scrolling
                                        const isLastCard = index === sortedCardsForRendering.length - 1;

                                        return (
                                            <div
                                                key={card.id}
                                                ref={isLastCard ? lastCardRef : undefined}
                                                data-card-id={card.id}
                                                className="relative w-full mb-3"
                                                style={{
                                                    minHeight: cardHeight
                                                }}
                                            >
                                                <SortableCard
                                                    item={card}
                                                    onClick={() => onCardClick(card)}
                                                />
                                            </div>
                                        );
                                    })}
                                </SortableContext>
                            </div>
                            {dropIndicatorY !== null && <CardDropIndicator isVisible={isOver} style={{ top: dropIndicatorY }} />}
                        </>
                    )}
                </div>

                {/* Error and Loading States */}
                {columnData?.error && (
                    <Alert variant="destructive" className="mt-2 mb-2">
                        <AlertDescription className="flex items-center justify-between">
                            {columnData.error}
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => fetchColumnCards(id, 1, undefined, isColumnArchived)}
                            >
                                Retry
                            </Button>
                        </AlertDescription>
                    </Alert>
                )}

                {/* Loading Indicator - Only show when loading and no cards are displayed yet */}
                {loadingCards && !sortedCardsForRendering.length && (
                    <div className="w-full h-20 flex items-center justify-center text-muted-foreground">
                        <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                        Loading cards...
                    </div>
                )}

                {/* Update the "Add Card" functionality to only use the + button in the header */}
                {isAddingCard && !isColumnArchived && (
                    <div className="sticky bottom-0 left-0 right-0 mt-2 bg-gradient-to-b from-transparent to-background pt-2 pb-2 px-3">
                        <form
                            onSubmit={(e) => {
                                e.preventDefault();
                                handleAddCard();
                                // Prevent form default behavior which might reset form state
                                return false;
                            }}
                            className="bg-card rounded-md shadow border p-3"
                        >
                            <div className="space-y-3">
                                <Input
                                    placeholder="Enter card title..."
                                    value={newCardTitle}
                                    onChange={(e) => setNewCardTitle(e.target.value)}
                                    className="w-full border-muted"
                                    autoFocus
                                />

                                {/* Optional fields toggle */}
                                <div className="flex items-center justify-between">
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                                        className="text-xs text-muted-foreground hover:text-foreground"
                                    >
                                        {showAdvancedOptions ? 'Hide options' : 'Add assignee & priority'}
                                    </Button>
                                </div>

                                {/* Advanced options */}
                                {showAdvancedOptions && (
                                    <div className="space-y-2 pt-2 border-t border-muted">
                                        <div className="flex items-center gap-2">
                                            <span className="text-xs text-muted-foreground min-w-[60px]">Assignee:</span>
                                            <AssigneePicker
                                                assignee={selectedAssignee ? {
                                                    id: selectedAssignee.id,
                                                    name: selectedAssignee.name,
                                                    avatar: selectedAssignee.avatar
                                                } : null}
                                                teamMembers={teamMembers}
                                                loadingTeamMembers={loadingTeamMembers}
                                                onUpdateAssignees={(users) => {
                                                    setSelectedAssignee(users.length > 0 ? users[0] : null);
                                                }}
                                                className="h-7"
                                            />
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="text-xs text-muted-foreground min-w-[60px]">Priority:</span>
                                            <PriorityPicker
                                                priority={selectedPriority}
                                                onChange={setSelectedPriority}
                                                className="text-xs"
                                            />
                                        </div>
                                    </div>
                                )}

                                <div className="flex justify-end gap-2">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                            setIsAddingCard(false);
                                            setNewCardTitle('');
                                            setSelectedAssignee(null);
                                            setSelectedPriority(PriorityLevel.P3);
                                            setShowAdvancedOptions(false);
                                        }}
                                    >
                                        Cancel
                                    </Button>
                                    <Button
                                        type="submit"
                                        size="sm"
                                        disabled={!newCardTitle.trim()}
                                    >
                                        Add Card
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </div>
                )}

                {isColumnArchived && (!columnData?.cards || columnData.cards.length === 0) && !loadingCards && (
                    <div className="flex flex-col items-center justify-center p-4 mt-4 text-center text-muted-foreground">
                        <Archive className="h-12 w-12 mb-2 opacity-20" />
                        <p>No archived cards</p>
                        <p className="text-xs mt-1">Archived cards will appear here</p>
                    </div>
                )}
            </div>
        </div>
    );
}, (prevProps: TeamKanbanColumnProps, nextProps: TeamKanbanColumnProps) => {
    // Custom comparison function for memo
    return (
        prevProps.id === nextProps.id &&
        prevProps.title === nextProps.title &&
        prevProps.canManageColumn === nextProps.canManageColumn
    );
});

TeamKanbanColumn.displayName = 'TeamKanbanColumn';