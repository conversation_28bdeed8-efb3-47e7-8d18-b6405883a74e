import React, { useMemo, useCallback, useState } from 'react';
import { useKanbanStore, kanbanSelectors, useTeamKanbanStore } from '@/features/teams/store/teamKanbanStore';
import { filterSelectors } from '@/features/teams/store/slices/filters';
import { PriorityLevel } from '@/features/teams/types/kanban';
import { RotateCcw, Filter, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
    DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

const priorityLevels = [PriorityLevel.P1, PriorityLevel.P2, PriorityLevel.P3] as const;

const priorityColors = {
    [PriorityLevel.P1]: 'bg-red-500 text-white',
    [PriorityLevel.P2]: 'bg-yellow-500 text-black',
    [PriorityLevel.P3]: 'bg-gray-500 text-white'
} as const;

/**
 * Props for the TeamKanbanQuickFilters component
 */
interface TeamKanbanQuickFiltersProps {
    /**
     * Optional className for styling
     */
    className?: string;
    /**
     * Team ID to fetch team members for
     */
    teamId: string;
}

/**
 * A component that provides quick filters for the Kanban board
 * Includes only priority and assignee filters
 */
export const TeamKanbanQuickFilters = React.memo(({
    className,
    teamId
}: TeamKanbanQuickFiltersProps) => {
    // Store readiness state
    const [storeReady, setStoreReady] = useState(false);
    const [storeError, setStoreError] = useState<Error | null>(null);

    // Use filter selectors and actions from the store (unconditionally)
    const filters = useKanbanStore(filterSelectors.selectFilters);
    const setPriorityFilter = useKanbanStore(state => state.setPriorityFilter);
    const setAssigneeFilter = useKanbanStore(state => state.setAssigneeFilter);
    const resetFilters = useKanbanStore(state => state.resetFilters);

    // Create memoized selector functions, not memoized selector results
    const selectTeamMembers = useMemo(() => kanbanSelectors.selectTeamMembers(teamId), [teamId]);
    const selectTeamMembersLoading = useMemo(() => kanbanSelectors.selectTeamMembersLoading(teamId), [teamId]);

    // Get team members for assignee filtering (instead of all users)
    const teamMembers = useKanbanStore(selectTeamMembers);
    const teamMembersLoading = useKanbanStore(selectTeamMembersLoading);

    // Get the raw fetchTeamMembers function from the store with a safe accessor
    const fetchTeamMembers = useCallback((id: string) => {
        try {
            const store = useTeamKanbanStore.getState();
            if (typeof store.fetchTeamMembers === 'function') {
                return store.fetchTeamMembers(id);
            } else {
                console.error('fetchTeamMembers is not a function in store');
                return Promise.resolve();
            }
        } catch (err: unknown) {
            console.error('Error calling fetchTeamMembers:', err);
            return Promise.resolve();
        }
    }, []);

    // Check if the store has necessary methods before proceeding
    React.useEffect(() => {
        try {
            const store = useTeamKanbanStore.getState();

            // Check if critical store functions exist
            const hasFetchTeamMembers = typeof store.fetchTeamMembers === 'function';
            const hasFilterActions = typeof store.setPriorityFilter === 'function' &&
                typeof store.setAssigneeFilter === 'function';

            if (!hasFetchTeamMembers || !hasFilterActions) {
                console.warn('Kanban store is not fully initialized. Some functions are missing.', {
                    hasFetchTeamMembers,
                    hasFilterActions
                });
                setStoreError(new Error('Store not fully initialized'));
                return;
            }

            setStoreReady(true);
        } catch (err) {
            console.error('Error accessing Kanban store:', err);
            setStoreError(err instanceof Error ? err : new Error(String(err)));
        }
    }, []);

    // Fetch team members if they're not already loaded
    React.useEffect(() => {
        if (storeReady && !teamMembers.length && !teamMembersLoading) {
            fetchTeamMembers(teamId).catch((err: unknown) => {
                console.error('Error fetching team members:', err);
            });
        }
    }, [teamId, teamMembers.length, teamMembersLoading, fetchTeamMembers, storeReady]);

    // Show active filters count
    const activeFiltersCount = useMemo(() => {
        let count = 0;
        if (filters.priorityFilter !== null) count++;
        if (filters.assigneeFilter !== null) count++;
        return count;
    }, [filters]);

    // If the store isn't ready yet, show a minimal loading state
    if (!storeReady) {
        return (
            <div className={cn("flex items-center space-x-2", className)}>
                <Button variant="outline" size="sm" className="flex items-center" disabled>
                    {storeError ? (
                        <Filter className="h-4 w-4 mr-2 text-muted-foreground" />
                    ) : (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    )}
                    Filters
                </Button>
            </div>
        );
    }

    return (
        <div className={cn("flex items-center space-x-2", className)}>
            {/* Main Filter Dropdown */}
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button
                        variant={activeFiltersCount > 0 ? "secondary" : "outline"}
                        size="sm"
                        className="flex items-center"
                    >
                        <Filter className="h-4 w-4 mr-2" />
                        Filters {activeFiltersCount > 0 && `(${activeFiltersCount})`}
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuItem className="flex flex-col items-start w-full p-0">
                        <div className="px-2 py-1.5 text-sm font-medium w-full">Priority</div>
                        <div className="w-full pl-2">
                            <DropdownMenuItem
                                onClick={() => setPriorityFilter(null)}
                                className={cn("text-sm", !filters.priorityFilter && "bg-accent")}
                            >
                                All Priorities
                            </DropdownMenuItem>
                            {priorityLevels.map((priority) => (
                                <DropdownMenuItem
                                    key={priority}
                                    onClick={() => setPriorityFilter(priority)}
                                    className={cn("text-sm", filters.priorityFilter === priority && "bg-accent")}
                                >
                                    <div className="flex items-center">
                                        <span className={cn(
                                            "w-6 h-5 rounded-sm mr-2 flex items-center justify-center text-xs",
                                            priorityColors[priority]
                                        )}>
                                            {priority}
                                        </span>
                                        {priority === PriorityLevel.P1 && "High"}
                                        {priority === PriorityLevel.P2 && "Medium"}
                                        {priority === PriorityLevel.P3 && "Low"}
                                    </div>
                                </DropdownMenuItem>
                            ))}
                        </div>
                    </DropdownMenuItem>

                    <DropdownMenuSeparator />

                    <DropdownMenuItem className="flex flex-col items-start w-full p-0">
                        <div className="px-2 py-1.5 text-sm font-medium w-full">Assignee</div>
                        <div className="w-full pl-2 max-h-48 overflow-y-auto">
                            <DropdownMenuItem
                                onClick={() => setAssigneeFilter(null)}
                                className={cn("text-sm", !filters.assigneeFilter && "bg-accent")}
                            >
                                All Assignees
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                onClick={() => setAssigneeFilter('unassigned')}
                                className={cn("text-sm", filters.assigneeFilter === 'unassigned' && "bg-accent")}
                            >
                                <span>Unassigned</span>
                            </DropdownMenuItem>
                            {teamMembers && teamMembers.length > 0 && (
                                <>
                                    {teamMembers.map(user => (
                                        <DropdownMenuItem
                                            key={user.id}
                                            onClick={() => setAssigneeFilter(user.id)}
                                            className={cn("text-sm", filters.assigneeFilter === user.id && "bg-accent")}
                                        >
                                            <div className="flex items-center">
                                                <Avatar className="h-6 w-6 mr-2">
                                                    <AvatarImage src={user.avatar} />
                                                    <AvatarFallback>
                                                        {user.name.split(' ').map(n => n[0]).join('')}
                                                    </AvatarFallback>
                                                </Avatar>
                                                <span>{user.name}</span>
                                            </div>
                                        </DropdownMenuItem>
                                    ))}
                                </>
                            )}
                        </div>
                    </DropdownMenuItem>

                    {activeFiltersCount > 0 && (
                        <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                                onClick={resetFilters}
                                className="gap-2"
                            >
                                <RotateCcw className="h-4 w-4" />
                                Reset Filters
                            </DropdownMenuItem>
                        </>
                    )}
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
});

TeamKanbanQuickFilters.displayName = 'TeamKanbanQuickFilters';

export default TeamKanbanQuickFilters;