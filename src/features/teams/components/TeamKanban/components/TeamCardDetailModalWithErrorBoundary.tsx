import { TeamCardDetailModal } from './CardDetail';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { errorHandler, ErrorType } from '@/utils/errorHandler';
import type { TeamKanbanCard } from '@/features/teams/types';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

interface TeamCardDetailModalProps {
    card: TeamKanbanCard;
    onClose: () => void;
}

/**
 * TeamCardDetailModal with error boundary protection
 * 
 * This wrapper adds error isolation to the card detail modal to prevent
 * an error in the modal from breaking the entire board
 */
export const TeamCardDetailModalWithErrorBoundary = (props: TeamCardDetailModalProps) => {
    const { card, onClose } = props;

    return (
        <ErrorBoundary
            componentName="TeamCardDetailModal"
            onError={(error, info) => {
                errorHandler.captureError(error, ErrorType.UNKNOWN, {
                    component: 'TeamCardDetailModal',
                    action: 'render',
                    context: {
                        cardId: card.id,
                        cardTitle: card.title,
                        componentStack: info.componentStack
                    }
                });
            }}
            // Custom fallback that allows users to close the modal on error
            fallbackRender={({ resetErrorBoundary }) => (
                <Dialog open={true} onOpenChange={() => onClose()}>
                    <DialogContent className="max-w-4xl min-w-[50vw] max-h-[95vh] min-h-[70vh] p-0 overflow-hidden w-[96vw] lg:w-[80vw]">
                        <DialogHeader>
                            <DialogTitle>Error Loading Card Details</DialogTitle>
                        </DialogHeader>
                        <div className="p-6 text-center">
                            <p className="text-sm text-muted-foreground mb-4">
                                An error occurred while loading the card details.
                                You may try again or close this dialog.
                            </p>
                            <div className="flex justify-center space-x-4">
                                <Button variant="outline" onClick={onClose}>
                                    Close
                                </Button>
                                <Button onClick={resetErrorBoundary}>
                                    Try Again
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            )}
            resetOnUpdate={true}
        >
            <TeamCardDetailModal card={card} onClose={onClose} />
        </ErrorBoundary>
    );
}; 