import { TeamKanbanCard } from './TeamKanbanCard';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { errorHandler, ErrorType } from '@/utils/errorHandler';
import type { TeamKanbanCard as TeamKanbanCardType } from '@/features/teams/types/kanban';
import type { UnifiedKanbanCard } from '@/features/teams/types/unified';

/**
 * Props for the TeamKanbanCard component with error boundary
 */
interface TeamKanbanCardProps {
    /**
     * The card item to display - can be either a kanban card type or a unified card type
     * Will be internally converted to kanban type
     */
    item: TeamKanbanCardType | UnifiedKanbanCard;
    /**
     * Click handler for the card
     */
    onClick: () => void;
}

/**
 * TeamKanbanCard with error boundary protection
 * 
 * This wrapper adds error isolation to individual cards to prevent a single
 * card error from breaking the entire column or board
 */
export const TeamKanbanCardWithErrorBoundary = (props: TeamKanbanCardProps) => {
    return (
        <ErrorBoundary
            componentName="TeamKanbanCard"
            onError={(error, info) => {
                errorHandler.captureError(error, ErrorType.UNKNOWN, {
                    component: 'TeamKanbanCard',
                    action: 'render',
                    context: {
                        cardId: (props.item as TeamKanbanCardType | UnifiedKanbanCard).id,
                        componentStack: info.componentStack
                    }
                });
            }}
            // Simple fallback that still preserves the layout
            fallbackRender={() => (
                <div className="mb-3 hover:bg-accent/50 cursor-pointer">
                    <div className="bg-card p-4 rounded-lg border shadow-sm">
                        <div className="h-16 flex items-center justify-center">
                            <p className="text-sm text-muted-foreground">
                                Card rendering error
                            </p>
                        </div>
                    </div>
                </div>
            )}
            resetOnUpdate={true}
        >
            <TeamKanbanCard {...props} />
        </ErrorBoundary>
    );
}; 