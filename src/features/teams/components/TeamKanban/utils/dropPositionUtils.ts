import { GridDimensions } from './types';

/**
 * Helper function to find the drop position in a kanban column
 * This is kept for future implementation but not currently used
 */
export function findDropPosition(
    mouseY: number,
    columnRect: DOMRect,
    cardElements: Element[],
    gridDimensions: GridDimensions
): { index: number; y: number } {
    const relativeMouseY = mouseY - columnRect.top;

    // Handle empty column case
    if (cardElements.length === 0) {
        return {
            index: 0,
            y: gridDimensions.padding,
        };
    }

    // Find the insertion point
    for (let i = 0; i < cardElements.length; i++) {
        const cardRect = cardElements[i].getBoundingClientRect();
        const cardMiddleY = cardRect.top + cardRect.height / 2 - columnRect.top;

        if (relativeMouseY < cardMiddleY) {
            return {
                index: i,
                y: cardRect.top - columnRect.top - gridDimensions.gap / 2,
            };
        }
    }

    // If we get here, the mouse is below all cards
    const lastCardRect = cardElements[cardElements.length - 1].getBoundingClientRect();
    return {
        index: cardElements.length,
        y: lastCardRect.bottom - columnRect.top + gridDimensions.gap / 2,
    };
} 