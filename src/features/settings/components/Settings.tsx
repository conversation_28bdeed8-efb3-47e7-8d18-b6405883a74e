import { User, Users, Shield, Globe, LogOut } from 'lucide-react';
import { useAuthStore } from '@/features/auth';
import { useToast } from '@/components/ui/use-toast';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useState, useRef } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Shimmer } from '@/components/ui/shimmer';
import { supabase } from '@/lib/supabase';
import { errorHandler, ErrorType } from '@/utils/errorHandler';
import { 
    Tooltip, 
    TooltipContent, 
    TooltipProvider, 
    TooltipTrigger 
} from '@/components/ui/tooltip';

const Settings = () => {
    const { signOut, userProfile, fetchUserProfile } = useAuthStore();
    const { toast } = useToast();
    const navigate = useNavigate();
    const [isUploading, setIsUploading] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleLogout = async () => {
        try {
            await signOut();
            toast({
                title: 'Success',
                description: 'You have been logged out successfully.',
            });
            navigate('/login');
        } catch (error) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error instanceof Error ? error.message : 'Failed to log out',
            });
        }
    };

    const handleSectionClick = (title: string, disabled: boolean) => {
        if (disabled) return;

        switch (title) {
            case 'Team Settings':
                navigate('/team-management');
                break;
            // Add other cases for different sections if needed
            default:
                break;
        }
    };

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        // Check if user profile exists
        if (!userProfile || !userProfile.id) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'Unable to upload: User profile information is missing.',
            });
            return;
        }

        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            toast({
                variant: 'destructive',
                title: 'Invalid file type',
                description: 'Please upload a JPG, PNG, or GIF file.',
            });
            return;
        }

        // Validate file size (2MB limit)
        const MAX_SIZE = 2 * 1024 * 1024; // 2MB in bytes
        if (file.size > MAX_SIZE) {
            toast({
                variant: 'destructive',
                title: 'File too large',
                description: 'Please upload an image smaller than 2MB.',
            });
            return;
        }

        try {
            setIsUploading(true);

            // Upload to Supabase Storage
            const fileExt = file.name.split('.').pop();
            const fileName = `${Date.now()}.${fileExt}`;
            const filePath = `${userProfile.id}/${fileName}`;

            const { error: uploadError } = await supabase.storage
                .from('profile-pictures')
                .upload(filePath, file);

            if (uploadError) {
                throw uploadError;
            }

            // Get signed URL for authenticated access
            const { data: signedUrlData, error: signedUrlError } = await supabase.storage
                .from('profile-pictures')
                .createSignedUrl(filePath, 60 * 60 * 24 * 365 * 10); // 10 year expiry

            if (signedUrlError) {
                throw signedUrlError;
            }

            const avatarUrl = signedUrlData.signedUrl;

            // Update profile with signed URL
            const { error: updateError } = await supabase
                .from('profiles')
                .update({ avatar_url: avatarUrl })
                .eq('id', userProfile.id);

            if (updateError) {
                throw updateError;
            }

            // Refresh user profile to get the updated avatar URL
            await fetchUserProfile();
            
            toast({
                title: 'Success',
                description: 'Profile picture updated successfully.',
            });
        } catch (error) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error instanceof Error ? error.message : 'Failed to update profile picture',
            });
            errorHandler.captureError(error, ErrorType.API, {
                component: 'Settings',
                action: 'updateProfilePicture',
            });
        } finally {
            setIsUploading(false);
        }
    };

    const handleUploadClick = () => {
        fileInputRef.current?.click();
    };

    const settingsSections = [
        {
            icon: User,
            title: 'Profile Settings',
            description: 'Update your personal information and preferences',
            disabled: true,
        },
        {
            icon: Users,
            title: 'Team Settings',
            description: 'Manage team members and permissions',
            disabled: false,
        },
        {
            icon: Shield,
            title: 'Security',
            description: 'Manage your account security and authentication',
            disabled: true,
        },
        {
            icon: Globe,
            title: 'Workspace',
            description: 'Customize your workspace settings and permissions',
            disabled: true,
        },
    ];

    return (
        <div className="p-8">
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
                <p className="text-gray-600 mt-1">Manage your account and preferences</p>
            </div>

            {/* Settings Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {settingsSections.map((section, index) => (
                    <div
                        key={index}
                        className={cn(
                            "bg-white p-6 rounded-xl shadow-sm border border-gray-200 transition-colors",
                            !section.disabled && "hover:border-indigo-300 cursor-pointer",
                            section.disabled && "opacity-50 cursor-not-allowed"
                        )}
                        onClick={() => handleSectionClick(section.title, section.disabled)}
                    >
                        <div className="flex items-center mb-4">
                            <div className="h-10 w-10 bg-indigo-50 rounded-lg flex items-center justify-center mr-4">
                                <section.icon className="h-5 w-5 text-indigo-600" />
                            </div>
                            <div>
                                <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                                <p className="text-sm text-gray-600">{section.description}</p>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Account Actions Section */}
            <div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-200">
                <div className="p-6 border-b border-gray-200">
                    <h2 className="text-lg font-semibold">Account Actions</h2>
                    <p className="text-sm text-gray-600">Manage your account access</p>
                </div>
                <div className="p-6">
                    {userProfile && (
                        <div className="flex flex-col md:flex-row justify-between items-start gap-6">
                            {/* User Info Section with Avatar */}
                            <div className="w-full space-y-4 order-2 md:order-1">
                                <div className="flex items-center gap-4">
                                    <TooltipProvider>
                                        <Tooltip delayDuration={0}>
                                            <TooltipTrigger asChild>
                                                <div 
                                                    className="relative cursor-pointer" 
                                                    onClick={handleUploadClick}
                                                >
                                                    {isUploading ? (
                                                        <Shimmer className="w-16 h-16 rounded-full" />
                                                    ) : (
                                                        <Avatar className="w-16 h-16 border-2 border-indigo-100 shadow-sm hover:border-indigo-300 transition-all">
                                                            <AvatarImage 
                                                                src={userProfile.avatar_url || undefined} 
                                                                alt={`${userProfile.full_name || 'User'}'s avatar`}
                                                            />
                                                            <AvatarFallback className="text-sm bg-indigo-50 text-indigo-600">
                                                                {userProfile.full_name?.split(' ').map(n => n[0]).join('') || '?'}
                                                            </AvatarFallback>
                                                        </Avatar>
                                                    )}
                                                </div>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p className="text-xs">Click to change profile picture</p>
                                                <p className="text-xs opacity-75">JPG, PNG, GIF (max 2MB)</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                    <div className="space-y-1.5">
                                        <div className="flex flex-col">
                                            <span className="text-xs font-medium text-gray-500">Name</span>
                                            <span className="text-sm font-medium text-gray-900">{userProfile.full_name || 'Not set'}</span>
                                        </div>
                                        <div className="flex flex-col">
                                            <span className="text-xs font-medium text-gray-500">Email</span>
                                            <span className="text-sm font-medium text-gray-900">{userProfile.email}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div>
                                    <Button
                                        variant="destructive"
                                        onClick={handleLogout}
                                        className="flex items-center justify-center"
                                        size="sm"
                                    >
                                        <LogOut className="h-3.5 w-3.5 mr-2" />
                                        Logout
                                    </Button>
                                </div>
                            </div>

                            {/* Upload Info Section */}
                            <div className="order-1 md:order-2">
                                <input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/jpeg,image/png,image/gif"
                                    className="hidden"
                                    onChange={handleFileChange}
                                />
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Settings;