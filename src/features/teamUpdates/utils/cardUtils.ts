import { TeamActivityData, TeamTaskCard } from '@/services/api/teamUpdatesApi';

/**
 * Get unique cards from card_movements, subtask_updates, and comments
 */
export const getInProgressCards = (activityData?: TeamActivityData): TeamTaskCard[] => {
  if (!activityData) return [];

  const cardMap = new Map<string, TeamTaskCard>();

  // Add cards from card movements
  activityData.card_movements?.forEach(card => {
    if (!cardMap.has(card.card_id)) {
      cardMap.set(card.card_id, card);
    }
  });

  // Add cards from subtask updates using card_id
  activityData.subtask_updates?.forEach(subtask => {
    if (!cardMap.has(subtask.card_id)) {
      cardMap.set(subtask.card_id, {
        card_id: subtask.card_id,
        card_title: subtask.card_title
      });
    }
  });

  // Add cards from comments using card_id
  activityData.comments?.forEach(comment => {
    if (!cardMap.has(comment.card_id)) {
      cardMap.set(comment.card_id, {
        card_id: comment.card_id,
        card_title: comment.card_title
      });
    }
  });

  return Array.from(cardMap.values());
};

/**
 * Get cards from completed_tasks
 */
export const getCompletedCards = (activityData?: TeamActivityData): TeamTaskCard[] => {
  if (!activityData) return [];
  return activityData.completed_tasks || [];
};

/**
 * Get cards from new_cards
 */
export const getNewCards = (activityData?: TeamActivityData): TeamTaskCard[] => {
  if (!activityData) return [];
  return activityData.new_cards || [];
}; 