import { useEffect, useState, useMemo, useCallback } from 'react';
import { endOfWeek, format, isBefore, startOfWeek, subWeeks, eachDayOfInterval } from 'date-fns';
import { CalendarIcon, RefreshCw, CheckSquare, Activity, Plus, Check, ChevronsUpDown } from 'lucide-react';
import { DayPicker, Modifiers } from 'react-day-picker';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';

import { teamUpdatesApi, Team, TeamWeeklyUpdate } from '@/services/api/teamUpdatesApi';
import { TaskCardList } from './TaskCardList';
import { getCompletedCards, getInProgressCards, getNewCards } from '../utils/cardUtils';
import { supabase } from '@/lib/supabase';
import { TaskViewModal } from './TaskViewModal';
import { HealthMetricsWidgets } from './HealthMetricsWidgets';

// Import the TeamKanbanCard type to use for opening the modal
import { TeamKanbanCard, PriorityLevel } from '@/features/teams/types';

const TeamUpdates = () => {
  const [teams, setTeams] = useState<Team[]>([]);
  const [selectedTeam, setSelectedTeam] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date>(subWeeks(new Date(), 1));
  const [weeklyUpdate, setWeeklyUpdate] = useState<TeamWeeklyUpdate | null>(null);
  const [loading, setLoading] = useState(false);
  const [fetchTrigger, setFetchTrigger] = useState(0); // Used to trigger fetches
  const [activeTab, setActiveTab] = useState<string>("updates");

  // State for the team dropdown
  const [teamDropdownOpen, setTeamDropdownOpen] = useState(false);

  // State for the task view modal
  const [selectedCard, setSelectedCard] = useState<TeamKanbanCard | null>(null);
  const [showCardModal, setShowCardModal] = useState<boolean>(false);
  const [cardLoading, setCardLoading] = useState(false);

  const { toast } = useToast();

  // Calculate week range with useMemo to prevent recalculation on every render
  const { weekStart, weekEnd, formattedWeekRange } = useMemo(() => {
    const start = startOfWeek(selectedDate, { weekStartsOn: 1 }); // Monday
    const end = endOfWeek(selectedDate, { weekStartsOn: 1 }); // Sunday
    return {
      weekStart: start,
      weekEnd: end,
      formattedWeekRange: `${format(start, 'MMM d')} - ${format(end, 'MMM d, yyyy')}`
    };
  }, [selectedDate]);

  // Calculate selected week days for highlighting
  const selectedWeekDays = useMemo(() => {
    if (!selectedDate) return [];
    return eachDayOfInterval({
      start: weekStart,
      end: weekEnd
    });
  }, [selectedDate, weekStart, weekEnd]);

  // Custom day rendering with week highlighting
  const modifiers = useMemo(() => {
    return {
      selectedWeek: selectedWeekDays,
    };
  }, [selectedWeekDays]);

  const modifiersStyles = {
    selectedWeek: {
      backgroundColor: 'var(--accent-muted)',
      borderRadius: '0'
    },
    disabled: {
      color: 'var(--muted-foreground)',
      opacity: 0.5,
      cursor: 'not-allowed'
    }
  };

  // Process the card data for display
  const { completedCards, inProgressCards, newCards } = useMemo(() => {
    const result = {
      completedCards: getCompletedCards(weeklyUpdate?.team_updates),
      inProgressCards: getInProgressCards(weeklyUpdate?.team_updates),
      newCards: getNewCards(weeklyUpdate?.team_updates)
    };

    console.log('Processed card data:', {
      completedCards: result.completedCards.length,
      inProgressCards: result.inProgressCards.length,
      newCards: result.newCards.length,
      raw: weeklyUpdate?.team_updates
    });

    // If there are no health metrics in the data, add some sample data for testing
    if (weeklyUpdate?.team_updates && !weeklyUpdate.team_updates.health_metrics) {
      weeklyUpdate.team_updates.health_metrics = [
        {
          id: '1',
          name: 'eNPS',
          value: 3.8,
          status: 'yellow',
          unit: '',
          updated_at: new Date().toISOString()
        },
        {
          id: '2',
          name: 'Deployment frequency',
          value: 1,
          status: 'red',
          unit: '/week',
          updated_at: new Date().toISOString()
        },
        {
          id: '3',
          name: 'Bug-to-fix ratio',
          value: 8,
          status: 'green',
          unit: '%',
          updated_at: new Date().toISOString()
        },
        {
          id: '4',
          name: 'Story throughput',
          value: 22,
          status: 'green',
          unit: '',
          updated_at: new Date().toISOString()
        }
      ];
    }

    return result;
  }, [weeklyUpdate?.team_updates]);

  // Handle day mouse enter for hover effect
  const handleDayMouseEnter = (_date: Date, modifiers: Modifiers) => {
    if (modifiers.disabled) return;

    // For simplicity, we'll use CSS hover effects only
    // Additional hover logic could be added here in the future
  };

  // Check if a date is in the future
  const isDateDisabled = (date: Date) => {
    const today = new Date();
    // Allow selection up to today
    return !isBefore(date, today) && date.getDate() !== today.getDate();
  };

  // Get last viewed team from local storage and fetch teams with proper sequencing
  useEffect(() => {
    const initializeComponent = async () => {
      // First, try to get the last viewed team from localStorage
      const lastViewedTeamId = localStorage.getItem('last_viewed_team_id');

      // Next, fetch teams regardless of whether we have a stored team
      try {
        const teamsData = await teamUpdatesApi.getTeams();
        setTeams(teamsData);

        // Now set the selected team - either from localStorage or the first team
        if (lastViewedTeamId) {
          // Check if the team from localStorage exists in the fetched teams
          const teamExists = teamsData.some(team => team.id === lastViewedTeamId);

          if (teamExists) {
            setSelectedTeam(lastViewedTeamId);
          } else if (teamsData.length > 0) {
            // If stored team doesn't exist anymore, use the first available team
            setSelectedTeam(teamsData[0].id);
            localStorage.setItem('last_viewed_team_id', teamsData[0].id);
          }
        } else if (teamsData.length > 0) {
          // If no team in localStorage, select the first one
          setSelectedTeam(teamsData[0].id);
          localStorage.setItem('last_viewed_team_id', teamsData[0].id);
        }
      } catch (error) {
        console.error('Error fetching teams:', error);
        toast({
          title: "Error",
          description: "Failed to fetch teams. Please try again.",
          variant: "destructive"
        });
      }
    };

    initializeComponent();
  }, [toast]); // Run only on mount, but include toast dependency

  // Create memoized fetchWeeklyUpdate function to prevent recreation on every render
  const fetchWeeklyUpdate = useCallback(async () => {
    if (!selectedTeam) return;

    setLoading(true);
    try {
      const startDateStr = format(weekStart, 'yyyy-MM-dd');
      const endDateStr = format(weekEnd, 'yyyy-MM-dd');

      const update = await teamUpdatesApi.getTeamWeeklyUpdate(
        selectedTeam,
        startDateStr,
        endDateStr
      );

      console.log('Weekly update data:', update);
      console.log('team_updates field:', update?.team_updates);

      setWeeklyUpdate(update);

      // Store selected team in local storage
      localStorage.setItem('last_viewed_team_id', selectedTeam);
    } catch (error) {
      console.error('Error fetching weekly update:', error);
      toast({
        title: "Error",
        description: "Failed to fetch weekly update. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [selectedTeam, weekStart, weekEnd, toast]);

  // Fetch weekly update when team, date, or fetchTrigger changes
  useEffect(() => {
    let isComponentMounted = true;

    if (selectedTeam && isComponentMounted) {
      fetchWeeklyUpdate();
    }

    return () => {
      isComponentMounted = false;
    };
  }, [selectedTeam, fetchWeeklyUpdate, fetchTrigger]);

  const handleTeamChange = (teamId: string) => {
    setSelectedTeam(teamId);
    // Save selected team to localStorage immediately when changed
    localStorage.setItem('last_viewed_team_id', teamId);
    // Close the dropdown
    setTeamDropdownOpen(false);
  };

  // Handle dropdown open state change
  const handleTeamDropdownOpenChange = (open: boolean) => {
    setTeamDropdownOpen(open);
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      // Trigger a fetch when date changes
      setFetchTrigger(prev => prev + 1);
    }
  };

  const handleRefresh = () => {
    setFetchTrigger(prev => prev + 1);
  };

  const handleCardClick = async (cardId: string) => {
    // Find the basic card info first
    const clickedCard = [...completedCards, ...inProgressCards, ...newCards].find(
      card => card.card_id === cardId
    );

    if (!clickedCard) {
      toast({
        title: "Card Not Found",
        description: "Could not find the card details. Please try again.",
        variant: "destructive"
      });
      return;
    }

    // Set loading state
    setCardLoading(true);

    try {
      // Direct fetch from kanban_cards table - no store initialization needed
      const { data: cardData, error } = await supabase
        .from('kanban_cards')
        .select(`
          *,
          assignee:profiles!kanban_cards_assignee_id_fkey(*),
          subtasks:kanban_subtasks(
            id,
            title,
            is_completed,
            order_index,
            created_at,
            updated_at,
            due_date,
            assignee_id,
            assignee:profiles!kanban_subtasks_assignee_id_fkey(*)
          ),
          comments:kanban_comments(
            id,
            content,
            created_at,
            updated_at,
            edited_at,
            deleted_at,
            user_id,
            user:profiles(id, full_name, avatar_url)
          )
        `)
        .eq('id', cardId)
        .single();

      if (error) {
        console.error("Error fetching card from database:", error);
        throw error;
      }

      if (cardData) {
        // Transform the data to match TeamKanbanCard structure
        const transformedCard: TeamKanbanCard = {
          id: cardData.id,
          column_id: cardData.column_id,
          team_id: cardData.team_id,
          title: cardData.title,
          description: cardData.description,
          order_index: cardData.order_index,
          position_updated_at: cardData.position_updated_at,
          due_date: cardData.due_date,
          priority: cardData.priority || PriorityLevel.P3,
          created_at: cardData.created_at,
          updated_at: cardData.updated_at,
          deleted_at: cardData.deleted_at,
          archived_at: cardData.archived_at,
          assignee: cardData.assignee_id ? {
            id: cardData.assignee_id,
            name: cardData.assignee ? cardData.assignee.full_name || 'Unknown User' : 'Unknown User',
            avatar: cardData.assignee ? cardData.assignee.avatar_url || '' : ''
          } : null,
          subtasks: (cardData.subtasks || []).map((subtask: {
            id: string;
            title: string;
            is_completed: boolean;
            order_index: number;
            created_at: string;
            updated_at: string;
            due_date: string | null;
            assignee_id: string | null;
            assignee?: {
              id: string;
              full_name: string | null;
              avatar_url: string | null;
            } | null;
          }) => ({
            ...subtask,
            assignee: subtask.assignee_id ? {
              id: subtask.assignee_id,
              name: subtask.assignee ? subtask.assignee.full_name || 'Unknown User' : 'Unknown User',
              avatar: subtask.assignee ? subtask.assignee.avatar_url || '' : ''
            } : null
          })),
          comments: (cardData.comments || [])
            .filter((c: { deleted_at: string | null }) => !c.deleted_at)
            .map((comment: {
              id: string;
              content: string;
              created_at: string;
              updated_at: string;
              edited_at: string | null;
              deleted_at: string | null;
              user_id: string;
              user?: {
                id: string;
                full_name: string | null;
                avatar_url: string | null;
              }
            }) => ({
              id: comment.id,
              content: comment.content,
              created_at: comment.created_at,
              edited_at: comment.edited_at,
              deleted_at: comment.deleted_at,
              user: comment.user ? {
                id: comment.user.id,
                name: comment.user.full_name || 'Unknown User',
                avatar: comment.user.avatar_url || ''
              } : {
                id: comment.user_id,
                name: 'Unknown User',
                avatar: ''
              }
            }))
        };

        console.log("Successfully fetched card details:", transformedCard);

        // Update state with the card data - no store updates needed
        setSelectedCard(transformedCard);
        setShowCardModal(true);
        return;
      }

      // If we reach here, fallback to using the minimal card object
      console.log("Card not found in database, using fallback");
      const fallbackCard: TeamKanbanCard = {
        id: clickedCard.card_id,
        column_id: '',
        team_id: selectedTeam || '',
        title: clickedCard.card_title,
        description: null,
        order_index: 0,
        position_updated_at: new Date().toISOString(),
        due_date: null,
        priority: PriorityLevel.P3,
        created_at: clickedCard.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null,
        archived_at: null,
        assignee: null,
        subtasks: [],
        comments: []
      };

      setSelectedCard(fallbackCard);
      setShowCardModal(true);

      toast({
        title: "Limited Card Details",
        description: "Only basic card information is available. Some details may be missing.",
        variant: "default"
      });
    } catch (error) {
      console.error("Error fetching card details:", error);
      toast({
        title: "Error",
        description: "Failed to load card details. Please try again.",
        variant: "destructive"
      });
    } finally {
      setCardLoading(false);
    }
  };

  const closeCardModal = () => {
    setShowCardModal(false);
    setSelectedCard(null);
  };

  // Custom CSS styles for scrolling on mobile
  const customScrollStyles = `
    @media (max-width: 640px) {
      .task-list-container {
        max-height: 50vh !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
      }
    }

    /* Fix for task card overflow issues */
    .task-card-grid {
      min-width: 0;
      width: 100%;
    }

    .task-card-column {
      min-width: 0;
      width: 100%;
      overflow: hidden;
    }

    .task-card-content {
      min-width: 0;
      width: 100%;
      max-width: 100%;
      overflow: hidden;
    }
  `;

  return (
    <div className="container py-6 space-y-6">
      {/* Add style tag for week highlighting and custom scrolling */}
      <style>{weekSelectStyles}</style>
      <style>{customScrollStyles}</style>

      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <h1 className="text-2xl font-bold">Team Updates</h1>

        <div className="flex flex-col sm:flex-row gap-2">
          {/* Team Selection - Searchable Dropdown */}
          <Popover open={teamDropdownOpen} onOpenChange={handleTeamDropdownOpenChange}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={teamDropdownOpen}
                className="w-[200px] justify-between"
              >
                {selectedTeam ? teams.find(team => team.id === selectedTeam)?.name : "Select Team"}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0">
              <Command filter={(value, search) => {
                if (value.toLowerCase().includes(search.toLowerCase())) return 1
                return 0
              }}>
                <CommandInput placeholder="Search teams..." />
                <CommandEmpty>No teams found.</CommandEmpty>
                <CommandGroup>
                  {teams.map(team => (
                    <CommandItem
                      key={team.id}
                      value={team.name}
                      onSelect={() => handleTeamChange(team.id)}
                    >
                      <span>{team.name}</span>
                      {selectedTeam === team.id && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>

          {/* Week Selection */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-[240px] justify-start">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formattedWeekRange}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <DayPicker
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                disabled={isDateDisabled}
                // initialFocus is deprecated, but we'll keep it for now
                modifiers={modifiers}
                modifiersStyles={modifiersStyles}
                showOutsideDays={true}
                showWeekNumber
                weekStartsOn={1}
                className="week-select-calendar"
                onDayMouseEnter={handleDayMouseEnter}
                footer={
                  <p className="text-xs text-center text-muted-foreground mt-2">
                    Click any day to select its week
                  </p>
                }
              />
            </PopoverContent>
          </Popover>

          {/* Refresh Button */}
          <Button
            variant="outline"
            size="icon"
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Tabs for Summary and Updates */}
      <Tabs defaultValue="updates" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="updates">Task Updates</TabsTrigger>
          <TabsTrigger value="summary">Summary</TabsTrigger>
        </TabsList>

        {/* Updates Tab */}
        <TabsContent value="updates" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>
                Task Updates: {formattedWeekRange}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="py-8 text-center">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">Loading update...</p>
                </div>
              ) : weeklyUpdate?.team_updates ? (
                <>
                  {/* Health Metrics Widgets */}
                  {weeklyUpdate.team_updates.health_metrics && weeklyUpdate.team_updates.health_metrics.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-lg font-medium mb-4">Health Metrics</h3>
                      <HealthMetricsWidgets metrics={weeklyUpdate.team_updates.health_metrics} />
                    </div>
                  )}

                  {/* Kanban Board */}
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 sm:gap-4 h-[65vh] sm:h-[70vh] overflow-hidden task-card-grid">
                    <div className="h-full overflow-hidden task-card-column">
                      <TaskCardList
                        title="Completed Tasks"
                        icon={<CheckSquare className="h-4 w-4 text-green-500" />}
                        cards={completedCards}
                        onCardClick={handleCardClick}
                        maxHeight="100%"
                        className="h-full flex flex-col task-card-content"
                      />
                    </div>

                    <div className="h-full overflow-hidden task-card-column">
                      <TaskCardList
                        title="In Progress Tasks"
                        icon={<Activity className="h-4 w-4 text-blue-500" />}
                        cards={inProgressCards}
                        onCardClick={handleCardClick}
                        maxHeight="100%"
                        className="h-full flex flex-col task-card-content"
                      />
                    </div>

                    <div className="h-full overflow-hidden task-card-column">
                      <TaskCardList
                        title="New Tasks"
                        icon={<Plus className="h-4 w-4 text-purple-500" />}
                        cards={newCards}
                        onCardClick={handleCardClick}
                        maxHeight="100%"
                        className="h-full flex flex-col task-card-content"
                      />
                    </div>
                  </div>
                </>
              ) : (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground mb-2">No task updates found for the selected week.</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    className="mt-2"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Data
                  </Button>
                  <div className="mt-4 text-xs text-muted-foreground">
                    {weeklyUpdate ? "A summary is available but detailed task data is missing." : "No team update data found."}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Summary Tab */}
        <TabsContent value="summary" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>
                Weekly Update: {formattedWeekRange}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="py-8 text-center">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">Loading update...</p>
                </div>
              ) : weeklyUpdate ? (
                <div className="prose dark:prose-invert max-w-none">
                  <div
                    dangerouslySetInnerHTML={{ __html: weeklyUpdate.team_update_summary || '' }}
                  />
                </div>
              ) : (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">No team updates found for the selected week.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Card detail modal with loading indicator */}
      {cardLoading && (
        <div className="fixed inset-0 bg-background/80 flex items-center justify-center z-50 backdrop-blur-sm">
          <Card className="w-[300px] shadow-lg">
            <CardContent className="flex flex-col items-center justify-center pt-6 pb-6">
              <RefreshCw className="h-8 w-8 animate-spin mb-4 text-primary" />
              <CardTitle className="mb-2">Loading Card</CardTitle>
              <p className="text-sm text-muted-foreground">Fetching card details...</p>
            </CardContent>
          </Card>
        </div>
      )}
      {selectedCard && (
        <TaskViewModal
          card={selectedCard}
          onClose={closeCardModal}
          open={showCardModal && !cardLoading}
        />
      )}
    </div>
  );
};

// Add styles for week hover effects and disabled dates
const weekSelectStyles = `
  .week-select-calendar .rdp-day:hover {
    background-color: var(--accent);
    color: var(--accent-foreground);
  }

  /* Highlight entire weeks on hover using CSS grid rows */
  .week-select-calendar .rdp-table .rdp-row:hover {
    background-color: var(--accent-hover);
  }

  /* Style disabled dates to be visually distinct */
  .week-select-calendar .rdp-day_disabled {
    opacity: 0.5;
    color: var(--muted-foreground);
    cursor: not-allowed;
  }

  .week-select-calendar .rdp-day_disabled:hover {
    background-color: transparent;
    color: var(--muted-foreground);
  }

  /* Style today's date to be more visible */
  .week-select-calendar .rdp-day_today {
    font-weight: bold;
    border: 1px solid var(--primary);
    color: var(--primary);
  }

  /* Make sure disabled weeks don't highlight on hover */
  .week-select-calendar .rdp-row:has(.rdp-day_disabled):hover {
    background-color: transparent;
  }

  /* Give selected week a distinct style */
  .week-select-calendar [aria-selected=true] {
    font-weight: bold;
  }

  /* Style week numbers */
  .week-select-calendar .rdp-weeknumber {
    color: var(--muted-foreground);
    font-size: 0.8rem;
    font-weight: 500;
    padding-right: 0.75rem;
    text-align: right;
  }

  /* Highlight current week number when its week is selected */
  .week-select-calendar .rdp-row:has(.selectedWeek) .rdp-weeknumber {
    color: var(--primary);
    font-weight: 700;
  }
`;

export default TeamUpdates;