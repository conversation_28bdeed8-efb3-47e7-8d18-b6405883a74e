import { format } from 'date-fns';
import { CheckCircle2, Clock, Circle, ChevronLeft, ChevronRight } from 'lucide-react';
import { useState } from 'react';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';

import { TeamKanbanCard, PriorityLevel } from '@/features/teams/types';

// Helper to get initials from a name
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

// Helper to format date
const formatDate = (dateString: string | null) => {
  if (!dateString) return 'No date';
  return format(new Date(dateString), 'MMM d, yyyy');
};

// Priority badge components with appropriate colors
const PriorityBadge = ({ priority }: { priority: PriorityLevel }) => {
  const priorityConfig = {
    [PriorityLevel.P1]: { label: 'P1 - Urgent', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' },
    [PriorityLevel.P2]: { label: 'P2 - High', color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300' },
    [PriorityLevel.P3]: { label: 'P3 - Medium', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' }
  };

  const config = priorityConfig[priority] || priorityConfig[PriorityLevel.P3];
  
  return (
    <Badge variant="outline" className={`${config.color} font-medium`}>
      {config.label}
    </Badge>
  );
};

interface TaskViewModalProps {
  card: TeamKanbanCard | null;
  onClose: () => void;
  open: boolean;
}

export const TaskViewModal = ({ card, onClose, open }: TaskViewModalProps) => {
  const [commentsPage, setCommentsPage] = useState(1);
  const commentsPerPage = 5;
  
  // Sort subtasks by completion status (incomplete first)
  const sortedSubtasks = [...(card?.subtasks || [])].sort((a, b) => {
    if (a.is_completed === b.is_completed) return 0;
    return a.is_completed ? 1 : -1;
  });

  // Comments sorted by date (newest first)
  const sortedComments = [...(card?.comments || [])].sort((a, b) => 
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  // Calculate pagination for comments
  const totalComments = sortedComments.length;
  const totalPages = Math.ceil(totalComments / commentsPerPage);
  const paginatedComments = sortedComments.slice(
    (commentsPage - 1) * commentsPerPage, 
    commentsPage * commentsPerPage
  );
  
  // Pagination navigation
  const goToNextPage = () => {
    if (commentsPage < totalPages) {
      setCommentsPage(prev => prev + 1);
    }
  };
  
  const goToPrevPage = () => {
    if (commentsPage > 1) {
      setCommentsPage(prev => prev - 1);
    }
  };

  if (!card) return null;

  return (
    <Dialog open={open} onOpenChange={(isOpen) => !isOpen && onClose()}>
      <DialogContent className="sm:max-w-[600px] max-h-[85vh] flex flex-col overflow-hidden">
        <DialogHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            {card.archived_at ? (
              <CheckCircle2 className="h-5 w-5 text-green-500" />
            ) : (
              <Circle className="h-5 w-5 text-blue-500" />
            )}
            <DialogTitle className="text-lg">
              {card.title}
            </DialogTitle>
          </div>
          <DialogClose className="h-4 w-4" />
        </DialogHeader>

        <ScrollArea className="flex-1 pr-2">
          <div className="grid grid-cols-2 gap-4 py-4">
            <div>
              <p className="text-sm font-medium mb-1">Status</p>
              <Badge variant={card.archived_at ? "secondary" : "outline"} className={card.archived_at ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300" : ""}>
                {card.archived_at ? "Completed" : "In Progress"}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium mb-1">Priority</p>
              <PriorityBadge priority={card.priority} />
            </div>
            <div>
              <p className="text-sm font-medium mb-1">Due Date</p>
              <div className="flex items-center text-sm">
                <Clock className="mr-1 h-4 w-4 opacity-70" />
                {formatDate(card.due_date)}
              </div>
            </div>
            <div>
              <p className="text-sm font-medium mb-1">Assignee</p>
              {card.assignee ? (
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={card.assignee.avatar} />
                    <AvatarFallback>{getInitials(card.assignee.name)}</AvatarFallback>
                  </Avatar>
                  <span className="text-sm">{card.assignee.name}</span>
                </div>
              ) : (
                <span className="text-sm text-muted-foreground">Unassigned</span>
              )}
            </div>
          </div>

          {card.description && (
            <>
              <Separator />
              <div className="py-2">
                <h4 className="font-medium mb-2">Description</h4>
                <p className="text-sm whitespace-pre-line">{card.description}</p>
              </div>
            </>
          )}

          {sortedSubtasks.length > 0 && (
            <>
              <Separator />
              <div className="py-2">
                <h4 className="font-medium mb-2">Subtasks ({sortedSubtasks.filter(s => s.is_completed).length}/{sortedSubtasks.length})</h4>
                <div className="border rounded-md">
                  <ScrollArea className="h-[200px] w-full">
                    <div className="p-2 space-y-2">
                      {sortedSubtasks.map(subtask => (
                        <div key={subtask.id} className="flex items-start gap-2 p-2 rounded bg-accent/50">
                          {subtask.is_completed ? (
                            <CheckCircle2 className="h-4 w-4 mt-0.5 text-green-500 flex-shrink-0" />
                          ) : (
                            <Circle className="h-4 w-4 mt-0.5 text-muted-foreground flex-shrink-0" />
                          )}
                          <div className="flex-1 min-w-0">
                            <p className={`text-sm font-medium ${subtask.is_completed ? 'line-through text-muted-foreground' : ''}`}>
                              {subtask.title}
                            </p>
                            <div className="flex flex-wrap gap-x-4 gap-y-1 mt-1 text-xs text-muted-foreground">
                              {subtask.due_date && (
                                <div className="flex items-center">
                                  <Clock className="mr-1 h-3 w-3" />
                                  {formatDate(subtask.due_date)}
                                </div>
                              )}
                              {subtask.assignee && (
                                <div className="flex items-center gap-1">
                                  <Avatar className="h-4 w-4">
                                    <AvatarImage src={subtask.assignee.avatar} />
                                    <AvatarFallback className="text-[8px]">{getInitials(subtask.assignee.name)}</AvatarFallback>
                                  </Avatar>
                                  <span>{subtask.assignee.name}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            </>
          )}

          {sortedComments.length > 0 && (
            <>
              <Separator />
              <div className="py-2">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">Comments ({sortedComments.length})</h4>
                  {totalPages > 1 && (
                    <div className="flex items-center gap-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={goToPrevPage} 
                        disabled={commentsPage === 1}
                        className="h-8 w-8 p-0"
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <span className="text-xs text-muted-foreground">
                        {commentsPage}/{totalPages}
                      </span>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={goToNextPage} 
                        disabled={commentsPage === totalPages}
                        className="h-8 w-8 p-0"
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
                
                <div className="border rounded-md">
                  <ScrollArea className="h-[200px]">
                    <div className="p-3 space-y-3">
                      {paginatedComments.length > 0 ? (
                        paginatedComments.map(comment => (
                          <div key={comment.id} className="flex gap-2">
                            <Avatar className="h-6 w-6 flex-shrink-0">
                              <AvatarImage src={comment.user.avatar} />
                              <AvatarFallback>{getInitials(comment.user.name)}</AvatarFallback>
                            </Avatar>
                            <div className="flex-1 space-y-1">
                              <div className="flex items-center justify-between">
                                <p className="text-xs font-medium">{comment.user.name}</p>
                                <p className="text-xs text-muted-foreground">
                                  {format(new Date(comment.created_at), 'MMM d, h:mm a')}
                                </p>
                              </div>
                              <p className="text-sm whitespace-pre-line">{comment.content}</p>
                            </div>
                          </div>
                        ))
                      ) : (
                        <p className="text-sm text-muted-foreground text-center py-2">No comments to display</p>
                      )}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            </>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}; 