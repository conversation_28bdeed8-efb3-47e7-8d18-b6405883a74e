import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';
import { TeamHealthMetric } from '@/services/api/teamUpdatesApi';

// Extended interface to include reported_at field that might come from the database
interface ExtendedTeamHealthMetric extends TeamHealthMetric {
  reported_at?: string;
}

interface HealthMetricsWidgetsProps {
  metrics: ExtendedTeamHealthMetric[];
}

export const HealthMetricsWidgets: React.FC<HealthMetricsWidgetsProps> = ({ metrics }) => {
  if (!metrics || metrics.length === 0) {
    return null;
  }

  // Helper function to get the color class for the status badge
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'green': return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'yellow': return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'red': return 'bg-red-100 text-red-800 hover:bg-red-200';
      default: return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // Helper function to get the text for the status badge
  const getStatusText = (status: string) => {
    switch (status) {
      case 'green': return 'Healthy';
      case 'yellow': return 'Warning';
      case 'red': return 'Critical';
      default: return 'No data';
    }
  };

  // Helper function to format the last updated time
  const formatLastUpdated = (metric: ExtendedTeamHealthMetric) => {
    // Check for reported_at first (from DB), then fall back to updated_at (from sample data)
    const dateString = metric.reported_at || metric.updated_at;

    if (!dateString) return 'No data';

    try {
      return `Updated ${formatDistanceToNow(new Date(dateString), { addSuffix: true })}`;
    } catch (_) {
      return 'Updated recently';
    }
  };

  return (
    <div className="mb-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric) => {
          return (
            <Card key={metric.id} className="p-4">
              <div className="font-medium text-gray-700 mb-1">{metric.name}</div>
              <div className="flex justify-between items-center mb-2">
                <div className="text-4xl font-bold">
                  {metric.value}
                  {metric.unit && <span className="text-sm ml-1">{metric.unit}</span>}
                </div>
                <div className="flex-shrink-0 min-w-[80px] flex justify-end">
                  <Badge
                    className={getStatusColor(metric.status)}
                    aria-label={`Status: ${getStatusText(metric.status)}`}
                  >
                    {getStatusText(metric.status)}
                  </Badge>
                </div>
              </div>
              <div className="text-xs text-gray-500 mt-2">
                {formatLastUpdated(metric)}
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );
};
