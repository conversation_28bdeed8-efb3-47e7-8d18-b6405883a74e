import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { TeamTaskCard } from '@/services/api/teamUpdatesApi';
import { Calendar, User } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface TaskCardProps {
  card: TeamTaskCard;
  onClick: (cardId: string) => void;
}

export const TaskCard: React.FC<TaskCardProps> = ({ card, onClick }) => {
  // Determine if the card has a date that can be displayed
  const displayDate = card.completed_at || card.moved_at || card.created_at;
  
  // Extract a name if available
  const displayName = card.completed_by || card.moved_by || card.created_by;
  
  // Determine card type based on available properties
  const isCompleted = !!card.completed_at || !!card.archived_at;
  const isNew = !!card.created_at && !card.moved_at && !card.completed_at;
  
  const borderColor = isCompleted 
    ? 'border-l-green-500' 
    : isNew
      ? 'border-l-purple-500'
      : 'border-l-blue-500';
  
  return (
    <Card 
      className={cn(
        "mb-2 hover:bg-accent/10 cursor-pointer transition-colors border-l-4 w-full max-w-full overflow-hidden",
        borderColor
      )}
      onClick={() => onClick(card.card_id)}
    >
      <CardContent className="p-3 overflow-hidden">
        <div className="text-sm font-medium mb-1 break-words line-clamp-2 overflow-hidden text-ellipsis">{card.card_title}</div>
        
        <div className="flex flex-wrap justify-between text-xs text-muted-foreground mt-2 gap-y-1">
          {displayDate && (
            <div className="flex items-center gap-1 min-w-[100px] max-w-full overflow-hidden">
              <Calendar className="h-3 w-3 flex-shrink-0" />
              <span className="truncate">{format(new Date(displayDate), 'MMM d, yyyy')}</span>
            </div>
          )}
          
          {displayName && (
            <div className="flex items-center gap-1 max-w-[150px] overflow-hidden">
              <User className="h-3 w-3 flex-shrink-0" />
              <span className="truncate">{displayName}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}; 