import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { TeamTaskCard } from '@/services/api/teamUpdatesApi';
import { TaskCard } from './TaskCard';
import { cn } from '@/lib/utils';

interface TaskCardListProps {
  title: string;
  icon?: React.ReactNode;
  cards: TeamTaskCard[];
  onCardClick: (cardId: string) => void;
  maxHeight?: string;
  className?: string;
}

export const TaskCardList: React.FC<TaskCardListProps> = ({ 
  title,
  icon,
  cards,
  onCardClick,
  maxHeight = "300px",
  className
}) => {
  // Ensure cards is an array and handle undefined/null
  const cardItems = Array.isArray(cards) ? cards : [];

  const headerClass = title.toLowerCase().includes('completed') 
    ? 'border-green-200 bg-green-50 dark:bg-green-950/30'
    : title.toLowerCase().includes('progress')
      ? 'border-blue-200 bg-blue-50 dark:bg-blue-950/30'
      : title.toLowerCase().includes('new')
        ? 'border-purple-200 bg-purple-50 dark:bg-purple-950/30'
        : '';

  if (cardItems.length === 0) {
    return (
      <Card className={cn("h-full shadow-sm", className)}>
        <CardHeader className={cn("py-3 rounded-t-lg", headerClass)}>
          <CardTitle className="text-md flex items-center gap-2">
            {icon}
            {title} (0)
          </CardTitle>
        </CardHeader>
        <CardContent className="px-4 pb-4 pt-4 text-center text-muted-foreground">
          <p className="text-sm">No items to display</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("h-full flex flex-col shadow-sm overflow-hidden", className)}>
      <CardHeader className={cn("py-3 rounded-t-lg flex-shrink-0", headerClass)}>
        <CardTitle className="text-md flex items-center gap-2 truncate">
          {icon}
          <span className="truncate">{title} ({cardItems.length})</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="px-4 pb-4 pt-2 flex-grow overflow-hidden">
        <ScrollArea 
          className="h-full task-list-container w-full"
          style={{ maxHeight }}
          type="always"
        >
          <div className="space-y-2 pr-2 w-full">
            {cardItems.map((card) => (
              <TaskCard 
                key={card.card_id} 
                card={card} 
                onClick={onCardClick} 
              />
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}; 