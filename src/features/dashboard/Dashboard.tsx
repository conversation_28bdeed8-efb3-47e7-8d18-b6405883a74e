import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LayoutGrid, List as ListIcon, Users, CheckSquare, BarChart2 } from 'lucide-react';
import { useTeamsStore } from '@/features/teams';
import { TeamCard } from '@/features/teams';
import { Team } from '@/features/teams';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

const Dashboard = () => {
    const [selectedView, setSelectedView] = useState<'grid' | 'list'>('grid');
    const { teams } = useTeamsStore();
    const navigate = useNavigate();
    const activeTeams = teams.filter((t: Team) => t.status === 'Active');

    const stats = [
        {
            title: "Active Teams",
            value: activeTeams.length.toString(),
            icon: CheckSquare,
        },
        {
            title: "Team Members",
            value: "12",
            icon: Users,
        },
        {
            title: "Total Progress",
            value: `${Math.round(activeTeams.reduce((acc: number, t: Team) => acc + t.progress, 0) / activeTeams.length)}%`,
            icon: BarChart2,
        },
    ];

    const handleTeamClick = (team: Team) => {
        navigate(`/team/${team.id}`);
    };

    return (
        <div className="space-y-6 p-8">
            <div>
                <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
                <p className="text-muted-foreground">
                    Welcome back to your team dashboard
                </p>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {stats.map((stat) => (
                    <Card key={stat.title}>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                {stat.title}
                            </CardTitle>
                            <stat.icon className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stat.value}</div>
                        </CardContent>
                    </Card>
                ))}
            </div>

            <div className="flex justify-between items-center">
                <h3 className="text-xl font-semibold">Active Teams</h3>
                <div className="flex gap-2">
                    <button
                        onClick={() => setSelectedView('grid')}
                        className={`inline-flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground ${selectedView === 'grid'
                            ? 'bg-secondary text-secondary-foreground'
                            : 'text-muted-foreground'
                            }`}
                    >
                        <LayoutGrid className="h-4 w-4 mr-2" />
                        Grid
                    </button>
                    <button
                        onClick={() => setSelectedView('list')}
                        className={`inline-flex items-center justify-center rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground ${selectedView === 'list'
                            ? 'bg-secondary text-secondary-foreground'
                            : 'text-muted-foreground'
                            }`}
                    >
                        <ListIcon className="h-4 w-4 mr-2" />
                        List
                    </button>
                </div>
            </div>

            {selectedView === 'grid' ? (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {activeTeams.map((team) => (
                        <TeamCard
                            key={team.id}
                            team={team}
                            onClick={handleTeamClick}
                        />
                    ))}
                </div>
            ) : (
                <Card>
                    <div className="rounded-md border">
                        <table className="w-full">
                            <thead>
                                <tr className="border-b bg-muted/50">
                                    <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Team</th>
                                    <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Lead</th>
                                    <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Members</th>
                                    <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Progress</th>
                                    <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Due Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                {activeTeams.map((team) => (
                                    <tr
                                        key={team.id}
                                        className="border-b transition-colors hover:bg-muted/50 cursor-pointer"
                                        onClick={() => handleTeamClick(team)}
                                    >
                                        <td className="p-4">
                                            <div className="font-medium">{team.name}</div>
                                            <div className="text-sm text-muted-foreground">{team.description}</div>
                                        </td>
                                        <td className="p-4">
                                            <div className="flex items-center gap-2">
                                                <Avatar className="h-8 w-8">
                                                    <AvatarImage src={team.lead.avatar} alt={team.lead.name} />
                                                    <AvatarFallback>
                                                        {team.lead.name.charAt(0).toUpperCase()}
                                                    </AvatarFallback>
                                                </Avatar>
                                                <span className="text-sm">{team.lead.name}</span>
                                            </div>
                                        </td>
                                        <td className="p-4">
                                            <div className="flex -space-x-2">
                                                {team.teamMembers.slice(0, 3).map((member) => (
                                                    <Avatar key={member.id} className="h-8 w-8 border-2 border-background">
                                                        <AvatarImage src={member.avatar} alt={member.name} />
                                                        <AvatarFallback>
                                                            {member.name.charAt(0).toUpperCase()}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                ))}
                                                {team.teamMembers.length > 3 && (
                                                    <div className="flex h-8 w-8 items-center justify-center rounded-full border-2 border-background bg-muted text-xs font-medium">
                                                        +{team.teamMembers.length - 3}
                                                    </div>
                                                )}
                                            </div>
                                        </td>
                                        <td className="p-4">
                                            <div className="w-full bg-secondary rounded-full h-2">
                                                <div
                                                    className="bg-primary h-2 rounded-full transition-all"
                                                    style={{ width: `${team.progress}%` }}
                                                />
                                            </div>
                                            <span className="text-sm text-muted-foreground mt-1">
                                                {team.progress}%
                                            </span>
                                        </td>
                                        <td className="p-4 text-sm text-muted-foreground">
                                            {new Date(team.endDate).toLocaleDateString()}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </Card>
            )}
        </div>
    );
};

export default Dashboard; 