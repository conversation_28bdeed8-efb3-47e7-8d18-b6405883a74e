import React from 'react';
import { Route, Routes } from 'react-router-dom';
import { HealthMetricsPage } from '../components/HealthMetricsPage';
import { HealthMetricDetailPage } from './HealthMetricDetailPage';
import { HealthMetricCheckInPage } from './HealthMetricCheckInPage';
import { HealthMetricCreatePage } from './HealthMetricCreatePage';

export const HealthMetricsRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<HealthMetricsPage />} />
      <Route path="/new" element={<HealthMetricCreatePage />} />
      <Route path="/:metricId" element={<HealthMetricDetailPage />} />
      <Route path="/:metricId/check-in" element={<HealthMetricCheckInPage />} />
      <Route path="/check-in" element={<HealthMetricCheckInPage />} />
    </Routes>
  );
};
