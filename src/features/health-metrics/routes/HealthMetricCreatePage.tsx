import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { useHealthMetricsStore } from '../store/healthMetricsStore';
import { HealthMetricForm } from '../components/HealthMetricForm';
import { CreateHealthMetricParams, UpdateHealthMetricParams } from '../types/healthMetrics.types';

export const HealthMetricCreatePage: React.FC = () => {
  const { teamId } = useParams<{ teamId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const {
    isCreating,
    error,
    createMetric,
    clearError
  } = useHealthMetricsStore();

  React.useEffect(() => {
    // Show error toast if there's an error
    if (error) {
      toast({
        title: 'Error',
        description: error,
        variant: 'destructive',
      });
      clearError();
    }
  }, [error, toast, clearError]);

  const handleBack = () => {
    if (teamId) {
      navigate(`/teams/${teamId}/health-metrics`);
    }
  };

  const handleCreate = async (values: CreateHealthMetricParams | UpdateHealthMetricParams) => {
    if (!teamId) return;

    try {
      // Check if it's a CreateHealthMetricParams
      if ('team_id' in values) {
        await createMetric(values as CreateHealthMetricParams);
      } else {
        // It's an UpdateHealthMetricParams, but we're creating a new metric
        await createMetric({
          name: values.name || '',
          description: values.description,
          green_threshold: values.green_threshold || 0,
          yellow_threshold: values.yellow_threshold || 0,
          red_threshold: values.red_threshold || 0,
          unit: values.unit,
          soe: values.soe,
          team_id: teamId,
        });
      }

      toast({
        title: 'Success',
        description: 'Health metric created successfully',
      });

      // Navigate back to the metrics list
      handleBack();
    } catch (error) {
      console.error('Error creating health metric:', error);
    }
  };

  if (!teamId) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center items-center h-40">
          <p>Team ID is required</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Health Metrics
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Create New Health Metric</CardTitle>
        </CardHeader>
        <CardContent>
          <HealthMetricForm
            teamId={teamId}
            onSubmit={handleCreate}
            onCancel={handleBack}
            isSubmitting={isCreating}
          />
        </CardContent>
      </Card>
    </div>
  );
};
