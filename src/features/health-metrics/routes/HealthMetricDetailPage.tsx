import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, Edit, Archive, ToggleLeft, ToggleRight } from 'lucide-react';
import { UserAvatar } from '../components/UserAvatar';
import { useUserStore } from '@/store/user.store';
import { useHealthMetricsStore } from '../store/healthMetricsStore';
import { HealthMetricForm } from '../components/HealthMetricForm';
import { HealthMetricMicroChart } from '../components/HealthMetricMicroChart';
import { TeamHealthMetricValue, UpdateHealthMetricParams, CreateHealthMetricParams } from '../types/healthMetrics.types';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

export const HealthMetricDetailPage: React.FC = () => {
  const { teamId, metricId } = useParams<{ teamId: string; metricId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [isEditing, setIsEditing] = useState(false);
  const [isArchiveDialogOpen, setIsArchiveDialogOpen] = useState(false);

  const {
    selectedMetric,
    metricHistory,
    isLoading,
    isHistoryLoading,
    isUpdating,
    error,
    fetchMetricWithHistory,
    updateMetric,
    archiveMetric,
    toggleMetricActive,
    clearSelectedMetric,
    clearError
  } = useHealthMetricsStore();

  // Get the user store for batch loading profiles
  const { getProfile } = useUserStore();

  useEffect(() => {
    if (!metricId) return;

    // Fetch both metric details and history in a single call
    fetchMetricWithHistory(metricId, 30);

    // Cleanup when component unmounts
    return () => {
      clearSelectedMetric();
    };
  }, [metricId, fetchMetricWithHistory, clearSelectedMetric]);

  // Preload user profiles for all reported_by values
  useEffect(() => {
    if (metricHistory.length > 0) {
      // Get unique user IDs from the history
      const userIds = Array.from(new Set(
        metricHistory
          .map(entry => entry.reported_by)
          .filter(id => id !== null) as string[]
      ));

      // Preload all user profiles in batch
      userIds.forEach(userId => {
        getProfile(userId).catch(error => {
          console.error(`Error loading profile for user ${userId}:`, error);
        });
      });
    }
  }, [metricHistory, getProfile]);

  useEffect(() => {
    // Show error toast if there's an error
    if (error) {
      toast({
        title: 'Error',
        description: error,
        variant: 'destructive',
      });
      clearError();
    }
  }, [error, toast, clearError]);

  const handleBack = () => {
    if (teamId) {
      navigate(`/teams/${teamId}/health-metrics`);
    }
  };

  const handleUpdate = async (values: CreateHealthMetricParams | UpdateHealthMetricParams) => {
    if (!metricId) return;

    try {
      await updateMetric({
        ...values,
        id: metricId,
      });

      setIsEditing(false);
      toast({
        title: 'Success',
        description: 'Health metric updated successfully',
      });
    } catch (error) {
      console.error('Error updating health metric:', error);
    }
  };

  const handleArchive = async () => {
    if (!metricId) return;

    try {
      await archiveMetric(metricId);

      setIsArchiveDialogOpen(false);
      toast({
        title: 'Success',
        description: 'Health metric archived successfully',
      });

      // Navigate back to the metrics list
      if (teamId) {
        navigate(`/teams/${teamId}/health-metrics`);
      }
    } catch (error) {
      console.error('Error archiving health metric:', error);
    }
  };

  const handleToggleActive = async () => {
    if (!metricId || !selectedMetric) return;

    try {
      await toggleMetricActive(metricId, !selectedMetric.is_active);

      toast({
        title: 'Success',
        description: `Health metric ${selectedMetric.is_active ? 'deactivated' : 'activated'} successfully`,
      });
    } catch (error) {
      console.error('Error toggling health metric active status:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Show loading state while fetching metric details
  if (isLoading || (!selectedMetric && metricId)) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center items-center h-40">
          <p>Loading metric details...</p>
        </div>
      </div>
    );
  }

  // Only show "Metric not found" if we're not loading and there's no metric
  // This prevents the flash of "Metric not found" during loading
  if (!isLoading && !selectedMetric) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center items-center h-40">
          <p>Metric not found</p>
        </div>
      </div>
    );
  }

  // Don't render the main content until we have the metric data
  if (!selectedMetric) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center items-center h-40">
          <p>Loading metric details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Health Metrics
        </Button>
      </div>

      {isEditing ? (
        <Card>
          <CardHeader>
            <CardTitle>Edit Health Metric</CardTitle>
          </CardHeader>
          <CardContent>
            {teamId && (
              <HealthMetricForm
                teamId={teamId}
                metric={selectedMetric}
                onSubmit={handleUpdate}
                onCancel={() => setIsEditing(false)}
                isSubmitting={isUpdating}
              />
            )}
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="flex justify-between items-start mb-6">
            <div>
              <h1 className="text-3xl font-bold">{selectedMetric.name}</h1>
              {selectedMetric.description && (
                <p className="text-gray-500 mt-2">{selectedMetric.description}</p>
              )}
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => setIsEditing(true)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
              <Button
                variant={selectedMetric.is_active ? "outline" : "default"}
                onClick={handleToggleActive}
              >
                {selectedMetric.is_active ? (
                  <>
                    <ToggleRight className="mr-2 h-4 w-4" />
                    Active
                  </>
                ) : (
                  <>
                    <ToggleLeft className="mr-2 h-4 w-4" />
                    Inactive
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsArchiveDialogOpen(true)}
                disabled={selectedMetric.is_archived}
              >
                <Archive className="mr-2 h-4 w-4" />
                Archive
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Green Threshold</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {selectedMetric.green_threshold}
                  {selectedMetric.unit && <span className="text-sm ml-1">{selectedMetric.unit}</span>}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Yellow Threshold</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {selectedMetric.yellow_threshold}
                  {selectedMetric.unit && <span className="text-sm ml-1">{selectedMetric.unit}</span>}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm font-medium">Red Threshold</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {selectedMetric.red_threshold}
                  {selectedMetric.unit && <span className="text-sm ml-1">{selectedMetric.unit}</span>}
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="history">
            <TabsList>
              <TabsTrigger value="history">History</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
              {selectedMetric.soe && (
                <TabsTrigger value="soe">Standard Operating Procedure</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="history" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Historical Data</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 mb-6">
                    <HealthMetricMicroChart metricId={selectedMetric.id} limit={30} />
                  </div>

                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 px-4">Date</th>
                          <th className="text-left py-2 px-4">Value</th>
                          <th className="text-left py-2 px-4">Status</th>
                          <th className="text-left py-2 px-4">Reported by</th>
                          <th className="text-left py-2 px-4">Notes</th>
                        </tr>
                      </thead>
                      <tbody>
                        {isHistoryLoading ? (
                          <tr>
                            <td colSpan={5} className="py-4 text-center text-gray-500">
                              Loading historical data...
                            </td>
                          </tr>
                        ) : metricHistory.length === 0 ? (
                          <tr>
                            <td colSpan={5} className="py-4 text-center text-gray-500">
                              No historical data available
                            </td>
                          </tr>
                        ) : (
                          metricHistory.map((entry: TeamHealthMetricValue) => (
                            <tr key={entry.id} className="border-b">
                              <td className="py-2 px-4">{formatDate(entry.reported_at)}</td>
                              <td className="py-2 px-4">
                                {entry.value}
                                {selectedMetric.unit && <span className="ml-1">{selectedMetric.unit}</span>}
                              </td>
                              <td className="py-2 px-4">
                                <span
                                  className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                                    entry.status === 'green' ? 'bg-green-100 text-green-800' :
                                    entry.status === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-red-100 text-red-800'
                                  }`}
                                >
                                  {entry.status.charAt(0).toUpperCase() + entry.status.slice(1)}
                                </span>
                              </td>
                              <td className="py-2 px-4">
                                <UserAvatar userId={entry.reported_by} size="sm" />
                              </td>
                              <td className="py-2 px-4">{entry.notes || '-'}</td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="details" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Metric Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Created</dt>
                      <dd>{formatDate(selectedMetric.created_at)}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                      <dd>{formatDate(selectedMetric.updated_at)}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Status</dt>
                      <dd>
                        {selectedMetric.is_archived ? 'Archived' :
                         selectedMetric.is_active ? 'Active' : 'Inactive'}
                      </dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Unit</dt>
                      <dd>{selectedMetric.unit || '-'}</dd>
                    </div>
                  </dl>
                </CardContent>
              </Card>
            </TabsContent>

            {selectedMetric.soe && (
              <TabsContent value="soe" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Standard Operating Procedure</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                      <h4 className="font-medium text-red-800 mb-2">When this metric is in the red zone:</h4>
                      <p className="text-red-700 whitespace-pre-line">{selectedMetric.soe}</p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        </>
      )}

      <AlertDialog open={isArchiveDialogOpen} onOpenChange={setIsArchiveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Archive Health Metric</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to archive this health metric?
              Archived metrics will no longer appear in the active metrics list.
              This action can't be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleArchive}>Archive</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
