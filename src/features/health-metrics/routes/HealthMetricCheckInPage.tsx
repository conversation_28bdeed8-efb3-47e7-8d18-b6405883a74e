import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { useHealthMetricsStore } from '../store/healthMetricsStore';
import { HealthMetricsCheckIn } from '../components/HealthMetricsCheckIn';
import { TeamHealthMetricWithValues } from '../types/healthMetrics.types';

export const HealthMetricCheckInPage: React.FC = () => {
  const { teamId, metricId } = useParams<{ teamId: string; metricId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [selectedMetrics, setSelectedMetrics] = useState<TeamHealthMetricWithValues[]>([]);
  
  const { 
    metrics,
    selectedMetric,
    isLoading, 
    isCheckinIn,
    error,
    fetchMetrics,
    fetchMetricById,
    checkInMetric,
    clearSelectedMetric,
    clearError
  } = useHealthMetricsStore();

  useEffect(() => {
    if (!teamId) return;
    
    // If a specific metric ID is provided, fetch just that metric
    if (metricId) {
      fetchMetricById(metricId);
    } 
    // Otherwise, fetch all active metrics for the team
    else {
      fetchMetrics(teamId);
    }
    
    // Cleanup when component unmounts
    return () => {
      clearSelectedMetric();
    };
  }, [teamId, metricId, fetchMetrics, fetchMetricById, clearSelectedMetric]);

  useEffect(() => {
    // If a specific metric was fetched, use that
    if (metricId && selectedMetric) {
      setSelectedMetrics([selectedMetric]);
    } 
    // Otherwise, use all active metrics
    else if (!metricId && metrics.length > 0) {
      setSelectedMetrics(metrics.filter(m => m.is_active));
    }
  }, [metricId, selectedMetric, metrics]);

  useEffect(() => {
    // Show error toast if there's an error
    if (error) {
      toast({
        title: 'Error',
        description: error,
        variant: 'destructive',
      });
      clearError();
    }
  }, [error, toast, clearError]);

  const handleBack = () => {
    if (teamId) {
      navigate(`/teams/${teamId}/health-metrics`);
    }
  };

  const handleCheckIn = async (metricId: string, value: number, notes?: string) => {
    try {
      await checkInMetric({
        team_health_metric_id: metricId,
        value,
        notes,
      });
      
      toast({
        title: 'Success',
        description: 'Health metric checked in successfully',
      });
      
      // If we're checking in a single metric, navigate back
      if (selectedMetrics.length === 1) {
        handleBack();
      } else {
        // Remove the checked-in metric from the list
        setSelectedMetrics(selectedMetrics.filter(m => m.id !== metricId));
      }
    } catch (error) {
      console.error('Error checking in health metric:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center items-center h-40">
          <p>Loading metrics...</p>
        </div>
      </div>
    );
  }

  if (selectedMetrics.length === 0) {
    return (
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Health Metrics
          </Button>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-40">
            <p className="text-gray-500 mb-4">No active health metrics found</p>
            <Button onClick={handleBack}>Return to Health Metrics</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Health Metrics
        </Button>
      </div>
      
      <div className="space-y-6">
        {selectedMetrics.map((metric) => (
          <Card key={metric.id}>
            <CardHeader>
              <CardTitle>Check-in: {metric.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <HealthMetricsCheckIn
                metric={metric}
                onSubmit={(metricId, value, notes) => handleCheckIn(metricId, value, notes)}
                onCancel={handleBack}
                isSubmitting={isCheckinIn}
              />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
