export type HealthMetricStatus = 'green' | 'yellow' | 'red';

export interface TeamHealthMetric {
  id: string;
  team_id: string;
  name: string;
  description: string | null;
  green_threshold: number;
  yellow_threshold: number;
  red_threshold: number;
  unit: string | null;
  is_active: boolean;
  is_archived: boolean;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  soe: string | null; // Standard Operating Procedure
}

export interface TeamHealthMetricValue {
  id: string;
  team_health_metric_id: string;
  value: number;
  status: HealthMetricStatus;
  notes: string | null;
  reported_at: string;
  reported_by: string | null;
}

export interface TeamHealthMetricWithValues extends TeamHealthMetric {
  values?: TeamHealthMetricValue[];
  latest_value?: TeamHealthMetricValue;
}

export interface CreateHealthMetricParams {
  team_id: string;
  name: string;
  description?: string;
  green_threshold: number;
  yellow_threshold: number;
  red_threshold: number;
  unit?: string;
  soe?: string;
}

export interface UpdateHealthMetricParams {
  id: string;
  name?: string;
  description?: string;
  green_threshold?: number;
  yellow_threshold?: number;
  red_threshold?: number;
  unit?: string;
  is_active?: boolean;
  is_archived?: boolean;
  soe?: string;
}

export interface CheckInHealthMetricParams {
  team_health_metric_id: string;
  value: number;
  notes?: string;
}

export interface HealthMetricFilters {
  is_active?: boolean;
  is_archived?: boolean;
  team_id?: string;
}
