import { formatDistanceToNow } from 'date-fns';

/**
 * Formats a date in a compact way for display in space-constrained UI elements
 * 
 * @param date The date to format
 * @returns A compact string representation of the relative time
 */
export const formatCompactDate = (date: string | Date | null | undefined): string => {
  if (!date) return 'Never';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Get the relative time using date-fns
  const fullRelativeTime = formatDistanceToNow(dateObj, { addSuffix: true });
  
  // Make it more compact
  return fullRelativeTime
    // Replace "less than" with "<"
    .replace('less than', '<')
    // Replace "about" with "~"
    .replace('about', '~')
    // Replace "minute" with "min"
    .replace('minute', 'min')
    // Replace "minutes" with "min"
    .replace('minutes', 'min')
    // Replace "hour" with "hr"
    .replace('hour', 'hr')
    // Replace "hours" with "hr"
    .replace('hours', 'hr')
    // Replace "day" with "d"
    .replace('day', 'd')
    // Replace "days" with "d"
    .replace('days', 'd')
    // Replace "month" with "mo"
    .replace('month', 'mo')
    // Replace "months" with "mo"
    .replace('months', 'mo')
    // Replace "year" with "yr"
    .replace('year', 'yr')
    // Replace "years" with "yr"
    .replace('years', 'yr');
};
