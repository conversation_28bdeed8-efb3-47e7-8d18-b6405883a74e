/**
 * Trend Utilities
 *
 * This module provides utilities for calculating and describing trends in health metrics data.
 * It includes functions for determining trend direction, calculating percentage changes,
 * and generating human-readable descriptions of trends.
 */

import { TeamHealthMetricValue } from '../types/healthMetrics.types';

/**
 * Possible directions for a trend
 *
 * - improving: The metric is moving in a positive direction
 * - declining: The metric is moving in a negative direction
 * - stable: The metric is not changing significantly
 * - unknown: There is not enough data to determine a trend
 */
export type TrendDirection = 'improving' | 'declining' | 'stable' | 'unknown';

/**
 * Information about a trend
 *
 * @property direction - The direction of the trend (improving, declining, stable, unknown)
 * @property percentage - The percentage change, or null if unknown
 * @property isSignificant - Whether the change is significant (e.g., > 5%)
 */
export interface TrendInfo {
  direction: TrendDirection;
  percentage: number | null;
  isSignificant: boolean; // True if the change is significant (e.g., > 5%)
}

/**
 * Calculate the trend direction and percentage change based on historical data
 * @param history Array of metric values sorted from oldest to newest
 * @param significanceThreshold Percentage threshold to consider a change significant (default: 5%)
 * @returns Trend information including direction and percentage change
 */
export const calculateTrend = (history: TeamHealthMetricValue[], significanceThreshold = 5): TrendInfo => {
  // Default return for insufficient data
  const defaultTrend: TrendInfo = {
    direction: 'unknown',
    percentage: null,
    isSignificant: false
  };

  // Need at least 2 data points to calculate a trend
  if (!history || !Array.isArray(history) || history.length < 2) {
    return defaultTrend;
  }

  try {
    // Get the oldest and newest values that are valid numbers
    const validHistory = history.filter(item => typeof item.value === 'number');
    if (validHistory.length < 2) return defaultTrend;

    // Get the oldest and newest values
    const oldestValue = validHistory[0].value;
    const newestValue = validHistory[validHistory.length - 1].value;

    // Calculate percentage change
    const absoluteChange = newestValue - oldestValue;
    let percentageChange = 0;

    // Avoid division by zero
    if (oldestValue !== 0) {
      percentageChange = (absoluteChange / Math.abs(oldestValue)) * 100;
    } else if (absoluteChange !== 0) {
      // If oldestValue is 0 and there is a change, use a large percentage
      percentageChange = absoluteChange > 0 ? 100 : -100;
    }

    // Determine trend direction
    let direction: TrendDirection = 'stable';

    // For health metrics, the interpretation of "improving" depends on the context
    // Here we assume that increasing values are improving, but this might need to be
    // adjusted based on the specific metric (e.g., for error rates, decreasing is improving)
    if (Math.abs(percentageChange) < 1) {
      direction = 'stable';
    } else if (percentageChange > 0) {
      direction = 'improving';
    } else {
      direction = 'declining';
    }

    // Check if the change is significant
    const isSignificant = Math.abs(percentageChange) >= significanceThreshold;

    return {
      direction,
      percentage: parseFloat(percentageChange.toFixed(1)),
      isSignificant
    };
  } catch (error) {
    console.error('Error calculating trend:', error);
    return defaultTrend;
  }
};

/**
 * Get a human-readable description of the trend
 * @param trend The trend information
 * @returns A string describing the trend
 */
export const getTrendDescription = (trend: TrendInfo): string => {
  if (trend.direction === 'unknown' || trend.percentage === null) {
    return 'Trend: Not enough data';
  }

  if (trend.direction === 'stable') {
    return 'Trend: Stable';
  }

  const changeText = Math.abs(trend.percentage).toFixed(1);

  if (trend.direction === 'improving') {
    return `Improving by ${changeText}%`;
  } else {
    return `Declining by ${changeText}%`;
  }
};
