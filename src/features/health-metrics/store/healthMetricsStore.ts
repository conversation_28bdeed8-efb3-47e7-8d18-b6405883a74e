/**
 * Health Metrics Store
 *
 * This module provides a Zustand store for managing health metrics data.
 * It handles fetching, creating, updating, and filtering health metrics.
 *
 * The store uses middleware for devtools integration and persistence.
 */

import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';
import { supabase } from '@/lib/supabase';
import {
  TeamHealthMetric,
  TeamHealthMetricValue,
  TeamHealthMetricWithValues,
  CreateHealthMetricParams,
  UpdateHealthMetricParams,
  CheckInHealthMetricParams,
  HealthMetricFilters
} from '../types/healthMetrics.types';
import {
  fetchTeamHealthMetrics,
  fetchHealthMetricById,
  fetchHealthMetricHistory,
  fetchMetricDetailsWithHistory,
  createHealthMetric,
  updateHealthMetric,
  archiveHealthMetric,
  toggleHealthMetricActive,
  checkInHealthMetric
} from '../api/healthMetricsApi';

interface HealthMetricsState {
  // Data
  metrics: TeamHealthMetricWithValues[];
  // Store metrics by team ID
  teamMetrics: Record<string, TeamHealthMetricWithValues[]>;
  selectedMetric: TeamHealthMetricWithValues | null;
  metricHistory: TeamHealthMetricValue[];
  // Store history data by metric ID
  metricHistoryById: Record<string, TeamHealthMetricValue[]>;

  // Loading states
  isLoading: boolean;
  isHistoryLoading: boolean;
  // Track loading state by team ID
  teamLoading: Record<string, boolean>;
  isCreating: boolean;
  isUpdating: boolean;
  isCheckinIn: boolean;

  // Error states
  error: string | null;

  // Filters
  filters: HealthMetricFilters;

  // Actions
  setFilters: (filters: HealthMetricFilters) => void;
  fetchMetrics: (teamId: string) => Promise<void>;
  getTeamMetrics: (teamId: string) => TeamHealthMetricWithValues[];
  fetchMetricById: (metricId: string) => Promise<void>;
  fetchMetricHistory: (metricId: string, limit?: number) => Promise<void>;
  fetchMetricWithHistory: (metricId: string, historyLimit?: number) => Promise<void>;
  createMetric: (params: CreateHealthMetricParams) => Promise<TeamHealthMetric>;
  updateMetric: (params: UpdateHealthMetricParams) => Promise<TeamHealthMetric>;
  archiveMetric: (metricId: string) => Promise<TeamHealthMetric>;
  toggleMetricActive: (metricId: string, isActive: boolean) => Promise<TeamHealthMetric>;
  checkInMetric: (params: CheckInHealthMetricParams) => Promise<TeamHealthMetricValue>;
  clearSelectedMetric: () => void;
  clearError: () => void;
}

/**
 * Health Metrics Store
 *
 * This store manages the state for health metrics, including:
 * - Fetching metrics for teams
 * - Creating and updating metrics
 * - Recording check-ins
 * - Filtering metrics
 * - Managing loading and error states
 *
 * The store uses devtools middleware for debugging and persist middleware
 * for persisting certain parts of the state across page reloads.
 */
export const useHealthMetricsStore = create<HealthMetricsState>(
  // @ts-expect-error - Zustand middleware type issue
  devtools(
    persist(
      (set, get) => ({
  // Initial state
  metrics: [],
  teamMetrics: {},
  selectedMetric: null,
  metricHistory: [],
  metricHistoryById: {},
  isLoading: false,
  isHistoryLoading: false,
  teamLoading: {},
  isCreating: false,
  isUpdating: false,
  isCheckinIn: false,
  error: null,
  filters: {
    is_active: true,
    is_archived: false
  },

  // Actions
  setFilters: (filters) => {
    set((state) => ({
      filters: { ...state.filters, ...filters }
    }));
  },

  /**
   * Fetch health metrics for a team
   *
   * This action fetches metrics from the API and updates the store.
   * It also sets the loading state for the team.
   *
   * @param teamId - The ID of the team to fetch metrics for
   */
  fetchMetrics: async (teamId) => {
    // Set loading state for this specific team
    set((state) => ({
      teamLoading: { ...state.teamLoading, [teamId]: true },
      isLoading: true,
      error: null
    }));

    try {
      const metrics = await fetchTeamHealthMetrics(teamId, get().filters);

      // Update metrics for this specific team
      set((state) => ({
        // Keep the metrics state for backward compatibility
        metrics: teamId === state.filters.team_id ? metrics : state.metrics,
        // Store metrics by team ID
        teamMetrics: { ...state.teamMetrics, [teamId]: metrics },
        teamLoading: { ...state.teamLoading, [teamId]: false },
        isLoading: false
      }));
    } catch (error) {
      set((state) => ({
        error: error instanceof Error ? error.message : 'Failed to fetch health metrics',
        teamLoading: { ...state.teamLoading, [teamId]: false },
        isLoading: false
      }));
    }
  },

  /**
   * Get metrics for a specific team from the store
   *
   * @param teamId - The ID of the team to get metrics for
   * @returns An array of team health metrics with their values
   */
  getTeamMetrics: (teamId) => {
    // This is a simple memoization that will be reset when the store updates
    // For more complex scenarios, consider using a library like reselect
    const metrics = get().teamMetrics[teamId] || [];
    return metrics;
  },

  fetchMetricById: async (metricId) => {
    // Set loading state first
    set({ isLoading: true, error: null });
    try {
      const metric = await fetchHealthMetricById(metricId);
      // Update the state with the fetched metric
      set({ selectedMetric: metric, isLoading: false });
    } catch (error) {
      // Handle errors and clear loading state
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch health metric',
        isLoading: false
      });
    }
  },

  fetchMetricHistory: async (metricId, limit = 10) => {
    // Use isHistoryLoading instead of isLoading to avoid conflicts
    set({ isHistoryLoading: true, error: null });
    try {
      const history = await fetchHealthMetricHistory(metricId, limit);

      // Store history both in the global state (for backward compatibility)
      // and in the metric-specific state
      set((state) => ({
        metricHistory: history,
        metricHistoryById: {
          ...state.metricHistoryById,
          [metricId]: history
        },
        isHistoryLoading: false
      }));
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch health metric history',
        isHistoryLoading: false
      });
    }
  },

  /**
   * Fetch both metric details and history in a single call
   * This reduces the number of API calls needed
   *
   * @param metricId - The ID of the metric to fetch
   * @param historyLimit - The maximum number of history entries to fetch
   */
  fetchMetricWithHistory: async (metricId, historyLimit = 30) => {
    // Set both loading states
    set({ isLoading: true, isHistoryLoading: true, error: null });
    try {
      // Make a single API call to get both metric and history
      const { metric, history } = await fetchMetricDetailsWithHistory(metricId, historyLimit);

      // Update both states at once to prevent UI flicker
      set((state) => ({
        selectedMetric: metric,
        metricHistory: history,
        metricHistoryById: {
          ...state.metricHistoryById,
          [metricId]: history
        },
        isLoading: false,
        isHistoryLoading: false
      }));
    } catch (error) {
      // Handle errors and clear loading states
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch health metric data',
        isLoading: false,
        isHistoryLoading: false
      });
    }
  },

  createMetric: async (params) => {
    set({ isCreating: true, error: null });
    try {
      const newMetric = await createHealthMetric(params);
      const teamId = params.team_id;

      set((state) => {
        // Update global metrics (for backward compatibility)
        const updatedMetrics = [newMetric as TeamHealthMetricWithValues, ...state.metrics];

        // Update team-specific metrics
        const teamMetrics = state.teamMetrics[teamId] || [];
        const updatedTeamMetrics = [newMetric as TeamHealthMetricWithValues, ...teamMetrics];

        return {
          metrics: updatedMetrics,
          teamMetrics: {
            ...state.teamMetrics,
            [teamId]: updatedTeamMetrics
          },
          isCreating: false
        };
      });

      return newMetric;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to create health metric',
        isCreating: false
      });
      throw error;
    }
  },

  updateMetric: async (params) => {
    set({ isUpdating: true, error: null });
    try {
      const updatedMetric = await updateHealthMetric(params);

      // Get the team ID for this metric
      const { data: metric, error: metricError } = await supabase
        .from('team_health_metrics')
        .select('team_id')
        .eq('id', params.id)
        .single();

      if (metricError) {
        throw metricError;
      }

      const teamId = metric.team_id;

      set((state) => {
        // Update global metrics (for backward compatibility)
        const updatedMetrics = state.metrics.map(m =>
          m.id === updatedMetric.id
            ? { ...m, ...updatedMetric }
            : m
        );

        // Update team-specific metrics
        const teamMetrics = state.teamMetrics[teamId] || [];
        const updatedTeamMetrics = teamMetrics.map(m =>
          m.id === updatedMetric.id
            ? { ...m, ...updatedMetric }
            : m
        );

        return {
          metrics: updatedMetrics,
          teamMetrics: {
            ...state.teamMetrics,
            [teamId]: updatedTeamMetrics
          },
          selectedMetric: state.selectedMetric?.id === updatedMetric.id
            ? { ...state.selectedMetric, ...updatedMetric }
            : state.selectedMetric,
          isUpdating: false
        };
      });

      return updatedMetric;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to update health metric',
        isUpdating: false
      });
      throw error;
    }
  },

  archiveMetric: async (metricId) => {
    set({ isUpdating: true, error: null });
    try {
      // Get the team ID for this metric before archiving
      const { data: metric, error: metricError } = await supabase
        .from('team_health_metrics')
        .select('team_id')
        .eq('id', metricId)
        .single();

      if (metricError) {
        throw metricError;
      }

      const teamId = metric.team_id;

      const archivedMetric = await archiveHealthMetric(metricId);

      set((state) => {
        // Update global metrics (for backward compatibility)
        const updatedMetrics = state.metrics.map(m =>
          m.id === archivedMetric.id
            ? { ...m, ...archivedMetric }
            : m
        );

        // Update team-specific metrics
        const teamMetrics = state.teamMetrics[teamId] || [];
        const updatedTeamMetrics = teamMetrics.map(m =>
          m.id === archivedMetric.id
            ? { ...m, ...archivedMetric }
            : m
        );

        return {
          metrics: updatedMetrics,
          teamMetrics: {
            ...state.teamMetrics,
            [teamId]: updatedTeamMetrics
          },
          selectedMetric: state.selectedMetric?.id === archivedMetric.id
            ? { ...state.selectedMetric, ...archivedMetric }
            : state.selectedMetric,
          isUpdating: false
        };
      });

      return archivedMetric;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to archive health metric',
        isUpdating: false
      });
      throw error;
    }
  },

  toggleMetricActive: async (metricId, isActive) => {
    set({ isUpdating: true, error: null });
    try {
      // Get the team ID for this metric
      const { data: metric, error: metricError } = await supabase
        .from('team_health_metrics')
        .select('team_id')
        .eq('id', metricId)
        .single();

      if (metricError) {
        throw metricError;
      }

      const teamId = metric.team_id;

      const updatedMetric = await toggleHealthMetricActive(metricId, isActive);

      set((state) => {
        // Update global metrics (for backward compatibility)
        const updatedMetrics = state.metrics.map(m =>
          m.id === updatedMetric.id
            ? { ...m, ...updatedMetric }
            : m
        );

        // Update team-specific metrics
        const teamMetrics = state.teamMetrics[teamId] || [];
        const updatedTeamMetrics = teamMetrics.map(m =>
          m.id === updatedMetric.id
            ? { ...m, ...updatedMetric }
            : m
        );

        return {
          metrics: updatedMetrics,
          teamMetrics: {
            ...state.teamMetrics,
            [teamId]: updatedTeamMetrics
          },
          selectedMetric: state.selectedMetric?.id === updatedMetric.id
            ? { ...state.selectedMetric, ...updatedMetric }
            : state.selectedMetric,
          isUpdating: false
        };
      });

      return updatedMetric;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to toggle health metric active status',
        isUpdating: false
      });
      throw error;
    }
  },

  checkInMetric: async (params) => {
    set({ isCheckinIn: true, error: null });
    try {
      const newValue = await checkInHealthMetric(params);

      // Get the team ID for this metric
      const { data: metric, error: metricError } = await supabase
        .from('team_health_metrics')
        .select('team_id')
        .eq('id', params.team_health_metric_id)
        .single();

      if (metricError) {
        throw metricError;
      }

      const teamId = metric.team_id;

      // Update the metrics list with the new value
      set((state) => {
        // Update the global metrics list (for backward compatibility)
        const updatedMetrics = state.metrics.map(metric => {
          if (metric.id === params.team_health_metric_id) {
            return {
              ...metric,
              latest_value: newValue
            };
          }
          return metric;
        });

        // Update the team-specific metrics
        const teamMetrics = state.teamMetrics[teamId] || [];
        const updatedTeamMetrics = teamMetrics.map(metric => {
          if (metric.id === params.team_health_metric_id) {
            return {
              ...metric,
              latest_value: newValue
            };
          }
          return metric;
        });

        // Also update the selected metric if it's the one being checked in
        const updatedSelectedMetric = state.selectedMetric?.id === params.team_health_metric_id
          ? {
              ...state.selectedMetric,
              latest_value: newValue
            }
          : state.selectedMetric;

        // Add the new value to the history if we're viewing that metric
        const updatedHistory = state.selectedMetric?.id === params.team_health_metric_id
          ? [newValue, ...state.metricHistory]
          : state.metricHistory;

        // Always update the metric-specific history regardless of selected metric
        // This ensures charts are updated even if we're not on the detail page
        const metricId = params.team_health_metric_id;
        const currentMetricHistory = state.metricHistoryById[metricId] || [];
        const updatedMetricHistory = [newValue, ...currentMetricHistory];

        return {
          metrics: updatedMetrics,
          teamMetrics: {
            ...state.teamMetrics,
            [teamId]: updatedTeamMetrics
          },
          selectedMetric: updatedSelectedMetric,
          metricHistory: updatedHistory,
          metricHistoryById: {
            ...state.metricHistoryById,
            [metricId]: updatedMetricHistory
          },
          isCheckinIn: false
        };
      });

      return newValue;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to check in health metric',
        isCheckinIn: false
      });
      throw error;
    }
  },

  clearSelectedMetric: () => {
    // Get the current selected metric ID before clearing it
    const currentMetricId = get().selectedMetric?.id;

    if (currentMetricId) {
      // If we have a selected metric, clear its history from metricHistoryById
      set((state) => ({
        selectedMetric: null,
        metricHistory: [],
        // Keep other metrics' history but remove the current one
        metricHistoryById: {
          ...state.metricHistoryById,
          [currentMetricId]: [] // Clear the history for this metric
        }
      }));
    } else {
      // If no metric is selected, just clear the selectedMetric and metricHistory
      set({ selectedMetric: null, metricHistory: [] });
    }
  },

  clearError: () => {
    set({ error: null });
  }
}),
      {
        name: 'health-metrics-storage',
        storage: createJSONStorage(() => sessionStorage),
        partialize: (state) => ({
          // Only persist these fields to avoid large storage
          filters: state.filters,
          // Don't persist large data arrays or sensitive information
        }),
      }
    )
  )
);
