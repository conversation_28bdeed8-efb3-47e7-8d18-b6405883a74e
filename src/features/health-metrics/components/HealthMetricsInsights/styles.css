.insights-content {
  line-height: 1.6;
  color: var(--foreground);
}

.insights-content h1,
.insights-content h2,
.insights-content h3 {
  color: var(--primary);
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  line-height: 1.3;
}

.insights-content h1 {
  font-size: 2rem;
  font-weight: 700;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.insights-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
}

.insights-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
}

.insights-content p {
  margin-bottom: 1rem;
}

.insights-content ul,
.insights-content ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.insights-content ul {
  list-style-type: disc;
}

.insights-content ol {
  list-style-type: decimal;
}

.insights-content li {
  margin-bottom: 0.5rem;
}

.insights-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.insights-content thead {
  background-color: var(--primary);
  color: white;
}

.insights-content th {
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
}

.insights-content td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border);
}

.insights-content tr:nth-child(even) {
  background-color: var(--muted);
}

.insights-content hr {
  margin: 2rem 0;
  border: 0;
  height: 1px;
  background-color: var(--border);
}

.insights-content blockquote {
  margin: 1.5rem 0;
  padding: 0.5rem 0 0.5rem 1.5rem;
  border-left: 4px solid var(--primary);
  background-color: var(--muted);
  border-radius: 0.25rem;
  font-style: italic;
}

.insights-content strong {
  font-weight: 600;
  color: var(--primary);
}

.insights-content em {
  font-style: italic;
}

.insights-content code {
  font-family: monospace;
  background-color: var(--muted);
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.9em;
}

.insights-content pre {
  background-color: var(--muted);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.insights-content pre code {
  background-color: transparent;
  padding: 0;
}
