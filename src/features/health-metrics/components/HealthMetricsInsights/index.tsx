import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Lightbulb } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import './styles.css';

const insightMarkdown = `# 📊 Insight Doc: Health Metrics in OKR Cadence

## ✅ What are Health Metrics?

Health Metrics help teams **track ongoing stability and efficiency** without being tied to ambitious OKR targets. They don't drive direct outcomes but **signal when things are off-track**. Think of them as your team's "vital signs."

---

## 🧠 Why Include Health Metrics?

While OKRs focus on **outcomes and growth**, Health Metrics help ensure:
- Quality doesn't drop while chasing goals
- Team morale and performance stay consistent
- Early detection of issues before they escalate

---

## 🛠️ How to Use Health Metrics

- Set **clear thresholds**, not stretch targets
- **Track regularly** (weekly/bi-weekly)
- Use them during **OKR reviews**, sprint retros, and 1:1s
- Don't aim to "improve" them aggressively — just **maintain healthy levels**

---

## 🔍 Common Health Metrics

### 🚀 Engineering Health

| Metric                        | Good Looks Like        |
|------------------------------|------------------------|
| Deployment Frequency         | ≥ 1 deploy/day         |
| Lead Time for Changes        | < 1 day                |
| MTTR (Mean Time to Recovery) | < 1 hour               |
| Incident Count (P0/P1)       | 0–1/week               |
| Code Coverage                | ≥ 80%                  |

### 📦 Product Health

| Metric                  | Good Looks Like               |
|-------------------------|-------------------------------|
| Daily Active Users (DAU) | Consistent or growing trend   |
| Feature Adoption Rate   | > 30% within 2 weeks of release |
| NPS or CSAT Score       | > 40 NPS or > 4.2 CSAT        |
| Bugs Open vs Closed     | < 20% net increase            |

### 👥 Team & Culture Health

| Metric                   | Good Looks Like  |
|--------------------------|------------------|
| eNPS (Team Sentiment)    | > 50             |
| Burnout/Pulse Survey     | ≤ 10% red flags  |
| Attrition Rate           | < 10% quarterly  |
| Meetings per IC          | ≤ 10 hrs/week    |

---

## 🔄 Cadence to Review

| Frequency   | What to Review              | Where                    |
|-------------|-----------------------------|--------------------------|
| Weekly      | Metric Trends & Anomalies   | Standups / Check-ins     |
| Bi-weekly   | Metrics + OKRs Review       | Sprint Review / OKR Sync |
| Monthly     | Department Summary          | All-Hands / Dashboards   |
| Quarterly   | Revisit Metrics / Thresholds| OKR Planning Sessions    |

---

## 💡 Pro Tips

- Use color indicators (🟢🟡🔴) to highlight status
- Avoid metric overload — **stick to 3–5 per team**
- Review trends, not isolated spikes
`;

interface HealthMetricsInsightsProps {
  isOpen: boolean;
  onClose: () => void;
}

export const HealthMetricsInsightsButton: React.FC<{
  onClick: () => void;
}> = ({ onClick }) => {
  return (
    <Button
      variant="outline"
      onClick={onClick}
      className="flex items-center gap-2"
    >
      <Lightbulb className="h-4 w-4" />
      <span>Insights</span>
    </Button>
  );
};

export const HealthMetricsInsights: React.FC<HealthMetricsInsightsProps> = ({
  isOpen,
  onClose,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">Health Metrics Insights</DialogTitle>
          <DialogDescription>
            Learn how to effectively use health metrics in your OKR framework
          </DialogDescription>
        </DialogHeader>
        <div className="insights-content py-4">
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              h1: (props) => <h1 className="text-3xl font-bold mb-4 text-primary" {...props} />,
              h2: (props) => <h2 className="text-2xl font-semibold mt-6 mb-3 text-primary" {...props} />,
              h3: (props) => <h3 className="text-xl font-semibold mt-5 mb-2" {...props} />,
              p: (props) => <p className="mb-4" {...props} />,
              ul: (props) => <ul className="list-disc pl-6 mb-4" {...props} />,
              ol: (props) => <ol className="list-decimal pl-6 mb-4" {...props} />,
              li: (props) => <li className="mb-1" {...props} />,
              table: (props) => (
                <div className="overflow-x-auto mb-6">
                  <table className="min-w-full border-collapse border border-border" {...props} />
                </div>
              ),
              thead: (props) => <thead className="bg-muted" {...props} />,
              tbody: (props) => <tbody {...props} />,
              tr: (props) => <tr className="border-b border-border" {...props} />,
              th: (props) => <th className="px-4 py-2 text-left font-semibold" {...props} />,
              td: (props) => <td className="px-4 py-2 border-r border-border last:border-r-0" {...props} />,
              hr: (props) => <hr className="my-6 border-t border-border" {...props} />,
              strong: (props) => <strong className="font-bold text-primary" {...props} />,
              em: (props) => <em className="italic" {...props} />,
              blockquote: (props) => (
                <blockquote className="pl-4 border-l-4 border-primary/30 italic my-4" {...props} />
              ),
            }}
          >
            {insightMarkdown}
          </ReactMarkdown>
        </div>
      </DialogContent>
    </Dialog>
  );
};
