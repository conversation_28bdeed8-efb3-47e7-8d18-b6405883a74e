import { memo } from 'react';
import { TeamHealthMetricValue } from '../../types/healthMetrics.types';

interface BarChartProps {
  history: TeamHealthMetricValue[];
  minValue: number;
  maxValue: number;
  range: number;
  barWidth: number;
}

export const BarChart = memo(({
  history,
  minValue,
  // @ts-expect-error - maxValue is not used but kept in props for future use
  _maxValue,
  range,
  barWidth
}: BarChartProps) => {
  return (
    <div className="h-full w-full flex items-end">
      {history.map((item, index) => {
        // Normalize the value to a percentage height (10% minimum height for visibility)
        const normalizedHeight = 10 + ((item.value - minValue) / range) * 90;

        // Determine the color based on status
        const barColor =
          item.status === 'green' ? 'bg-green-500' :
          item.status === 'yellow' ? 'bg-yellow-500' :
          item.status === 'red' ? 'bg-red-500' :
          'bg-gray-300';

        return (
          <div
            key={item.id || index}
            className={`${barColor} rounded-t-sm mx-px`}
            style={{
              height: `${normalizedHeight}%`,
              width: `${barWidth}%`,
              maxWidth: '8px',
              transition: 'height 0.3s ease'
            }}
            title={`${item.value} - ${new Date(item.reported_at).toLocaleDateString()}`}
          />
        );
      })}
    </div>
  );
});
