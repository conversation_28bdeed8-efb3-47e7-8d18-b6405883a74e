/**
 * HealthMetricMicroChart Component
 *
 * This component displays a small chart showing the historical values of a health metric.
 * It handles data fetching, loading states, and rendering the chart.
 *
 * The component is designed to be small and focused, with the actual chart rendering
 * delegated to sub-components for better separation of concerns.
 */

import React, { useEffect, useState, useMemo } from 'react';
import { useHealthMetricsStore } from '../../store/healthMetricsStore';

// Import sub-components
import { Bar<PERSON>hart<PERSON>enderer } from './BarChartRenderer';
import { LoadingState } from './LoadingState';
import { EmptyState } from './EmptyState';

/**
 * Props for the HealthMetricMicroChart component
 *
 * @property metricId - The ID of the metric to display history for
 * @property limit - Optional limit on the number of historical data points to display
 */
interface HealthMetricMicroChartProps {
  metricId: string;
  limit?: number;
}

/**
 * HealthMetricMicroChart Component
 *
 * @param metricId - The ID of the metric to display history for
 * @param limit - Optional limit on the number of historical data points to display (default: 10)
 */
export const HealthMetricMicroChart: React.FC<HealthMetricMicroChartProps> = ({
  metricId,
  limit = 10
}) => {
  // Use the store to get and fetch history data
  const { metricHistoryById, isHistoryLoading, fetchMetricHistory } = useHealthMetricsStore();

  // Track if this component has loaded data
  const [hasLoaded, setHasLoaded] = useState(false);

  // Fetch historical data when the component mounts or when metricId/limit changes
  useEffect(() => {
    // Only fetch if this is a different metric than what's already in the store
    // This prevents unnecessary API calls when multiple components use the same data
    const loadHistory = async () => {
      await fetchMetricHistory(metricId, limit);
      setHasLoaded(true);
    };

    // Check if we already have data for this metric
    // We need to check if the metricHistoryById object has this metricId as a key
    // This distinguishes between "not fetched yet" and "fetched but empty"
    const hasBeenFetched = Object.prototype.hasOwnProperty.call(metricHistoryById, metricId);

    if (!hasBeenFetched) {
      loadHistory();
    } else {
      setHasLoaded(true);
    }
    // The dependency array ensures this effect only runs when necessary
  }, [metricId, limit, fetchMetricHistory, metricHistoryById]);

  // Memoize and reverse the history data for the chart
  // This ensures we only process the data when it actually changes
  const chartData = useMemo(() => {
    // Get the history for this specific metric
    const metricHistory = metricHistoryById[metricId] || [];
    // Only use the first 'limit' items from the history
    const limitedHistory = metricHistory.slice(0, limit);
    // Reverse to show oldest to newest (left to right)
    return [...limitedHistory].reverse();
  }, [metricHistoryById, metricId, limit]);

  // Show loading state while fetching data
  if (isHistoryLoading && !hasLoaded) {
    return <LoadingState />;
  }

  // Show empty state if there's no historical data
  if (chartData.length === 0) {
    return <EmptyState />;
  }

  // Render the chart with the historical data
  return (
    <div className="h-full w-full flex items-end">
      <BarChartRenderer history={chartData} />
    </div>
  );
};
