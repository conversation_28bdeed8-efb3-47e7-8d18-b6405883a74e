import React from 'react';
import { TeamHealthMetricValue } from '../../types/healthMetrics.types';

interface BarChartRendererProps {
  history: TeamHealthMetricValue[];
}

export const BarChartRenderer: React.FC<BarChartRendererProps> = ({ history }) => {
  // Safety check for history
  if (!history || !Array.isArray(history) || history.length === 0) {
    return null;
  }

  try {
    // Calculate the min and max values for scaling
    const safeValues = history.map(item => {
      return typeof item.value === 'number' ? item.value : 0;
    });

    const minValue = safeValues.length > 0 ? Math.min(...safeValues) : 0;
    const maxValue = safeValues.length > 0 ? Math.max(...safeValues) : 0;
    const range = maxValue - minValue || 1; // Avoid division by zero
    // Limit to 10 most recent items and calculate bar width
    const limitedHistory = history.slice(-10);
    const barWidth = 100 / (limitedHistory.length || 1);

    return (
      <>
        {limitedHistory.map((item, index) => {
          // Handle undefined or null values
          const value = typeof item.value === 'number' ? item.value : 0;

          // Normalize the value to a percentage height (10% minimum height for visibility)
          const normalizedHeight = 10 + ((value - minValue) / range) * 90;

          // Determine the color based on status
          const barColor =
            item.status === 'green' ? 'bg-green-500' :
            item.status === 'yellow' ? 'bg-yellow-500' :
            item.status === 'red' ? 'bg-red-500' :
            'bg-gray-300';

          // Format the date safely
          const dateStr = item.reported_at ? new Date(item.reported_at).toLocaleDateString() : 'Unknown date';

          return (
            <div
              key={`chart-bar-${index}`}
              className={`${barColor} rounded-t-sm mx-px`}
              style={{
                height: `${normalizedHeight}%`,
                width: `${barWidth}%`,
                maxWidth: '8px',
                transition: 'height 0.3s ease'
              }}
              title={`${value} - ${dateStr}`}
            />
          );
        })}
      </>
    );
  } catch (error) {
    console.error('Error rendering chart:', error);
    return null;
  }
};
