import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TeamHealthMetricWithValues } from '../../types/healthMetrics.types';
import { Badge } from '@/components/ui/badge';

// Define the form schema with Zod
const formSchema = z.object({
  value: z.coerce.number({
    required_error: "Value is required",
    invalid_type_error: "Value must be a number",
  }),
  notes: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface HealthMetricsCheckInProps {
  metric: TeamHealthMetricWithValues;
  onSubmit: (metricId: string, value: number, notes?: string) => Promise<void>;
  onCancel: () => void;
  isSubmitting: boolean;
}

export const HealthMetricsCheckIn: React.FC<HealthMetricsCheckInProps> = ({
  metric,
  onSubmit,
  onCancel,
  isSubmitting,
}) => {
  const [predictedStatus, setPredictedStatus] = useState<string | null>(null);

  // Initialize the form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      value: metric.latest_value?.value || 0,
      notes: '',
    },
  });

  // Predict the status based on the entered value
  const predictStatus = (value: number): string => {
    const { green_threshold, yellow_threshold } = metric;

    // Higher is better (green > yellow > red)
    if (green_threshold > yellow_threshold) {
      if (value >= green_threshold) return 'green';
      if (value >= yellow_threshold) return 'yellow';
      return 'red';
    }
    // Lower is better (green < yellow < red)
    else {
      if (value <= green_threshold) return 'green';
      if (value <= yellow_threshold) return 'yellow';
      return 'red';
    }
  };

  // Update predicted status when value changes
  React.useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'value' && value.value !== undefined) {
        setPredictedStatus(predictStatus(Number(value.value)));
      }
    });

    // Set initial predicted status
    const currentValue = form.getValues('value');
    if (currentValue !== undefined) {
      setPredictedStatus(predictStatus(Number(currentValue)));
    }

    return () => subscription.unsubscribe();
  }, [form, metric, predictStatus]);

  const handleSubmit = async (values: FormValues) => {
    await onSubmit(metric.id, values.value, values.notes);
  };

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case 'green':
        return 'bg-green-100 text-green-800';
      case 'yellow':
        return 'bg-yellow-100 text-yellow-800';
      case 'red':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string | null) => {
    switch (status) {
      case 'green':
        return 'Healthy';
      case 'yellow':
        return 'Warning';
      case 'red':
        return 'Critical';
      default:
        return 'Unknown';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Check-in: {metric.name}</CardTitle>
        <CardDescription>
          Record the current value for this health metric
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="value"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Current Value</FormLabel>
                  <div className="flex items-center space-x-4">
                    <FormControl>
                      <Input
                        type="number"
                        step="any"
                        {...field}
                        className="w-full"
                      />
                    </FormControl>
                    {metric.unit && (
                      <span className="text-sm text-gray-500">{metric.unit}</span>
                    )}
                    {predictedStatus && (
                      <Badge className={getStatusColor(predictedStatus)}>
                        {getStatusText(predictedStatus)}
                      </Badge>
                    )}
                  </div>
                  <FormDescription>
                    Enter the current value for this metric
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-3 gap-4 py-2">
              <div className="text-center">
                <div className="text-xs text-gray-500">Green</div>
                <div className="font-medium">{metric.green_threshold}{metric.unit}</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-gray-500">Yellow</div>
                <div className="font-medium">{metric.yellow_threshold}{metric.unit}</div>
              </div>
              <div className="text-center">
                <div className="text-xs text-gray-500">Red</div>
                <div className="font-medium">{metric.red_threshold}{metric.unit}</div>
              </div>
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any context or notes about this check-in..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Provide any additional context about this measurement
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {predictedStatus === 'red' && metric.soe && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <h4 className="font-medium text-red-800 mb-2">Standard Operating Procedure:</h4>
                <p className="text-sm text-red-700">{metric.soe}</p>
              </div>
            )}

            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit Check-in'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
