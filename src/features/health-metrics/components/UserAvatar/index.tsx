import React, { useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useUserStore } from '@/store/user.store';
import type { UserProfile } from '@/store/user.store';

// Using the UserProfile type from the store

interface UserAvatarProps {
  userId: string | null;
  size?: 'sm' | 'md' | 'lg';
  showName?: boolean;
}

export const UserAvatar: React.FC<UserAvatarProps> = ({
  userId,
  size = 'sm',
  showName = true
}) => {
  // Use the user store to get profile data
  const { getProfile, isLoading } = useUserStore();
  const [profile, setProfile] = React.useState<UserProfile | null>(null);

  // Fetch the profile from the store
  useEffect(() => {
    if (!userId) return;

    // Use the store's getProfile method which handles caching
    getProfile(userId).then(profileData => {
      if (profileData) {
        setProfile(profileData);
      }
    });
  }, [userId, getProfile]);

  if (!userId) {
    return <span className="text-gray-500">-</span>;
  }

  // Size classes
  const avatarSizeClass =
    size === 'sm' ? 'h-6 w-6' :
    size === 'md' ? 'h-8 w-8' :
    'h-10 w-10';

  const textSizeClass =
    size === 'sm' ? 'text-xs' :
    size === 'md' ? 'text-sm' :
    'text-base';

  // Display name
  const displayName = profile?.full_name ||
                     (profile?.username ? profile.username :
                      profile?.email ? profile.email.split('@')[0] : 'Unknown User');

  // Initials for fallback
  const initials = displayName
    ? displayName
        .split(' ')
        .map((n: string) => n[0])
        .join('')
        .toUpperCase()
        .substring(0, 2)
    : '??';

  return (
    <div className="flex items-center gap-2">
      <Avatar className={avatarSizeClass}>
        <AvatarImage
          src={profile?.avatar_url || `https://api.dicebear.com/7.x/initials/svg?seed=${userId}`}
          alt={displayName}
          isLoading={isLoading}
        />
        <AvatarFallback className="bg-primary/10 text-primary">
          {initials || '?'}
        </AvatarFallback>
      </Avatar>
      {showName && (
        <span className={`${textSizeClass} font-medium`}>
          {isLoading ? 'Loading...' : displayName}
        </span>
      )}
    </div>
  );
};

export default UserAvatar;
