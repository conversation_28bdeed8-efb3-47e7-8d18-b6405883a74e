import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';

interface HealthMetricsEmptyStateProps {
  onCreateMetric: (e: React.MouseEvent) => void;
}

export const HealthMetricsEmptyState: React.FC<HealthMetricsEmptyStateProps> = ({
  onCreateMetric
}) => {
  return (
    <div className="flex flex-col justify-center items-center h-24 border rounded-lg p-4 bg-gray-50">
      <p className="text-gray-500 mb-2">No health metrics defined</p>
      <Button
        onClick={onCreateMetric}
        variant="outline"
        size="sm"
        aria-label="Create your first health metric"
      >
        <PlusCircle className="mr-2 h-4 w-4" aria-hidden="true" />
        Create your first metric
      </Button>
    </div>
  );
};
