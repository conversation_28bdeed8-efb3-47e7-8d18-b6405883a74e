/**
 * TeamHealthMetricsSummary Component
 *
 * This component displays a summary of health metrics for a team. It includes:
 * - A collapsible header with overall status
 * - A carousel of health metric cards when expanded
 * - Action buttons for creating and managing metrics
 * - Loading and empty states
 *
 * The component follows a modular architecture with smaller, focused sub-components
 * to improve maintainability and separation of concerns.
 */

import React, { useEffect, useState, useCallback } from 'react';
import '../HealthMetricCard/styles.css';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle, AlertCircle, CheckCircle2 } from 'lucide-react';
import { useHealthMetricsStore } from '../../store/healthMetricsStore';
import { HealthMetricStatus } from '../../types/healthMetrics.types';
import { useTeamMetrics, useOverallHealthStatus } from '../../hooks/useHealthMetricsSelectors';

// Import sub-components
import { HealthMetricsHeader } from './HealthMetricsHeader';
import { HealthMetricsEmptyState } from './HealthMetricsEmptyState';
import { HealthMetricsCarousel } from './HealthMetricsCarousel';
import { HealthMetricsActionButtons } from './HealthMetricsActionButtons';
import { HealthMetricsLoadingState } from './HealthMetricsLoadingState';

interface TeamHealthMetricsSummaryProps {
  teamId: string;
}

/**
 * TeamHealthMetricsSummary Component
 *
 * @param teamId - The ID of the team to display health metrics for
 */
export const TeamHealthMetricsSummary: React.FC<TeamHealthMetricsSummaryProps> = ({
  teamId,
}) => {
  const navigate = useNavigate();
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHoveringCard, setIsHoveringCard] = useState(false);

  // Use optimized selectors to get only the data we need
  // These custom hooks handle memoization and prevent unnecessary re-renders
  const { metrics, isLoading } = useTeamMetrics(teamId);
  const overallStatus = useOverallHealthStatus(teamId);

  // Access only the required store actions to minimize re-renders
  // This approach ensures components only re-render when the specific
  // parts of the store they depend on change
  const { fetchMetrics, setFilters } = useHealthMetricsStore(
    (state) => ({
      fetchMetrics: state.fetchMetrics,
      setFilters: state.setFilters
    })
  );

  // Fetch metrics when the component mounts or teamId changes, but only if they haven't been fetched yet
  useEffect(() => {
    // Set filters to only show active metrics
    setFilters({ is_active: true, is_archived: false, team_id: teamId });

    // Check if metrics for this team are already in the store
    // We need to check if the teamMetrics object has this teamId as a key
    // This distinguishes between "not fetched yet" and "fetched but empty"
    const teamMetricsState = useHealthMetricsStore.getState().teamMetrics;
    const hasBeenFetched = Object.prototype.hasOwnProperty.call(teamMetricsState, teamId);

    // Only fetch metrics if they haven't been fetched yet
    if (!hasBeenFetched) {
      fetchMetrics(teamId);
    }

    // The dependency array ensures this effect only runs when necessary
  }, [teamId, fetchMetrics, setFilters]);

  // Memoize navigation handlers
  const handleCreateMetric = useCallback(() => {
    navigate(`/teams/${teamId}/health-metrics/new`);
  }, [navigate, teamId]);

  const handleCheckIn = useCallback((metricId: string) => {
    navigate(`/teams/${teamId}/health-metrics/${metricId}/check-in`);
  }, [navigate, teamId]);

  const handleSettings = useCallback((metricId: string) => {
    navigate(`/teams/${teamId}/health-metrics/${metricId}`);
  }, [navigate, teamId]);

  const handleViewAll = useCallback(() => {
    navigate(`/teams/${teamId}/health-metrics`);
  }, [navigate, teamId]);

  const toggleExpanded = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  // All metrics will be displayed in the carousel if there are more than 3

  // Memoize the status icon function
  const getStatusIcon = useCallback((status: HealthMetricStatus | null) => {
    switch (status) {
      case 'green':
        return <CheckCircle2 className="h-5 w-5 text-green-600" />;
      case 'yellow':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'red':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return null;
    }
  }, []);

  // Custom status text for overall status (different from individual metrics)
  const getOverallStatusText = useCallback((status: HealthMetricStatus | null) => {
    switch (status) {
      case 'green':
        return 'All metrics healthy';
      case 'yellow':
        return 'Warning: Some metrics need attention';
      case 'red':
        return 'Critical: Immediate action required';
      default:
        return 'No metrics data available';
    }
  }, []);

  // Handle view all button click
  const handleViewAllClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    handleViewAll();
  }, [handleViewAll]);

  // Handle check-in button click
  const handleCheckInClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/teams/${teamId}/health-metrics/check-in`);
  }, [navigate, teamId]);

  // Handle create metric button click
  const handleCreateMetricClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    handleCreateMetric();
  }, [handleCreateMetric]);

  // Render action buttons
  const renderActionButtons = useCallback(() => {
    return (
      <HealthMetricsActionButtons
        isHoveringCard={isHoveringCard}
        onViewAll={handleViewAllClick}
        onCheckIn={handleCheckInClick}
        onCreateMetric={handleCreateMetricClick}
      />
    );
  }, [isHoveringCard, handleViewAllClick, handleCheckInClick, handleCreateMetricClick]);

  return (
    <Card className="health-metrics-container">
      <HealthMetricsHeader
        isExpanded={isExpanded}
        toggleExpanded={toggleExpanded}
        overallStatus={overallStatus}
        hasMetrics={metrics.length > 0}
        isHoveringCard={isHoveringCard}
        getStatusIcon={getStatusIcon}
        getOverallStatusText={getOverallStatusText}
        onViewAll={handleViewAllClick}
        renderActionButtons={renderActionButtons}
      />

      {isExpanded && (
        <CardContent id="health-metrics-content">
          {isLoading ? (
            <HealthMetricsLoadingState />
          ) : metrics.length === 0 ? (
            <HealthMetricsEmptyState onCreateMetric={handleCreateMetricClick} />
          ) : (
            <HealthMetricsCarousel
              metrics={metrics}
              onCheckIn={handleCheckIn}
              onSettings={handleSettings}
              onMouseEnter={() => setIsHoveringCard(true)}
              onMouseLeave={() => setIsHoveringCard(false)}
            />
          )}
        </CardContent>
      )}
    </Card>
  );
};
