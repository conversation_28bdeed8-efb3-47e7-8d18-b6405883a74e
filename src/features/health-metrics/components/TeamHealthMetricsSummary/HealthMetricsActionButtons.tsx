import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { CheckCircle, PlusCircle } from 'lucide-react';

interface HealthMetricsActionButtonsProps {
  isHoveringCard: boolean;
  onViewAll: (e: React.MouseEvent) => void;
  onCheckIn: (e: React.MouseEvent) => void;
  onCreateMetric: (e: React.MouseEvent) => void;
}

export const HealthMetricsActionButtons: React.FC<HealthMetricsActionButtonsProps> = ({
  isHoveringCard,
  onViewAll,
  onCheckIn,
  onCreateMetric
}) => {
  return (
    <div className={`flex space-x-2 ${isHoveringCard ? 'opacity-0' : 'opacity-100'} transition-opacity outer-actions`}>
      <Button
        variant="outline"
        size="sm"
        onClick={onViewAll}
        aria-label="View all health metrics"
      >
        View All
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={onCheckIn}
        aria-label="Check-in for health metrics"
      >
        <CheckCircle className="mr-2 h-4 w-4" aria-hidden="true" />
        Check-in
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={onCreateMetric}
        aria-label="Create new health metric"
      >
        <PlusCircle className="mr-2 h-4 w-4" aria-hidden="true" />
        New
      </Button>
    </div>
  );
};
