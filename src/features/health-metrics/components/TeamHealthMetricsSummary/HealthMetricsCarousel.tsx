import React from 'react';
import { Carousel } from '@/components/ui/carousel';
import { HealthMetricCard } from '../HealthMetricCard';
import { TeamHealthMetricWithValues } from '../../types/healthMetrics.types';

interface HealthMetricsCarouselProps {
  metrics: TeamHealthMetricWithValues[];
  onCheckIn: (metricId: string) => void;
  onSettings: (metricId: string) => void;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

export const HealthMetricsCarousel: React.FC<HealthMetricsCarouselProps> = ({
  metrics,
  onCheckIn,
  onSettings,
  onMouseEnter,
  onMouseLeave
}) => {
  return (
    <Carousel
      items={metrics}
      itemsPerPage={3}
      renderItem={(metric) => (
        <div
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
        >
          <HealthMetricCard
            key={metric.id}
            metric={metric}
            onCheckIn={onCheckIn}
            onSettings={onSettings}
          />
        </div>
      )}
    />
  );
};
