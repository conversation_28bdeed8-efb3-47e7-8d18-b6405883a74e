import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { HealthMetricStatus } from '../../types/healthMetrics.types';
import { getStatusColor } from '../../hooks/useHealthMetricsSelectors';

interface HealthMetricsHeaderProps {
  isExpanded: boolean;
  toggleExpanded: () => void;
  overallStatus: HealthMetricStatus | null;
  hasMetrics: boolean;
  isHoveringCard: boolean;
  getStatusIcon: (status: HealthMetricStatus | null) => React.ReactNode;
  getOverallStatusText: (status: HealthMetricStatus | null) => string;
  onViewAll: (e: React.MouseEvent) => void;
  renderActionButtons: () => React.ReactNode;
}

export const HealthMetricsHeader: React.FC<HealthMetricsHeaderProps> = ({
  isExpanded,
  toggleExpanded,
  overallStatus,
  hasMetrics,
  // @ts-expect-error - isHoveringCard is not currently used but kept for future hover effects
  _isHoveringCard,
  getStatusIcon,
  getOverallStatusText,
  onViewAll,
  renderActionButtons
}) => {
  return (
    <CardHeader
      className="pb-2 cursor-pointer"
      onClick={toggleExpanded}
      role="button"
      aria-expanded={isExpanded}
      aria-controls="health-metrics-content"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          toggleExpanded();
        }
      }}
    >
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          {isExpanded ?
            <ChevronDown className="h-5 w-5 text-gray-500" aria-hidden="true" /> :
            <ChevronRight className="h-5 w-5 text-gray-500" aria-hidden="true" />
          }
          <CardTitle className="text-lg font-medium">Health Metrics</CardTitle>
          {!isExpanded && hasMetrics && (
            <div className="flex items-center space-x-2 ml-2">
              {getStatusIcon(overallStatus)}
              <Badge
                className={getStatusColor(overallStatus)}
                aria-label={`Overall status: ${getOverallStatusText(overallStatus)}`}
              >
                {getOverallStatusText(overallStatus)}
              </Badge>
            </div>
          )}
        </div>
        {!isExpanded && (
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => onViewAll(e)}
            className="text-gray-500 hover:text-gray-700"
            aria-label="View all health metrics"
          >
            View All
          </Button>
        )}
        {isExpanded && renderActionButtons()}
      </div>
    </CardHeader>
  );
};
