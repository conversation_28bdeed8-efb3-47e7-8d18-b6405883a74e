import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useHealthMetricsStore } from '../../store/healthMetricsStore';
import { HealthMetricsList } from '../HealthMetricsList';
import { HealthMetricForm } from '../HealthMetricForm';
import { HealthMetricsCheckIn } from '../HealthMetricsCheckIn';
import { HealthMetricsInsights, HealthMetricsInsightsButton } from '../HealthMetricsInsights';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CreateHealthMetricParams, UpdateHealthMetricParams } from '../../types/healthMetrics.types';
import { ArrowLeft } from 'lucide-react';

export const HealthMetricsPage: React.FC = () => {
  const params = useParams<{ teamId: string }>();
  const teamId = params.teamId;
  const navigate = useNavigate();
  const { toast } = useToast();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isCheckInDialogOpen, setIsCheckInDialogOpen] = useState(false);
  const [isInsightsOpen, setIsInsightsOpen] = useState(false);
  const [_selectedMetricId, setSelectedMetricId] = useState<string | null>(null);

  const {
    selectedMetric,
    isLoading,
    isCreating,
    // isUpdating, // Unused but available in store
    isCheckinIn,
    error,
    filters,
    fetchMetrics,
    fetchMetricById,
    createMetric,
    checkInMetric,
    setFilters,
    getTeamMetrics,
    clearSelectedMetric,
    clearError
  } = useHealthMetricsStore();

  // Get team-specific metrics instead of using the global metrics array
  const metrics = teamId ? getTeamMetrics(teamId) : [];

  useEffect(() => {
    if (!teamId) {
      return;
    }

    // Set filters to only show active metrics by default
    setFilters({ is_active: true, is_archived: false, team_id: teamId });

    // Fetch metrics for this team
    fetchMetrics(teamId);

    // Cleanup when component unmounts
    return () => {
      clearSelectedMetric();
    };
  }, [teamId, fetchMetrics, setFilters, clearSelectedMetric]);

  useEffect(() => {
    // Show error toast if there's an error
    if (error) {
      toast({
        title: 'Error',
        description: error,
        variant: 'destructive',
      });
      clearError();
    }
  }, [error, toast, clearError]);

  const handleCreateMetric = async (values: CreateHealthMetricParams | UpdateHealthMetricParams) => {
    if (!teamId) return;

    try {
      // Check if it's a CreateHealthMetricParams
      if ('team_id' in values) {
        await createMetric(values as CreateHealthMetricParams);
      } else {
        // It's an UpdateHealthMetricParams, but we're creating a new metric
        await createMetric({
          name: values.name || '',
          description: values.description,
          green_threshold: values.green_threshold || 0,
          yellow_threshold: values.yellow_threshold || 0,
          red_threshold: values.red_threshold || 0,
          unit: values.unit,
          soe: values.soe,
          team_id: teamId,
        });
      }

      setIsCreateDialogOpen(false);
      toast({
        title: 'Success',
        description: 'Health metric created successfully',
      });
    } catch (error) {
      console.error('Error creating health metric:', error);
    }
  };

  const handleCheckIn = async (metricId: string) => {
    setSelectedMetricId(metricId);
    await fetchMetricById(metricId);
    setIsCheckInDialogOpen(true);
  };

  const handleCheckInSubmit = async (metricId: string, value: number, notes?: string) => {
    try {
      await checkInMetric({
        team_health_metric_id: metricId,
        value,
        notes,
      });

      setIsCheckInDialogOpen(false);
      toast({
        title: 'Success',
        description: 'Health metric checked in successfully',
      });
    } catch (error) {
      console.error('Error checking in health metric:', error);
    }
  };

  const handleSettings = (metricId: string) => {
    if (!teamId) return;
    navigate(`/teams/${teamId}/health-metrics/${metricId}`);
  };

  const handleFilterChange = (filters: { is_active?: boolean; is_archived?: boolean }) => {
    setFilters(filters);
    if (teamId) {
      fetchMetrics(teamId);
    }
  };

  const handleBackToTeam = () => {
    if (teamId) {
      navigate(`/team/${teamId}`);
    } else {
      navigate('/teams');
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <Button variant="outline" onClick={handleBackToTeam}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Team
        </Button>
        <HealthMetricsInsightsButton onClick={() => setIsInsightsOpen(true)} />
      </div>
      <HealthMetricsList
        metrics={metrics}
        isLoading={isLoading}
        filters={filters}
        onCreateMetric={() => setIsCreateDialogOpen(true)}
        onCheckIn={handleCheckIn}
        onSettings={handleSettings}
        onFilterChange={handleFilterChange}
      />

      {/* Create Metric Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Create New Health Metric</DialogTitle>
          </DialogHeader>
          {teamId && (
            <HealthMetricForm
              teamId={teamId}
              onSubmit={handleCreateMetric}
              onCancel={() => setIsCreateDialogOpen(false)}
              isSubmitting={isCreating}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Check-in Dialog */}
      <Dialog open={isCheckInDialogOpen} onOpenChange={setIsCheckInDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Health Metric Check-in</DialogTitle>
          </DialogHeader>
          {selectedMetric && (
            <HealthMetricsCheckIn
              metric={selectedMetric}
              onSubmit={(metricId, value, notes) => handleCheckInSubmit(metricId, value, notes)}
              onCancel={() => setIsCheckInDialogOpen(false)}
              isSubmitting={isCheckinIn}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Insights Dialog */}
      <HealthMetricsInsights
        isOpen={isInsightsOpen}
        onClose={() => setIsInsightsOpen(false)}
      />
    </div>
  );
};
