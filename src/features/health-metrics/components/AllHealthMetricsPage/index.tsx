import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, ArrowLeft } from 'lucide-react';
import { useTeamsStore } from '@/features/teams/store/teamsStore';
import { useHealthMetricsStore } from '../../store/healthMetricsStore';
import { Team } from '@/features/teams/types';
import { TeamHealthMetricsSummary } from '../TeamHealthMetricsSummary';
import { HealthMetricsInsights, HealthMetricsInsightsButton } from '../HealthMetricsInsights';

// Helper function to sort teams by health status
const sortTeamsByHealthStatus = (teams: Team[]): Team[] => {
  return [...teams].sort((a, b) => {
    // Get overall health status for each team
    const healthStore = useHealthMetricsStore.getState();
    const teamMetricsA = healthStore.teamMetrics[a.id] || [];
    const teamMetricsB = healthStore.teamMetrics[b.id] || [];

    const statusA = teamMetricsA.some(m => m.latest_value?.status === 'red') ? 'red' :
                  teamMetricsA.some(m => m.latest_value?.status === 'yellow') ? 'yellow' :
                  teamMetricsA.every(m => m.latest_value?.status === 'green') && teamMetricsA.length > 0 ? 'green' : 'none';

    const statusB = teamMetricsB.some(m => m.latest_value?.status === 'red') ? 'red' :
                  teamMetricsB.some(m => m.latest_value?.status === 'yellow') ? 'yellow' :
                  teamMetricsB.every(m => m.latest_value?.status === 'green') && teamMetricsB.length > 0 ? 'green' : 'none';

    // Define priority order: red (0), yellow (1), green (2), none (3)
    const priorityMap: Record<string, number> = { 'red': 0, 'yellow': 1, 'green': 2, 'none': 3 };

    return priorityMap[statusA] - priorityMap[statusB];
  });
};

export const AllHealthMetricsPage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { teams, loading, error, fetchTeams } = useTeamsStore();
  const [isInsightsOpen, setIsInsightsOpen] = useState(false);

  useEffect(() => {
    const loadTeams = async () => {
      try {
        await fetchTeams();
      } catch (_err) {
        toast({
          title: 'Error',
          description: 'Failed to load teams. Please try again.',
          variant: 'destructive',
        });
      }
    };

    loadTeams();
  }, [fetchTeams, toast]);

  // Load health metrics for all teams when the page loads
  const [isMetricsLoading, setIsMetricsLoading] = useState(false);

  useEffect(() => {
    const fetchAllTeamMetrics = async () => {
      if (!loading && !error && teams.length > 0) {
        setIsMetricsLoading(true);

        try {
          // Create a single batch operation to fetch metrics for all teams
          const healthStore = useHealthMetricsStore.getState();

          // Set filters to only show active metrics
          healthStore.setFilters({ is_active: true, is_archived: false });

          // Get teams that don't have metrics in the store yet
          // We need to check if the teamMetrics object has this teamId as a key
          // This distinguishes between "not fetched yet" and "fetched but empty"
          const teamsNeedingMetrics = teams.filter(team => {
            return !Object.prototype.hasOwnProperty.call(healthStore.teamMetrics, team.id);
          });

          if (teamsNeedingMetrics.length > 0) {
            // Use Promise.all to fetch metrics for teams that need them in parallel
            await Promise.all(
              teamsNeedingMetrics.map(team => healthStore.fetchMetrics(team.id))
            );
          }
        } catch (err) {
          console.error('Error fetching health metrics:', err);
          toast({
            title: 'Error',
            description: 'Failed to load some health metrics.',
            variant: 'destructive',
          });
        } finally {
          setIsMetricsLoading(false);
        }
      }
    };

    fetchAllTeamMetrics();
  }, [teams, loading, error, toast]);

  if (loading || isMetricsLoading) {
    return (
      <div className="container mx-auto py-6 flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64">
            <p className="text-destructive mb-4">Failed to load teams</p>
            <Button onClick={() => fetchTeams()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleBackToTeams = () => {
    navigate('/teams');
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Button variant="outline" onClick={handleBackToTeams}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Teams
        </Button>
      </div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Health Metrics</h1>
        <HealthMetricsInsightsButton onClick={() => setIsInsightsOpen(true)} />
      </div>

      {/* Insights Dialog */}
      <HealthMetricsInsights
        isOpen={isInsightsOpen}
        onClose={() => setIsInsightsOpen(false)}
      />

      {teams.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64">
            <p className="text-gray-500 mb-4">No teams found</p>
            <Button onClick={() => navigate('/teams')}>Go to Teams</Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-8">
          {/* Sort teams by health status: red first, then yellow, then green, then no data */}
          {sortTeamsByHealthStatus(teams).map((team) => (
            <Card key={team.id} className="overflow-hidden">
              <CardHeader className="bg-muted/50">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-xl">{team.name}</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-0 overflow-hidden">
                <div className="w-full">
                  <TeamHealthMetricsSummary teamId={team.id} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
