import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { TeamHealthMetric, CreateHealthMetricParams, UpdateHealthMetricParams } from '../../types/healthMetrics.types';

// Define the form schema with Zod
const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  green_threshold: z.coerce.number(),
  yellow_threshold: z.coerce.number(),
  red_threshold: z.coerce.number(),
  unit: z.string().optional(),
  soe: z.string().optional(),
}).refine((data) => {
  // Custom validation to ensure thresholds make sense
  // This assumes higher values are better (green > yellow > red)
  if (data.green_threshold > data.yellow_threshold && data.yellow_threshold > data.red_threshold) {
    return true;
  }
  // Or lower values are better (green < yellow < red)
  if (data.green_threshold < data.yellow_threshold && data.yellow_threshold < data.red_threshold) {
    return true;
  }
  return false;
}, {
  message: "Thresholds must be in order (either green > yellow > red or green < yellow < red)",
  path: ["yellow_threshold"],
});

type FormValues = z.infer<typeof formSchema>;

interface HealthMetricFormProps {
  teamId: string;
  metric?: TeamHealthMetric;
  onSubmit: (values: CreateHealthMetricParams | UpdateHealthMetricParams) => void;
  onCancel: () => void;
  isSubmitting: boolean;
}

export const HealthMetricForm: React.FC<HealthMetricFormProps> = ({
  teamId,
  metric,
  onSubmit,
  onCancel,
  isSubmitting,
}) => {
  // Initialize the form with default values or existing metric values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: metric?.name || '',
      description: metric?.description || '',
      green_threshold: metric?.green_threshold || 0,
      yellow_threshold: metric?.yellow_threshold || 0,
      red_threshold: metric?.red_threshold || 0,
      unit: metric?.unit || '',
      soe: metric?.soe || '',
    },
  });

  const handleSubmit = (values: FormValues) => {
    // If metric exists, it's an update operation
    if (metric?.id) {
      onSubmit({
        ...values,
        id: metric.id
      } as UpdateHealthMetricParams);
    } else {
      // Otherwise it's a create operation
      onSubmit({
        ...values,
        team_id: teamId
      } as CreateHealthMetricParams);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Metric Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Customer Satisfaction" {...field} />
              </FormControl>
              <FormDescription>
                A clear, concise name for this health metric.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe what this metric measures and why it's important..."
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormDescription>
                Provide context about this metric and how it's measured.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="green_threshold"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Green Threshold</FormLabel>
                <FormControl>
                  <Input type="number" step="any" {...field} />
                </FormControl>
                <FormDescription>
                  Target value (healthy)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="yellow_threshold"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Yellow Threshold</FormLabel>
                <FormControl>
                  <Input type="number" step="any" {...field} />
                </FormControl>
                <FormDescription>
                  Warning value
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="red_threshold"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Red Threshold</FormLabel>
                <FormControl>
                  <Input type="number" step="any" {...field} />
                </FormControl>
                <FormDescription>
                  Critical value
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="unit"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Unit (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="e.g., %, ms, count" {...field} value={field.value || ''} />
              </FormControl>
              <FormDescription>
                The unit of measurement for this metric.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="soe"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Standard Operating Procedure (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="What should the team do when this metric is in the red zone?"
                  {...field}
                  value={field.value || ''}
                />
              </FormControl>
              <FormDescription>
                Define the steps to take when this metric reaches a critical state.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : metric ? 'Update Metric' : 'Create Metric'}
          </Button>
        </div>
      </form>
    </Form>
  );
};
