import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PlusCircle, Search, SlidersHorizontal } from 'lucide-react';
import { TeamHealthMetricWithValues } from '../../types/healthMetrics.types';
import { HealthMetricCard } from '../HealthMetricCard';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface HealthMetricsListProps {
  metrics: TeamHealthMetricWithValues[];
  isLoading: boolean;
  filters: { is_active?: boolean; is_archived?: boolean };
  onCreateMetric: () => void;
  onCheckIn: (metricId: string) => void;
  onSettings: (metricId: string) => void;
  onFilterChange: (filters: { is_active?: boolean; is_archived?: boolean }) => void;
}

export const HealthMetricsList: React.FC<HealthMetricsListProps> = ({
  metrics,
  isLoading,
  filters,
  onCreateMetric,
  onCheckIn,
  onSettings,
  onFilterChange,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  // Filter metrics based on search term and active tab
  const filteredMetrics = metrics ? metrics.filter((metric) => {
    const matchesSearch = metric.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (metric.description?.toLowerCase().includes(searchTerm.toLowerCase()) || false);

    if (activeTab === 'all') return matchesSearch;
    if (activeTab === 'green') return matchesSearch && metric.latest_value?.status === 'green';
    if (activeTab === 'yellow') return matchesSearch && metric.latest_value?.status === 'yellow';
    if (activeTab === 'red') return matchesSearch && metric.latest_value?.status === 'red';
    if (activeTab === 'no-data') return matchesSearch && !metric.latest_value;

    return matchesSearch;
  }) : [];

  const handleFilterChange = (key: 'is_active' | 'is_archived', value: boolean) => {
    onFilterChange({ [key]: value });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Health Metrics</h2>
        <Button onClick={onCreateMetric}>
          <PlusCircle className="mr-2 h-4 w-4" />
          New Metric
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search metrics..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <SlidersHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuCheckboxItem
              checked={filters.is_active !== false}
              onCheckedChange={(checked) => handleFilterChange('is_active', checked)}
            >
              Show Active
            </DropdownMenuCheckboxItem>
            <DropdownMenuCheckboxItem
              checked={filters.is_archived === true}
              onCheckedChange={(checked) => handleFilterChange('is_archived', checked)}
            >
              Show Archived
            </DropdownMenuCheckboxItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="green">Green</TabsTrigger>
          <TabsTrigger value="yellow">Yellow</TabsTrigger>
          <TabsTrigger value="red">Red</TabsTrigger>
          <TabsTrigger value="no-data">No Data</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <p>Loading metrics...</p>
            </div>
          ) : filteredMetrics.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-40 border rounded-lg p-6 bg-gray-50">
              <p className="text-gray-500 mb-4">No health metrics found</p>
              <Button onClick={onCreateMetric} variant="outline">
                <PlusCircle className="mr-2 h-4 w-4" />
                Create your first metric
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredMetrics.map((metric) => (
                <HealthMetricCard
                  key={metric.id}
                  metric={metric}
                  onCheckIn={onCheckIn}
                  onSettings={onSettings}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
