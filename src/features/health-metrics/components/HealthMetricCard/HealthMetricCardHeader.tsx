import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

interface HealthMetricCardHeaderProps {
  name: string;
}

export const HealthMetricCardHeader: React.FC<HealthMetricCardHeaderProps> = ({
  name
}) => {
  return (
    <CardHeader className="pb-2 health-metric-header">
      <div className="flex items-start">
        <CardTitle className="text-lg font-medium" title={name}>{name}</CardTitle>
      </div>
    </CardHeader>
  );
};
