import React from 'react';
import { Badge } from '@/components/ui/badge';
import { TeamHealthMetricWithValues } from '../../types/healthMetrics.types';
import { getStatusColor, getStatusText, getStatusAriaLabel } from '../../hooks/useHealthMetricsSelectors';

interface HealthMetricValueProps {
  metric: TeamHealthMetricWithValues;
  formattedValue: string;
  lastUpdatedText: string;
}

export const HealthMetricValue: React.FC<HealthMetricValueProps> = ({
  metric,
  formattedValue,
  lastUpdatedText
}) => {
  return (
    <div className="flex justify-between items-center health-metric-value">
      <div className="flex flex-col overflow-hidden">
        <span className="text-2xl font-bold truncate" title={formattedValue}>
          {formattedValue}
        </span>
        <span className="text-xs text-gray-500 whitespace-nowrap truncate max-w-[150px]" title={`Updated ${lastUpdatedText}`}>
          Updated {lastUpdatedText}
        </span>
      </div>
      <div className="flex-shrink-0 min-w-[80px] flex justify-end">
        <Badge
          className={`${getStatusColor(metric.latest_value?.status)}`}
          aria-label={getStatusAriaLabel(metric.latest_value?.status)}
        >
          {getStatusText(metric.latest_value?.status)}
        </Badge>
      </div>
    </div>
  );
};
