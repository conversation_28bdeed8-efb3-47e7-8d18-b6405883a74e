/**
 * HealthMetricCard Component
 *
 * This component displays a single health metric in a card format. It shows:
 * - The metric name and action buttons in the header
 * - The current value and status
 * - A micro chart showing historical data
 * - Threshold indicators in the footer
 *
 * The component is optimized for performance with memoization and
 * follows a modular architecture with smaller, focused sub-components.
 */

import React, { useMemo, useCallback } from 'react';
import './styles.css';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, CheckCircle } from 'lucide-react';
import { TeamHealthMetricWithValues } from '../../types/healthMetrics.types';
import { formatCompactDate } from '../../utils/dateUtils';

// Import sub-components
import { HealthMetricCardHeader } from './HealthMetricCardHeader';
import { HealthMetricValue } from './HealthMetricValue';
import { HealthMetricChart } from './HealthMetricChart';

/**
 * Props for the HealthMetricCard component
 *
 * @property metric - The health metric data to display
 * @property onCheckIn - Callback function when the check-in button is clicked
 * @property onSettings - Callback function when the settings button is clicked
 */
interface HealthMetricCardProps {
  metric: TeamHealthMetricWithValues;
  onCheckIn: (metricId: string) => void;
  onSettings: (metricId: string) => void;
}

/**
 * HealthMetricCard Component
 *
 * @param metric - The health metric data to display
 * @param onCheckIn - Callback function when the check-in button is clicked
 * @param onSettings - Callback function when the settings button is clicked
 */
export const HealthMetricCard = React.memo<HealthMetricCardProps>(({
  metric,
  onCheckIn,
  onSettings,
}) => {

  // Memoize the formatted value to prevent unnecessary recalculations
  // This ensures we don't recompute the value on every render
  const formattedValue = useMemo(() => {
    // Inline the formatting logic to avoid dependency on formatValue
    const value = metric.latest_value?.value;
    if (value === undefined) return 'No data';
    return `${value}${metric.unit ? ` ${metric.unit}` : ''}`;
  }, [metric.latest_value?.value, metric.unit]);

  // Memoize the last updated text to prevent unnecessary recalculations
  // This transforms the timestamp into a compact human-readable relative time
  const lastUpdatedText = useMemo(() => {
    return metric.latest_value?.reported_at
      ? formatCompactDate(metric.latest_value.reported_at)
      : 'Never';
  }, [metric.latest_value?.reported_at]);

  // Memoize event handlers to prevent unnecessary re-renders in child components
  // These callbacks will only be recreated if their dependencies change
  const handleCheckIn = useCallback(() => {
    onCheckIn(metric.id);
  }, [onCheckIn, metric.id]);

  const handleDetails = useCallback(() => {
    onSettings(metric.id);
  }, [onSettings, metric.id]);

  return (
    <Card className="w-full health-metric-card h-full" role="region" aria-label={`Health metric: ${metric.name}`}>
      <HealthMetricCardHeader
        name={metric.name}
      />
      <CardContent className="pb-2 flex-grow">
        <HealthMetricValue
          metric={metric}
          formattedValue={formattedValue}
          lastUpdatedText={lastUpdatedText}
        />
        <div className="flex justify-between items-center mt-3">
          <div className="w-1/2 pr-2">
            <HealthMetricChart
              metricId={metric.id}
              metricName={metric.name}
            />
          </div>
          <div className="flex space-x-1 card-actions flex-shrink-0 w-1/2 justify-end items-center h-12">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCheckIn}
              title="Check-in"
              aria-label={`Check-in for ${metric.name}`}
            >
              <CheckCircle className="h-4 w-4" aria-hidden="true" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDetails}
              title="Details"
              aria-label={`View details for ${metric.name}`}
            >
              <FileText className="h-4 w-4" aria-hidden="true" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for React.memo
  // This optimizes performance by preventing unnecessary re-renders
  // Only re-render if these specific properties have changed
  return (
    // Compare ID and name
    prevProps.metric.id === nextProps.metric.id &&
    prevProps.metric.name === nextProps.metric.name &&
    // Compare latest value properties
    prevProps.metric.latest_value?.value === nextProps.metric.latest_value?.value &&
    prevProps.metric.latest_value?.status === nextProps.metric.latest_value?.status &&
    prevProps.metric.latest_value?.reported_at === nextProps.metric.latest_value?.reported_at
    // Note: We don't compare thresholds as they rarely change and don't affect the visual display as much
  );
});
