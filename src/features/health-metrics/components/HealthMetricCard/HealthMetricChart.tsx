import React from 'react';
import { HealthMetricMicroChart } from '../HealthMetricMicroChart';

interface HealthMetricChartProps {
  metricId: string;
  metricName: string;
}

export const HealthMetricChart: React.FC<HealthMetricChartProps> = ({
  metricId,
  metricName
}) => {
  return (
    <div className="h-12" aria-label={`Trend chart for ${metricName}`} role="img">
      <HealthMetricMicroChart metricId={metricId} />
    </div>
  );
};
