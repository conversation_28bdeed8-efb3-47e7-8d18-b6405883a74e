/* Health metric card styles */
.health-metric-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Health metric header styles */
.health-metric-header {
  min-height: 50px; /* Reduced height since we removed buttons */
  display: flex;
  flex-direction: column;
}

/* Health metric value styles */
.health-metric-value {
  min-height: 50px; /* Ensure consistent value section height */
  width: 100%;
}

/* Quick actions in card content */
.health-metric-card .card-actions {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  visibility: hidden;
  display: flex;
  align-items: center;
}

.health-metric-card:hover .card-actions {
  opacity: 1;
  visibility: visible;
}

/* Chart and actions container */
.chart-actions-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.75rem;
}

/* Health metric card quick actions */
.health-metric-card .quick-actions {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  visibility: hidden; /* Hide completely when not visible */
}

.health-metric-card:hover .quick-actions {
  opacity: 1;
  visibility: visible;
}

/* Hide quick actions in collapsed state */
.health-metrics-container:not(:hover) .quick-actions {
  opacity: 0;
  visibility: hidden;
}

/* Health metrics container outer actions */
.health-metrics-container .outer-actions {
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  visibility: hidden; /* Hide completely when not visible */
}

.health-metrics-container:hover .outer-actions {
  opacity: 1;
  visibility: visible;
}

/* Hide outer actions when hovering over a card */
.health-metric-card:hover ~ .outer-actions,
.health-metric-card:hover ~ * .outer-actions {
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Responsive styles */
@media (max-width: 768px) {
  /* Always show quick actions on mobile for better touch accessibility */
  .health-metric-card .quick-actions {
    opacity: 0.8;
    visibility: visible;
  }

  /* Improve touch targets on mobile */
  .health-metric-card .quick-actions button {
    padding: 8px;
  }
}
