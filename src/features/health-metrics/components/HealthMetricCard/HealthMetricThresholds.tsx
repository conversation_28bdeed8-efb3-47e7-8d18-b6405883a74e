import React from 'react';
import { CardFooter } from '@/components/ui/card';

interface HealthMetricThresholdsProps {
  greenThreshold: number;
  yellowThreshold: number;
  redThreshold: number;
}

export const HealthMetricThresholds: React.FC<HealthMetricThresholdsProps> = ({
  greenThreshold,
  yellowThreshold,
  redThreshold
}) => {
  return (
    <CardFooter className="pt-0 mt-auto health-metric-footer">
      <div className="w-full flex justify-between items-center text-xs text-gray-500">
        <div>
          <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-1"></span>
          {greenThreshold}
        </div>
        <div>
          <span className="inline-block w-2 h-2 rounded-full bg-yellow-500 mr-1"></span>
          {yellowThreshold}
        </div>
        <div>
          <span className="inline-block w-2 h-2 rounded-full bg-red-500 mr-1"></span>
          {redThreshold}
        </div>
      </div>
    </CardFooter>
  );
};
