/**
 * Health Metrics Selectors
 *
 * This module provides custom hooks for accessing and deriving data from the health metrics store.
 * These hooks are optimized for performance with memoization to prevent unnecessary re-renders.
 */

import { useMemo } from 'react';
import { useHealthMetricsStore } from '../store/healthMetricsStore';
import { HealthMetricStatus, TeamHealthMetricWithValues } from '../types/healthMetrics.types';
import { shallow } from 'zustand/shallow';

/**
 * Custom hook for memoized team metrics data
 *
 * This hook returns team metrics data and loading state for a specific team.
 * It uses shallow comparison to prevent unnecessary re-renders when other parts
 * of the store change.
 *
 * @param teamId - The ID of the team to get metrics for
 * @returns Object containing metrics array and loading state
 */
export const useTeamMetrics = (teamId: string) => {
  // Get only the specific team metrics and loading state from the store
  const { metrics, isLoading } = useHealthMetricsStore(
    (state) => ({
      metrics: state.teamMetrics[teamId] || [],
      isLoading: state.teamLoading[teamId] || false
    }),
    shallow // Use shallow comparison to prevent unnecessary re-renders
  );

  return { metrics, isLoading };
};

/**
 * Custom hook for memoized overall health status calculation
 *
 * This hook calculates the overall health status for a team based on its metrics.
 * The status is determined by the following rules:
 * - Red if any metric is red
 * - Yellow if any metric is yellow
 * - Green if all metrics are green
 * - null if there are no metrics or no metrics have values
 *
 * @param teamId - The ID of the team to calculate status for
 * @returns The overall health status (green, yellow, red, or null)
 */
export const useOverallHealthStatus = (teamId: string) => {
  const { metrics } = useTeamMetrics(teamId);

  // Memoize the overall status calculation
  const overallStatus = useMemo(() => {
    if (metrics.length === 0) return null;

    // Check if any metric is red (critical)
    if (metrics.some(m => m.latest_value?.status === 'red')) {
      return 'red';
    }

    // Check if any metric is yellow (warning)
    if (metrics.some(m => m.latest_value?.status === 'yellow')) {
      return 'yellow';
    }

    // Check if all metrics have values and are green
    if (metrics.every(m => m.latest_value?.status === 'green')) {
      return 'green';
    }

    // Some metrics might not have values yet
    return null;
  }, [metrics]);

  return overallStatus;
};

/**
 * Custom hook for memoized filtered metrics
 */
export const useFilteredMetrics = (
  teamId: string,
  filterFn?: (metric: TeamHealthMetricWithValues) => boolean
) => {
  const { metrics, isLoading } = useTeamMetrics(teamId);

  // Memoize the filtered metrics
  const filteredMetrics = useMemo(() => {
    if (!filterFn) return metrics;
    return metrics.filter(filterFn);
  }, [metrics, filterFn]);

  return { metrics: filteredMetrics, isLoading };
};

/**
 * Get text representation of a health metric status
 */
export const getStatusText = (status: HealthMetricStatus | undefined | null) => {
  switch (status) {
    case 'green':
      return 'Healthy';
    case 'yellow':
      return 'Warning';
    case 'red':
      return 'Critical';
    default:
      return 'No data';
  }
};

/**
 * Get color class for a health metric status
 */
export const getStatusColor = (status: HealthMetricStatus | undefined | null) => {
  switch (status) {
    case 'green':
      return 'bg-green-100 text-green-800 hover:bg-green-200';
    case 'yellow':
      return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
    case 'red':
      return 'bg-red-100 text-red-800 hover:bg-red-200';
    default:
      return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
  }
};

/**
 * Get ARIA label for a health metric status
 */
export const getStatusAriaLabel = (status: HealthMetricStatus | undefined | null) => {
  switch (status) {
    case 'green':
      return 'Status: Healthy';
    case 'yellow':
      return 'Status: Warning';
    case 'red':
      return 'Status: Critical';
    default:
      return 'Status: No data available';
  }
};
