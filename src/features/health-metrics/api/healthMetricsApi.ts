import { supabase } from '@/lib/supabase';
import {
  TeamHealthMetric,
  TeamHealthMetricValue,
  TeamHealthMetricWithValues,
  CreateHealthMetricParams,
  UpdateHealthMetricParams,
  CheckInHealthMetricParams,
  HealthMetricFilters
} from '../types/healthMetrics.types';

// Fetch all health metrics for a team
export const fetchTeamHealthMetrics = async (
  teamId: string,
  filters: HealthMetricFilters = {}
): Promise<TeamHealthMetricWithValues[]> => {
  let query = supabase
    .from('team_health_metrics')
    .select('*')
    .eq('team_id', teamId);

  // Apply filters if provided
  if (filters.is_active !== undefined) {
    query = query.eq('is_active', filters.is_active);
  }

  if (filters.is_archived !== undefined) {
    query = query.eq('is_archived', filters.is_archived);
  }

  const { data: metrics, error } = await query.order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Error fetching team health metrics: ${error.message}`);
  }

  // Fetch the latest value for each metric
  const metricsWithLatestValues = await Promise.all(
    metrics.map(async (metric) => {
      const { data: values, error: valuesError } = await supabase
        .from('team_health_metric_values')
        .select('*')
        .eq('team_health_metric_id', metric.id)
        .order('reported_at', { ascending: false })
        .limit(1);

      if (valuesError) {
        console.error(`Error fetching latest value for metric ${metric.id}:`, valuesError);
        return metric as TeamHealthMetricWithValues;
      }

      return {
        ...metric,
        latest_value: values && values.length > 0 ? values[0] : undefined
      } as TeamHealthMetricWithValues;
    })
  );

  return metricsWithLatestValues;
};

// Fetch a single health metric by ID
export const fetchHealthMetricById = async (
  metricId: string
): Promise<TeamHealthMetricWithValues | null> => {
  const { data: metric, error } = await supabase
    .from('team_health_metrics')
    .select('*')
    .eq('id', metricId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // PGRST116 is the error code for "no rows returned"
      return null;
    }
    throw new Error(`Error fetching health metric: ${error.message}`);
  }

  // Fetch the latest value
  const { data: values, error: valuesError } = await supabase
    .from('team_health_metric_values')
    .select('*')
    .eq('team_health_metric_id', metricId)
    .order('reported_at', { ascending: false })
    .limit(1);

  if (valuesError) {
    console.error(`Error fetching latest value for metric ${metricId}:`, valuesError);
    return metric as TeamHealthMetricWithValues;
  }

  return {
    ...metric,
    latest_value: values && values.length > 0 ? values[0] : undefined
  } as TeamHealthMetricWithValues;
};

// Fetch historical values for a health metric
export const fetchHealthMetricHistory = async (
  metricId: string,
  limit: number = 10
): Promise<TeamHealthMetricValue[]> => {
  const { data, error } = await supabase
    .from('team_health_metric_values')
    .select('*')
    .eq('team_health_metric_id', metricId)
    .order('reported_at', { ascending: false })
    .limit(limit);

  if (error) {
    throw new Error(`Error fetching health metric history: ${error.message}`);
  }

  return data || [];
};

// Fetch both metric details and history in a single function
export const fetchMetricDetailsWithHistory = async (
  metricId: string,
  historyLimit: number = 30
): Promise<{ metric: TeamHealthMetricWithValues | null; history: TeamHealthMetricValue[] }> => {
  try {
    // Fetch the metric details
    const { data: metric, error: metricError } = await supabase
      .from('team_health_metrics')
      .select('*')
      .eq('id', metricId)
      .single();

    if (metricError) {
      if (metricError.code === 'PGRST116') {
        // PGRST116 is the error code for "no rows returned"
        return { metric: null, history: [] };
      }
      throw new Error(`Error fetching health metric: ${metricError.message}`);
    }

    // Fetch the history data
    const { data: historyData, error: historyError } = await supabase
      .from('team_health_metric_values')
      .select('*')
      .eq('team_health_metric_id', metricId)
      .order('reported_at', { ascending: false })
      .limit(historyLimit);

    if (historyError) {
      throw new Error(`Error fetching health metric history: ${historyError.message}`);
    }

    const history = historyData || [];

    // Use the first history item as the latest value
    const latestValue = history.length > 0 ? history[0] : undefined;

    // Return both the metric and history data
    return {
      metric: {
        ...metric,
        latest_value: latestValue
      } as TeamHealthMetricWithValues,
      history
    };
  } catch (error) {
    console.error('Error in fetchMetricDetailsWithHistory:', error);
    throw error;
  }
};

// Create a new health metric
export const createHealthMetric = async (
  params: CreateHealthMetricParams
): Promise<TeamHealthMetric> => {
  const { data, error } = await supabase
    .from('team_health_metrics')
    .insert({
      team_id: params.team_id,
      name: params.name,
      description: params.description || null,
      green_threshold: params.green_threshold,
      yellow_threshold: params.yellow_threshold,
      red_threshold: params.red_threshold,
      unit: params.unit || null,
      soe: params.soe || null,
      created_by: (await supabase.auth.getUser()).data.user?.id
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Error creating health metric: ${error.message}`);
  }

  return data;
};

// Update an existing health metric
export const updateHealthMetric = async (
  params: UpdateHealthMetricParams
): Promise<TeamHealthMetric> => {
  const { data, error } = await supabase
    .from('team_health_metrics')
    .update({
      name: params.name,
      description: params.description,
      green_threshold: params.green_threshold,
      yellow_threshold: params.yellow_threshold,
      red_threshold: params.red_threshold,
      unit: params.unit,
      is_active: params.is_active,
      is_archived: params.is_archived,
      soe: params.soe,
      updated_at: new Date().toISOString()
    })
    .eq('id', params.id)
    .select()
    .single();

  if (error) {
    throw new Error(`Error updating health metric: ${error.message}`);
  }

  return data;
};

// Archive a health metric
export const archiveHealthMetric = async (
  metricId: string
): Promise<TeamHealthMetric> => {
  const { data, error } = await supabase
    .from('team_health_metrics')
    .update({
      is_archived: true,
      is_active: false,
      updated_at: new Date().toISOString()
    })
    .eq('id', metricId)
    .select()
    .single();

  if (error) {
    throw new Error(`Error archiving health metric: ${error.message}`);
  }

  return data;
};

// Toggle a health metric's active status
export const toggleHealthMetricActive = async (
  metricId: string,
  isActive: boolean
): Promise<TeamHealthMetric> => {
  const { data, error } = await supabase
    .from('team_health_metrics')
    .update({
      is_active: isActive,
      updated_at: new Date().toISOString()
    })
    .eq('id', metricId)
    .select()
    .single();

  if (error) {
    throw new Error(`Error toggling health metric active status: ${error.message}`);
  }

  return data;
};

// Check in a health metric (add a new value)
export const checkInHealthMetric = async (
  params: CheckInHealthMetricParams
): Promise<TeamHealthMetricValue> => {
  const { data, error } = await supabase
    .from('team_health_metric_values')
    .insert({
      team_health_metric_id: params.team_health_metric_id,
      value: params.value,
      notes: params.notes || null,
      reported_by: (await supabase.auth.getUser()).data.user?.id
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Error checking in health metric: ${error.message}`);
  }

  return data;
};
