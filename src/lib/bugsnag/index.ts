import React from 'react';
import Bugsnag from '@bugsnag/js';
import BugsnagPluginReact from '@bugsnag/plugin-react';
import BugsnagPerformance from '@bugsnag/browser-performance';
import { ReactRouterRoutingProvider } from '@bugsnag/react-router-performance';

const BUGSNAG_API_KEY = import.meta.env.VITE_BUGSNAG_API_KEY || '26dcf98c3f6d9ff4df3b8ea7aa29d846';
const isDevelopment = import.meta.env.DEV;
const appVersion = import.meta.env.VITE_APP_VERSION;

// Initialize Bugsnag for error tracking
Bugsnag.start({
    apiKey: BUGSNAG_API_KEY,
    plugins: [new BugsnagPluginReact()],
    releaseStage: isDevelopment ? 'development' : 'production',
    enabledReleaseStages: ['development', 'production'],
    appVersion,
    // Add additional configuration options
    maxBreadcrumbs: 40,
    autoDetectErrors: true,
    autoTrackSessions: true,
    collectUserIp: false, // GDPR compliance
    redactedKeys: [ // Add any sensitive keys you want to redact
        'password',
        'token',
        'authorization',
        'cookie',
        'secret',
    ],
});

// Initialize Bugsnag Performance monitoring with React Router integration
BugsnagPerformance.start({
    apiKey: BUGSNAG_API_KEY,
    enabledReleaseStages: ['development', 'production'],
    autoInstrumentNetworkRequests: true,
    routingProvider: new ReactRouterRoutingProvider([
        { path: '/' },
        { path: '/teams' },
        { path: '/team/:teamId' },
        { path: '/settings' },
        { path: '/team-management' },
        { path: '/auth/reset-password' },
        { path: '/auth/callback' },
        { path: '/health-metrics' },
        { path: '/teams/:teamId/health-metrics' },
        { path: '/teams/:teamId/health-metrics/:metricId' },
        { path: '/teams/:teamId/health-metrics/check-in' }
    ])
});

// Create and export the Error Boundary component
export const ErrorBoundary = Bugsnag.getPlugin('react')!.createErrorBoundary(React);

// Export a notify helper for manual error reporting
export const notifyError = (error: Error, metadata?: Record<string, unknown>) => {
    Bugsnag.notify(error, (event) => {
        if (metadata) {
            event.addMetadata('custom', metadata);
        }
    });
};

// Helper function to set user information
export const setUser = (id: string, email?: string, name?: string) => {
    Bugsnag.setUser(id, email, name);
};

// Export the Bugsnag instance for direct access if needed
export default Bugsnag;