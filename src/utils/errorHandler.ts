/**
 * Error handling utility for TeamKanban
 * 
 * This utility provides consistent error handling throughout the application
 * with the following features:
 * - Standardized error capture and reporting
 * - Environment-specific error handling
 * - Integration with logger utility
 * - Support for future error monitoring service integration
 */

import { logger } from './logger';

/**
 * Error types to categorize different errors in the application
 */
export enum ErrorType {
    API = 'API_ERROR',
    VALIDATION = 'VALIDATION_ERROR',
    AUTHENTICATION = 'AUTHENTICATION_ERROR',
    AUTHORIZATION = 'AUTHORIZATION_ERROR',
    NETWORK = 'NETWORK_ERROR',
    UNKNOWN = 'UNKNOWN_ERROR',
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
    LOW = 'LOW',
    MEDIUM = 'MEDIUM',
    HIGH = 'HIGH',
    CRITICAL = 'CRITICAL',
}

/**
 * Interface for error context details
 */
export interface ErrorContext {
    component?: string;
    action?: string;
    context?: Record<string, any>;
    userId?: string;
    teamId?: string;
    severity?: ErrorSeverity;
}

/**
 * Centralized error handler
 */
export const errorHandler = {
    /**
     * Capture an error with contextual information
     * @param error - The error object
     * @param type - Type of error
     * @param context - Additional context about where/how the error occurred
     */
    captureError: (error: unknown, type: ErrorType = ErrorType.UNKNOWN, context: ErrorContext = {}) => {
        // Ensure error is properly converted to a useful message
        let errorMessage: string;
        let errorStack: string | undefined;

        if (error instanceof Error) {
            errorMessage = error.message;
            errorStack = error.stack;
        } else if (error !== null && typeof error === 'object') {
            // Handle Supabase and other API errors that might have a message property
            if ('message' in error) {
                errorMessage = String((error as any).message);
            } else if ('error' in error && typeof (error as any).error === 'string') {
                errorMessage = (error as any).error;
            } else {
                try {
                    errorMessage = JSON.stringify(error);
                } catch {
                    errorMessage = String(error);
                }
            }
        } else {
            errorMessage = String(error);
        }

        // Create a more informative error message
        const errorDetails = {
            type,
            message: errorMessage,
            stack: errorStack,
            ...context,
        };

        // Format component and action info for more helpful error messages
        const locationInfo = context.component
            ? `${context.component}${context.action ? ` (${context.action})` : ''}`
            : 'Unknown location';

        // Log the error with the logger utility
        logger.error(`[${type}] Error in ${locationInfo}: ${errorMessage}`, {
            ...errorDetails,
            timestamp: new Date().toISOString(),
        });

        // In production, we could integrate with error monitoring services here
        // e.g., Sentry, LogRocket, etc.
        if (process.env.NODE_ENV === 'production') {
            // Production-specific error handling (placeholder for future integration)
            // Example: sendToErrorMonitoringService(errorDetails);
        }

        return {
            error,
            type,
            message: errorMessage,
        };
    },

    /**
     * Handle API errors with specific context
     * @param error - The API error
     * @param context - Additional context
     */
    handleApiError: (error: unknown, context: ErrorContext = {}) => {
        return errorHandler.captureError(error, ErrorType.API, {
            ...context,
            severity: context.severity || ErrorSeverity.MEDIUM,
        });
    },

    /**
     * Handle authentication errors
     * @param error - The authentication error
     * @param context - Additional context
     */
    handleAuthError: (error: unknown, context: ErrorContext = {}) => {
        return errorHandler.captureError(error, ErrorType.AUTHENTICATION, {
            ...context,
            severity: context.severity || ErrorSeverity.HIGH,
        });
    },

    /**
     * Format a user-friendly error message based on the error type and context
     * @param error - The error object
     * @param type - Type of error
     * @param context - Additional context
     * @returns A user-friendly error message
     */
    getUserFriendlyMessage: (error: unknown, type: ErrorType = ErrorType.UNKNOWN, __context: ErrorContext = {}): string => {
        // Default messages by error type
        const defaultMessages: Record<ErrorType, string> = {
            [ErrorType.API]: 'Failed to communicate with the server.',
            [ErrorType.VALIDATION]: 'Invalid data provided. Please check your inputs.',
            [ErrorType.AUTHENTICATION]: 'Authentication failed. Please log in again.',
            [ErrorType.AUTHORIZATION]: 'You don\'t have permission to perform this action.',
            [ErrorType.NETWORK]: 'Network error. Please check your connection.',
            [ErrorType.UNKNOWN]: 'An unexpected error occurred.',
        };

        // Use the error message if it's a simple string message
        if (typeof error === 'string') {
            return error;
        }

        // Extract message from Error object
        if (error instanceof Error) {
            // For API errors, try to extract more meaningful information
            if (type === ErrorType.API && 'response' in error) {
                const apiError = error as any;
                if (apiError.response?.data?.message) {
                    return apiError.response.data.message;
                }
                if (apiError.response?.data?.error) {
                    return apiError.response.data.error;
                }
                if (apiError.response?.status === 404) {
                    return 'The requested resource was not found.';
                }
                if (apiError.response?.status === 403) {
                    return 'You don\'t have permission to perform this action.';
                }
                if (apiError.response?.status === 401) {
                    return 'Your session has expired. Please log in again.';
                }
            }

            return error.message || defaultMessages[type];
        }

        // Fallback to default message for this error type
        return defaultMessages[type];
    }
};

export default errorHandler; 