/**
 * Logger utility for TeamKanban
 * 
 * This utility provides consistent logging throughout the application
 * with environment-specific behavior:
 * - In development: Logs are shown in the console
 * - In production: Logs are suppressed (except for errors)
 */

const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * Logger utility with environment-aware methods
 */
export const logger = {
  /**
   * Log debug information (only in development)
   * @param message - The message to log
   * @param data - Optional data to include
   */
  debug: (message: string, data?: any) => {
    if (isDevelopment) {
      if (data) {
        console.log(`[DEBUG] ${message}`, data);
      } else {
        console.log(`[DEBUG] ${message}`);
      }
    }
  },

  /**
   * Log informational messages (only in development)
   * @param message - The message to log
   * @param data - Optional data to include
   */
  info: (message: string, data?: any) => {
    if (isDevelopment) {
      if (data) {
        console.info(`[INFO] ${message}`, data);
      } else {
        console.info(`[INFO] ${message}`);
      }
    }
  },

  /**
   * Log warnings (in development, and critical ones in production)
   * @param message - The warning message
   * @param data - Optional data to include
   * @param showInProduction - Whether to show this warning in production
   */
  warn: (message: string, data?: any, showInProduction: boolean = false) => {
    if (isDevelopment || showInProduction) {
      if (data) {
        console.warn(`[WARN] ${message}`, data);
      } else {
        console.warn(`[WARN] ${message}`);
      }
    }
  },

  /**
   * Log errors (in all environments)
   * @param message - The error message
   * @param error - The error object
   */
  error: (message: string, error?: any) => {
    if (error) {
      console.error(`[ERROR] ${message}`, error);
    } else {
      console.error(`[ERROR] ${message}`);
    }

    // In a real application, you could add error reporting service integration here
    // e.g., Sentry, LogRocket, etc.
  }
}; 