import React, { Component, ErrorInfo, ReactNode } from 'react';
import { error<PERSON><PERSON><PERSON>, ErrorType } from '@/utils/errorHandler';
import { Button } from './button';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from './alert';

interface ErrorBoundaryProps {
    /**
     * Component that this error boundary wraps
     */
    children: ReactNode;

    /**
     * Component name for error reporting
     */
    componentName?: string;

    /**
     * Custom error UI renderer
     */
    fallbackRender?: (props: { error: Error; resetErrorBoundary: () => void }) => ReactNode;

    /**
     * Called when an error is caught
     */
    onError?: (error: Error, info: ErrorInfo) => void;

    /**
     * Whether to reset the error state when a child component updates
     */
    resetOnUpdate?: boolean;
}

interface ErrorBoundaryState {
    hasError: boolean;
    error: Error | null;
}

/**
 * Error Boundary component to catch JavaScript errors in children component tree,
 * log those errors, and display a fallback UI.
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        // Update state so the next render will show the fallback UI
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
        // Capture the error for logging and reporting
        errorHandler.captureError(error, ErrorType.UNKNOWN, {
            component: this.props.componentName || 'ErrorBoundary',
            action: 'render',
            context: {
                componentStack: errorInfo.componentStack,
            },
        });

        // Call the onError callback if provided
        if (this.props.onError) {
            this.props.onError(error, errorInfo);
        }
    }

    componentDidUpdate(prevProps: ErrorBoundaryProps): void {
        // Reset error state if children changed and resetOnUpdate is true
        if (
            this.state.hasError &&
            this.props.resetOnUpdate &&
            prevProps.children !== this.props.children
        ) {
            this.resetErrorBoundary();
        }
    }

    resetErrorBoundary = (): void => {
        this.setState({ hasError: false, error: null });
    };

    render(): ReactNode {
        const { hasError, error } = this.state;
        const { children, fallbackRender } = this.props;

        if (hasError && error !== null) {
            // Use custom fallback if provided
            if (fallbackRender) {
                return fallbackRender({
                    error,
                    resetErrorBoundary: this.resetErrorBoundary,
                });
            }

            // Default fallback UI
            return (
                <div className="p-4 rounded-lg border bg-background">
                    <Alert variant="destructive" className="mb-4">
                        <AlertCircle className="h-5 w-5" />
                        <AlertTitle>Something went wrong</AlertTitle>
                        <AlertDescription className="mt-2">
                            {error.message || 'An unexpected error occurred'}
                        </AlertDescription>
                    </Alert>

                    <div className="flex flex-col items-center mt-4 space-y-4">
                        <p className="text-sm text-muted-foreground text-center max-w-md">
                            The application encountered an unexpected error.
                            You can try to recover by clicking the button below.
                        </p>

                        <Button
                            onClick={this.resetErrorBoundary}
                            variant="outline"
                            className="flex items-center gap-2"
                        >
                            <RefreshCw className="h-4 w-4" />
                            Try Again
                        </Button>
                    </div>
                </div>
            );
        }

        // When there's no error, render children normally
        return children;
    }
}

/**
 * A hook-based wrapper for the ErrorBoundary component
 */
export function withErrorBoundary<P extends object>(
    Component: React.ComponentType<P>,
    errorBoundaryProps: Omit<ErrorBoundaryProps, 'children'>
): React.ComponentType<P> {
    const displayName = Component.displayName || Component.name || 'Component';

    const WrappedComponent = (props: P) => {
        return (
            <ErrorBoundary {...errorBoundaryProps} componentName={displayName}>
                <Component {...props} />
            </ErrorBoundary>
        );
    };

    WrappedComponent.displayName = `withErrorBoundary(${displayName})`;

    return WrappedComponent;
}

export default ErrorBoundary; 