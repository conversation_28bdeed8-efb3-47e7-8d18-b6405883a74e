import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { cn } from '@/lib/utils';

interface MarkdownProps {
    children: string;
    className?: string;
}

export const Markdown: React.FC<MarkdownProps> = ({ children, className }) => {
    return (
        <ReactMarkdown
            className={cn("prose prose-sm dark:prose-invert max-w-none", className)}
            remarkPlugins={[remarkGfm]}
        >
            {children}
        </ReactMarkdown>
    );
}; 