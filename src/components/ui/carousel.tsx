import React, { useState, useEffect, useCallback, useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "./button";
import { cn } from "@/lib/utils";

interface CarouselProps<T = unknown> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  itemsPerPage?: number;
  className?: string;
  containerClassName?: string;
  navigationClassName?: string;
  showNavigation?: boolean;
  autoScroll?: boolean;
  autoScrollInterval?: number; // in milliseconds
}

export function Carousel<T>({
  items,
  renderItem,
  itemsPerPage = 3,
  className,
  containerClassName,
  navigationClassName,
  showNavigation = true,
  autoScroll = false,
  autoScrollInterval = 5000,
}: CarouselProps<T>) {
  const [currentPage, setCurrentPage] = useState(0);
  const totalPages = Math.ceil(items.length / itemsPerPage);

  const goToNextPage = useCallback(() => {
    setCurrentPage((prev) => (prev === totalPages - 1 ? 0 : prev + 1));
  }, [totalPages]);

  const goToPrevPage = useCallback(() => {
    setCurrentPage((prev) => (prev === 0 ? totalPages - 1 : prev - 1));
  }, [totalPages]);

  // Auto scroll functionality
  useEffect(() => {
    if (!autoScroll || items.length <= itemsPerPage) return;

    const interval = setInterval(() => {
      goToNextPage();
    }, autoScrollInterval);

    return () => clearInterval(interval);
  }, [autoScroll, autoScrollInterval, goToNextPage, items.length, itemsPerPage]);

  // Create a ref for the carousel container
  const carouselRef = useRef<HTMLDivElement>(null);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowLeft') {
      e.preventDefault();
      goToPrevPage();
    } else if (e.key === 'ArrowRight') {
      e.preventDefault();
      goToNextPage();
    }
  };

  // If there are fewer items than itemsPerPage, just render them all
  if (items.length <= itemsPerPage) {
    return (
      <div className={cn("w-full", className)} role="region" aria-label="Content carousel">
        <div className={cn("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4", containerClassName)}>
          {items.map((item, index) => (
            <React.Fragment key={`item-${index}`}>
              {renderItem(item, index)}
            </React.Fragment>
          ))}
        </div>
      </div>
    );
  }

  // Calculate which items to display on the current page
  const startIndex = currentPage * itemsPerPage;
  const visibleItems = items.slice(startIndex, startIndex + itemsPerPage);

  return (
    <div
      className={cn("w-full", className)}
      role="region"
      aria-label="Content carousel"
      ref={carouselRef}
      tabIndex={0}
      onKeyDown={handleKeyDown}
    >
      <div className={cn("relative", containerClassName)}>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {visibleItems.map((item, index) => (
            <React.Fragment key={`visible-item-${index}`}>
              {renderItem(item, index)}
            </React.Fragment>
          ))}
        </div>

        {showNavigation && totalPages > 1 && (
          <div
            className={cn("flex justify-center items-center mt-4 space-x-4", navigationClassName)}
            aria-label="Carousel navigation"
          >
            <Button
              variant="outline"
              size="icon"
              onClick={goToPrevPage}
              className="h-10 w-10 rounded-full"
              aria-label="Previous page"
            >
              <ChevronLeft className="h-5 w-5" aria-hidden="true" />
            </Button>
            <div className="text-sm text-muted-foreground" aria-live="polite" aria-atomic="true">
              <span className="sr-only">Page</span>
              {currentPage + 1} <span aria-hidden="true">/</span> <span className="sr-only">of</span> {totalPages}
            </div>
            <Button
              variant="outline"
              size="icon"
              onClick={goToNextPage}
              className="h-10 w-10 rounded-full"
              aria-label="Next page"
            >
              <ChevronRight className="h-5 w-5" aria-hidden="true" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
