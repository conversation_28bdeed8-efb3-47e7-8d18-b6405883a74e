import { cn } from "@/lib/utils"

interface ShimmerProps extends React.HTMLAttributes<HTMLDivElement> {
    className?: string
    card?: boolean
    rounded?: boolean
}

export function Shimmer({
    className,
    card = false,
    rounded = false,
    ...props
}: ShimmerProps) {
    return (
        <div
            className={cn(
                "animate-pulse bg-muted/60 relative overflow-hidden",
                "before:absolute before:inset-0",
                "before:-translate-x-full",
                "before:animate-[shimmer_2s_infinite]",
                "before:bg-gradient-to-r",
                "before:from-transparent before:via-white/20 before:to-transparent",
                card && "p-4 space-y-4",
                rounded && "rounded-lg",
                className
            )}
            {...props}
        />
    )
}

export function ShimmerText({
    className,
    ...props
}: React.HTMLAttributes<HTMLDivElement>) {
    return (
        <div
            className={cn("h-4 w-full bg-muted rounded", className)}
            {...props}
        />
    )
}

export function ShimmerTitle({
    className,
    ...props
}: React.HTMLAttributes<HTMLDivElement>) {
    return (
        <div
            className={cn("h-6 w-1/3 bg-muted rounded", className)}
            {...props}
        />
    )
}

export function ShimmerCard({
    className,
    ...props
}: React.HTMLAttributes<HTMLDivElement>) {
    return (
        <Shimmer card rounded className={cn("w-full", className)} {...props}>
            <ShimmerTitle />
            <div className="space-y-2">
                <ShimmerText />
                <ShimmerText />
                <ShimmerText className="w-4/5" />
            </div>
        </Shimmer>
    )
} 