import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
} from "@/components/ui/command"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { User } from "@/features/teams/types/kanban"

interface UserComboboxProps {
    users: User[]
    selectedUsers: User[]
    onSelect: (users: User[]) => void
    maxUsers?: number
}

export function UserCombobox({ users, selectedUsers, onSelect, maxUsers = 5 }: UserComboboxProps) {
    const [open, setOpen] = React.useState(false)

    const handleSelect = (userId: string) => {
        const user = users.find(u => u.id === userId)
        if (!user) return

        const isSelected = selectedUsers.some(u => u.id === userId)
        let newSelectedUsers: User[]

        if (isSelected) {
            newSelectedUsers = selectedUsers.filter(u => u.id !== userId)
        } else {
            if (maxUsers === 1) {
                newSelectedUsers = [user]
            } else {
                newSelectedUsers = selectedUsers.length < maxUsers
                    ? [...selectedUsers, user]
                    : selectedUsers
            }
        }

        onSelect(newSelectedUsers)
        if (maxUsers === 1) setOpen(false)
    }

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-[200px] justify-between"
                >
                    <div className="flex items-center gap-2">
                        {selectedUsers.length > 0 ? (
                            <div className="flex -space-x-2">
                                {selectedUsers.slice(0, 3).map((user) => (
                                    <Avatar key={user.id} className="h-6 w-6 border-2 border-background">
                                        <AvatarImage src={user.avatar} alt={user.name} />
                                        <AvatarFallback>
                                            {user.name.substring(0, 2).toUpperCase()}
                                        </AvatarFallback>
                                    </Avatar>
                                ))}
                                {selectedUsers.length > 3 && (
                                    <div className="flex h-6 w-6 items-center justify-center rounded-full border-2 border-background bg-muted text-xs">
                                        +{selectedUsers.length - 3}
                                    </div>
                                )}
                            </div>
                        ) : (
                            <span className="text-muted-foreground">Select user{maxUsers > 1 ? 's' : ''}</span>
                        )}
                    </div>
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0">
                <Command>
                    <CommandInput placeholder="Search users..." />
                    <CommandEmpty>No users found.</CommandEmpty>
                    <CommandGroup>
                        {users.map((user) => (
                            <CommandItem
                                key={user.id}
                                value={user.id}
                                onSelect={handleSelect}
                            >
                                <div className="flex items-center gap-2">
                                    <Avatar className="h-6 w-6">
                                        <AvatarImage src={user.avatar} alt={user.name} />
                                        <AvatarFallback>
                                            {user.name.substring(0, 2).toUpperCase()}
                                        </AvatarFallback>
                                    </Avatar>
                                    <span>{user.name}</span>
                                </div>
                                {selectedUsers.some(u => u.id === user.id) && (
                                    <Check className="ml-auto h-4 w-4" />
                                )}
                            </CommandItem>
                        ))}
                    </CommandGroup>
                </Command>
            </PopoverContent>
        </Popover>
    )
} 