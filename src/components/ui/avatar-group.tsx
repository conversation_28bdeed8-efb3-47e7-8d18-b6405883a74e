import * as React from "react"
import { cn } from "@/lib/utils"

interface AvatarGroupProps extends React.HTMLAttributes<HTMLDivElement> {
    maxCount?: number;
}

const AvatarGroup = React.forwardRef<HTMLDivElement, AvatarGroupProps>(
    ({ className, maxCount = 3, children, ...props }, ref) => {
        const childrenArray = React.Children.toArray(children);
        const totalChildren = childrenArray.length;
        const displayCount = Math.min(totalChildren, maxCount);
        const remainingCount = totalChildren - displayCount;

        return (
            <div
                ref={ref}
                className={cn("flex -space-x-2", className)}
                {...props}
            >
                {childrenArray.slice(0, displayCount).map((child, index) => (
                    <div key={index} className="relative">
                        {child}
                    </div>
                ))}
                {remainingCount > 0 && (
                    <div className="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-full bg-muted">
                        <span className="flex h-full w-full items-center justify-center text-xs text-muted-foreground">
                            +{remainingCount}
                        </span>
                    </div>
                )}
            </div>
        )
    }
)
AvatarGroup.displayName = "AvatarGroup"

export { AvatarGroup } 