import { useState, useEffect } from 'react';
import { ExternalLink, Image as ImageIcon, Video, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LinkPreviewProps {
    url: string;
    className?: string;
}

interface PreviewData {
    title?: string;
    description?: string;
    image?: string;
    type: 'image' | 'video' | 'link';
}

export function LinkPreview({ url, className }: LinkPreviewProps) {
    const [previewData, setPreviewData] = useState<PreviewData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const detectType = (url: string): PreviewData['type'] => {
            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
            const videoExtensions = ['.mp4', '.webm', '.ogg'];

            const extension = url.toLowerCase().split('.').pop();
            if (extension && imageExtensions.includes(`.${extension}`)) return 'image';
            if (extension && videoExtensions.includes(`.${extension}`)) return 'video';
            return 'link';
        };

        const fetchPreview = async () => {
            try {
                setLoading(true);
                setError(null);

                const type = detectType(url);

                if (type === 'image' || type === 'video') {
                    setPreviewData({ type, image: url });
                } else {
                    // For regular links, you might want to implement a server-side
                    // link preview service here. For now, we'll just show the URL.
                    setPreviewData({
                        type: 'link',
                        title: new URL(url).hostname
                    });
                }
            } catch (err) {
                setError('Failed to load preview');
                console.error('Link preview error:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchPreview();
    }, [url]);

    if (loading) {
        return (
            <div className="flex items-center gap-2 text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Loading preview...</span>
            </div>
        );
    }

    if (error) {
        return (
            <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 underline"
            >
                {url}
                <ExternalLink className="h-3 w-3" />
            </a>
        );
    }

    if (!previewData) return null;

    return (
        <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className={cn(
                "block max-w-sm rounded-lg border bg-card p-2 shadow-sm hover:bg-accent transition-colors",
                className
            )}
        >
            {previewData.type === 'image' && (
                <div className="relative aspect-video">
                    <img
                        src={previewData.image}
                        alt=""
                        className="rounded object-cover w-full h-full"
                    />
                </div>
            )}
            {previewData.type === 'video' && (
                <div className="relative aspect-video bg-muted rounded flex items-center justify-center">
                    <Video className="h-8 w-8 text-muted-foreground" />
                </div>
            )}
            <div className="flex items-center gap-2 mt-2 text-sm">
                {previewData.type === 'link' && (
                    <ImageIcon className="h-4 w-4 text-muted-foreground" />
                )}
                <span className="flex-1 truncate">
                    {previewData.title || new URL(url).hostname}
                </span>
                <ExternalLink className="h-3 w-3 text-muted-foreground flex-shrink-0" />
            </div>
        </a>
    );
} 