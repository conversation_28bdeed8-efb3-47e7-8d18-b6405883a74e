import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

interface AuthState {
    user: User | null
    session: any | null
    isLoading: boolean
    error: Error | null

    // Actions
    setUser: (user: User | null) => void
    setSession: (session: any | null) => void
    setError: (error: Error | null) => void
    setLoading: (isLoading: boolean) => void
    signOut: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
    persist(
        (set) => ({
            user: null,
            session: null,
            isLoading: true,
            error: null,

            setUser: (user) => set({ user }),
            setSession: (session) => set({ session }),
            setError: (error) => set({ error }),
            setLoading: (isLoading) => set({ isLoading }),

            signOut: async () => {
                try {
                    await supabase.auth.signOut()
                    set({ user: null, session: null })
                } catch (error) {
                    set({ error: error as Error })
                }
            },
        }),
        {
            name: 'auth-storage',
            // Only persist essential auth data
            partialize: (state) => ({
                user: state.user,
                session: state.session,
            }),
        }
    )
) 