import { create } from 'zustand'
import { supabase } from '@/lib/supabase'

export interface UserProfile {
    id: string
    username: string
    full_name: string
    avatar_url: string
    updated_at: string
    email?: string
    // Add other user profile fields as needed
}

interface CachedData<T> {
    data: T
    timestamp: number
}

interface UserState {
    profiles: Map<string, CachedData<UserProfile>>
    isLoading: boolean
    error: Error | null
    cacheDuration: number // in milliseconds

    // Actions
    getProfile: (userId: string) => Promise<UserProfile | null>
    setProfile: (userId: string, profile: UserProfile) => void
    invalidateCache: (userId: string) => void
    clearCache: () => void
    setError: (error: Error | null) => void
}

const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes default cache duration

export const useUserStore = create<UserState>()((set, get) => ({
    profiles: new Map(),
    isLoading: false,
    error: null,
    cacheDuration: CACHE_DURATION,

    getProfile: async (userId: string) => {
        const state = get()
        const cachedProfile = state.profiles.get(userId)

        // Check if we have a valid cached profile
        if (cachedProfile) {
            const isExpired = Date.now() - cachedProfile.timestamp > state.cacheDuration
            if (!isExpired) {
                return cachedProfile.data
            }
        }

        // Fetch fresh data if cache is expired or doesn't exist
        try {
            set({ isLoading: true, error: null })

            const { data, error } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', userId)
                .single()

            if (error) throw error

            const profile = data as UserProfile
            state.setProfile(userId, profile)
            return profile
        } catch (error) {
            set({ error: error as Error })
            return null
        } finally {
            set({ isLoading: false })
        }
    },

    setProfile: (userId: string, profile: UserProfile) => {
        set((state) => ({
            profiles: new Map(state.profiles).set(userId, {
                data: profile,
                timestamp: Date.now(),
            }),
        }))
    },

    invalidateCache: (userId: string) => {
        set((state) => {
            const newProfiles = new Map(state.profiles)
            newProfiles.delete(userId)
            return { profiles: newProfiles }
        })
    },

    clearCache: () => {
        set({ profiles: new Map() })
    },

    setError: (error: Error | null) => set({ error }),
}))