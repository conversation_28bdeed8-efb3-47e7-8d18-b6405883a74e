import { BrowserRouter as Router } from 'react-router-dom';
import { AuthProvider } from '@/features/auth';
import { Toaster } from '@/components/ui/toaster';
import { AppRoutes } from '@/app/routes/index';
import { SessionContextProvider } from '@supabase/auth-helpers-react';
import { supabase } from './lib/supabase';
import { SidebarProvider } from '@/app/context/SidebarContext';

const App = () => {
  return (
    <SessionContextProvider supabaseClient={supabase}>
      <SidebarProvider>
        <Router>
          <AuthProvider>
            <AppRoutes />
            <Toaster />
          </AuthProvider>
        </Router>
      </SidebarProvider>
    </SessionContextProvider>
  );
};

export default App;