import { vi } from 'vitest';
import '@testing-library/jest-dom';

// Create a proper ResizeObserver mock
class MockResizeObserver {
    callback: ResizeObserverCallback;

    constructor(callback: ResizeObserverCallback) {
        this.callback = callback;
    }

    observe(target: Element) {
        // Simulate the ResizeObserverEntry
        this.callback([{
            target,
            contentRect: target.getBoundingClientRect(),
            borderBoxSize: [{ blockSize: 0, inlineSize: 0 }],
            contentBoxSize: [{ blockSize: 0, inlineSize: 0 }],
            devicePixelContentBoxSize: [{ blockSize: 0, inlineSize: 0 }]
        }], this);
    }

    unobserve() { }
    disconnect() { }
}

global.ResizeObserver = MockResizeObserver as unknown as typeof ResizeObserver;

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
    })),
});

// Mock window.innerWidth
Object.defineProperty(window, 'innerWidth', {
    writable: true,
    value: 1024,
});

// Mock IntersectionObserver
// @ts-expect-error - This is a mock implementation
window.IntersectionObserver = class IntersectionObserver {
    // ... existing code ...
}; 