name: CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    env:
      VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
      VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run tests with coverage
        run: npm run test:coverage


  deploy:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Set Version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          echo "version=$VERSION" >> $GITHUB_ENV
 

      - name: Pull Vercel Environment Information
        env:
          VITE_APP_VERSION: ${{ env.version }}
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }} --scope=${{ secrets.VERCEL_ORG_ID }}
        
      - name: Build Project
        env:
          VITE_APP_VERSION: ${{ env.version }}
          VITE_BUGSNAG_API_KEY: ${{ secrets.VITE_BUGSNAG_API_KEY }}
          VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}
        run: vercel build --prod

      - name: Deploy to Vercel
        id: deploy
        env:
          VITE_APP_VERSION: ${{ env.version }}        
        run: |
          DEPLOYMENT_URL=$(vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }} --scope=${{ secrets.VERCEL_ORG_ID }})
          echo "deployment_url=$DEPLOYMENT_URL" >> $GITHUB_OUTPUT
          echo "Deployed to: $DEPLOYMENT_URL"

      - name: Upload Source Maps to Bugsnag
        env:
          VITE_BUGSNAG_API_KEY: ${{ secrets.VITE_BUGSNAG_API_KEY }}
          VITE_APP_VERSION: ${{ env.version }}
        run: |
          echo "Uploading source maps for version: $VERSION"
          npx bugsnag-source-maps upload-browser \
            --api-key ${{ secrets.VITE_BUGSNAG_API_KEY }} \
            --app-version "$VITE_APP_VERSION" \
            --directory .vercel/output/static \
            --base-url ${{ steps.deploy.outputs.deployment_url }} \
            --overwrite

      - name: Deployment Summary
        run: |
          echo "### Deployment Complete! 🚀" >> $GITHUB_STEP_SUMMARY
          echo "* **Version:** ${{ env.version }}" >> $GITHUB_STEP_SUMMARY
          echo "* **URL:** ${{ steps.deploy.outputs.deployment_url }}" >> $GITHUB_STEP_SUMMARY