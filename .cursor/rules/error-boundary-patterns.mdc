# Error Boundary Component Patterns

## Standalone Component with Error Boundary

For standalone components that need error isolation, create a wrapper component with this pattern:

```typescript
import React from 'react';
import { ComponentName } from './ComponentName';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { errorHandler, ErrorType } from '@/utils/errorHandler';

interface ComponentNameProps {
  // Component props...
}

export const ComponentNameWithErrorBoundary = (props: ComponentNameProps) => {
  return (
    <ErrorBoundary
      componentName="ComponentName"
      onError={(error, info) => {
        errorHandler.captureError(error, ErrorType.UNKNOWN, {
          component: 'ComponentName',
          action: 'render',
          context: { 
            // Include relevant props as context
            id: props.id,
            componentStack: info.componentStack 
          }
        });
      }}
      fallbackRender={({ error, resetErrorBoundary }) => (
        // Component-specific fallback UI
        <div className="[appropriate-styling-to-maintain-layout]">
          <p className="text-sm text-muted-foreground">
            Error displaying component
          </p>
          <button 
            onClick={resetErrorBoundary}
            className="[appropriate-button-styling]"
          >
            Retry
          </button>
        </div>
      )}
    >
      <ComponentName {...props} />
    </ErrorBoundary>
  );
};
```

## Higher-Order Component Pattern

For reusable components that need error boundaries, use the HOC pattern:

```typescript
import { ComponentName } from './ComponentName';
import { withErrorBoundary } from '@/components/ui/error-boundary';
import { errorHandler, ErrorType } from '@/utils/errorHandler';

// Export the original component for testing and direct use
export { ComponentName };

// Export a wrapped version as the default export
export default withErrorBoundary(ComponentName, {
  componentName: 'ComponentName',
  onError: (error, info) => {
    errorHandler.captureError(error, ErrorType.UNKNOWN, {
      component: 'ComponentName',
      action: 'render',
      context: { componentStack: info.componentStack }
    });
  },
  fallbackRender: ({ error, resetErrorBoundary }) => (
    // Component-specific fallback UI
  )
});
```

## Modal Component with Error Boundary

For modal components, ensure the fallback UI maintains the modal context:

```typescript
fallbackRender={({ error, resetErrorBoundary }) => (
  <Dialog open={true} onOpenChange={() => onClose()}>
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Error</DialogTitle>
      </DialogHeader>
      <div className="p-4 text-center">
        <p className="text-sm text-muted-foreground mb-4">
          An error occurred while loading the content.
        </p>
        <div className="flex justify-center space-x-4">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button onClick={resetErrorBoundary}>
            Try Again
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
)}
```

## Inline Error Boundaries

For protecting specific parts of a component while maintaining the rest:

```typescript
// Main component continues to work even if part of it fails
<div>
  <h2>Component Title</h2>
  
  {/* Critical part that might fail */}
  <ErrorBoundary fallback={<div>Error loading data</div>}>
    <CriticalDataComponent />
  </ErrorBoundary>
  
  {/* Non-critical part that will still work */}
  <div>Other component content</div>
</div>
```

## Interactive Component Patterns

For components with user interactions, ensure error boundaries reset on key prop changes:

```typescript
<ErrorBoundary
  componentName="InteractiveComponent"
  resetKeys={[itemId, version]} // Reset error state when these props change
  onReset={() => {
    // Clean up any state needed on reset
    setLocalState(initialState);
  }}
>
  <InteractiveComponent itemId={itemId} version={version} />
</ErrorBoundary>
``` 