---
description: Best practices for integrating Supabase for backend services and React App
globs: **/*.{ts,tsx,js,jsx}
---
- Integrate Supabase for backend services, using its client-side library for data fetching and authentication to simplify backend development.
- Prioritize security and performance optimizations in Supabase integrations, such as enabling row-level security and using appropriate indexing to ensure data integrity and query efficiency.
- Use Supabase's auth helpers for authentication
- Implement proper error handling for API calls
- Use real-time subscriptions for live updates
- Secure your database with row-level security