---
description: Best practices for React component development and optimization
globs: **/*.{ts,tsx,js,jsx}
---
- Use `React.memo()` for component memoization when appropriate to prevent unnecessary re-renders.
- Implement `useCallback` for memoizing functions passed as props to optimize performance.
- Use `useMemo` for expensive computations to cache results and improve performance.
- Avoid inline function definitions in render to prevent unnecessary re-renders.
- Prefer composition over inheritance for better code reusability and maintainability.
- Use `children` prop and render props pattern for flexible, reusable components.
- Implement `React.lazy()` and `Suspense` for code splitting to improve initial load time.
- Use `refs` sparingly and mainly for DOM access to maintain React's declarative nature.
- Prefer controlled components over uncontrolled components for better state management.
- Implement error boundaries to catch and handle errors gracefully, improving user experience.
- Use cleanup functions in `useEffect` to prevent memory leaks and ensure proper resource management.