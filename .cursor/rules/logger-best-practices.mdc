# Logger Utility Best Practices

## Core Guidelines

1. Use `logger` utility for all logging, never use raw `console.log`, `console.warn`, or `console.error`
2. Choose the appropriate log level based on the importance and purpose of the message
3. Include contextual data with log messages when relevant
4. Keep in mind that only errors are shown in production by default

## Logger Utility Usage

```typescript
import { logger } from '@/utils/logger';

// Debug: For detailed information useful during development
logger.debug('Loading data for component', { userId, componentState });

// Info: For general information about application flow
logger.info('User completed onboarding process');

// Warning: For potential issues that don't prevent operation
logger.warn('API request rate limit approaching', { requestCount });

// Error: For errors that should always be logged (even in production)
logger.error('Failed to load critical data', error);
```

## When to Use Each Level

- **Debug**: Detailed information useful for debugging
  - Component initialization
  - Data transformations
  - State updates
  - API request/response details
  
- **Info**: General application flow information
  - Feature usage
  - Significant user actions
  - Application lifecycle events
  
- **Warning**: Potential issues that don't prevent operation
  - Performance concerns
  - Deprecated feature usage
  - Non-critical failures with fallback
  
- **Error**: Issues that prevent expected functionality
  - API failures
  - Component errors
  - Data validation errors
  - Authentication/authorization failures

## Context Parameters

Always include relevant context with your log messages:

```typescript
// Good: Provides context about what failed
logger.error('Failed to update card', { 
  cardId, 
  updateData, 
  error: error.message 
});

// Bad: No context about what failed
logger.error('Update failed', error);
```

## Best Practices

1. **Be selective**: Don't log unnecessarily; focus on what's helpful
2. **Be clear**: Use descriptive messages that identify the source and nature of the event
3. **Be consistent**: Use similar logging patterns for similar events
4. **Be contextual**: Include relevant data without exposing sensitive information
5. **Use proper levels**: Match the log level to the importance of the message 