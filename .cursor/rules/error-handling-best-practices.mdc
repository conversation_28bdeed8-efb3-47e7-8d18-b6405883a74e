# Error Handling Best Practices

## Core Guidelines

1. Use `errorHandler` for all error handling, never raw `console.error` or `try/catch` without proper handling
2. Wrap critical components with Error Boundaries
3. Add contextual information to all error reports
4. Provide user-friendly fallback UIs
5. Include retry mechanisms where appropriate

## Error Handler Usage

When handling API errors:
```typescript
try {
  // API call
} catch (error) {
  errorHandler.handleApiError(error, {
    component: 'ModuleName',
    action: 'actionName',
    context: { /* relevant data */ }
  });
  throw new Error('User-friendly message');
}
```

## Error Boundary Pattern

Component error boundaries should follow this pattern:
```typescript
// For component wrappers
<ErrorBoundary
  componentName="ComponentName"
  onError={(error, info) => {
    errorHandler.captureError(error, ErrorType.UNKNOWN, {
      component: 'ComponentName',
      action: 'render',
      context: { componentStack: info.componentStack }
    });
  }}
  fallbackRender={({ error, resetErrorBoundary }) => (
    // Custom fallback UI with retry
  )}
>
  <Component {...props} />
</ErrorBoundary>

// For HOC pattern
export { OriginalComponent };
export default withErrorBoundary(OriginalComponent, {
  componentName: 'ComponentName'
  // configuration
});
```

## Critical Components

Always add error boundaries to:
- Layout components (Columns, Cards, Modals)
- Data components (Lists, Tables)
- Interactive components (Forms, Drag and Drop)
- Asynchronous components

## Further Reference

See `docs/error-handling-best-practices.md` for full documentation 