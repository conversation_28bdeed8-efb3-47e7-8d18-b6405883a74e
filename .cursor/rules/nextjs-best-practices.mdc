---
description: Best practices for Next.js applications and routing
globs: **/*.{ts,tsx,js,jsx}
---
- Minimize the use of 'use client', 'useEffect', and 'useState'; favor React Server Components (RSC) for better performance and SEO.
- Wrap client components in `Suspense` with a fallback to improve user experience during loading.
- Use dynamic loading for non-critical components to optimize initial load time.
- Optimize images by using WebP format, including size data, and implementing lazy loading for better performance.